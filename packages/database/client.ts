/**
 * Prisma client instance for the database package
 * 
 * This is the core Prisma client that should be used by all applications in the monorepo.
 * It provides a singleton instance with proper connection management.
 */

import { PrismaClient } from "@prisma/client";

/**
 * Global Prisma client instance management for Next.js development environment.
 * Maintains a singleton instance to prevent multiple database connections during HMR.
 */
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient;
  prismaInstanceCount: number; // Tracks client instances for connection pool monitoring
};

// Check for Edge Runtime
const isEdgeRuntime =
  typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge";

// Add environment check
const isServer = typeof window === "undefined";
const isSafeRuntime = isServer && !isEdgeRuntime;

// Check if we're in a build-time context
// Skip initialization in Edge Runtime and during build processes
const isBuildTime = isEdgeRuntime || process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL;

if (!isServer) {
  throw new Error(
    "PrismaClient is only meant to be used server-side, not in the browser. " +
      "Please use API routes or Server Components to handle database operations.",
  );
}

if (!globalForPrisma.prismaInstanceCount) {
  globalForPrisma.prismaInstanceCount = 0;
}

/**
 * Core Prisma client instance with RLS (Row Level Security) configuration.
 * Uses DATABASE_URL_ROOT_USER connection string for least-privilege access
 */
function createPrismaClient(): PrismaClient {
  // Skip Prisma initialization in Edge Runtime only
  if (isEdgeRuntime || isBuildTime) {
    console.warn(
      "Prisma client initialization skipped in Edge Runtime",
    );
    // Return a mock PrismaClient with the necessary methods for Edge Runtime
    return {
      $extends: () => ({ query: {} }),
      $use: () => {},
      $on: () => {},
      $transaction: async (queries: any[]) => queries[queries.length - 1],
      $executeRaw: async () => null,
      user: { findUnique: async () => null, findFirst: async () => null, create: async () => null, update: async () => null, delete: async () => null },
      member: { findFirstOrThrow: async () => null },
      auditLog: { create: async () => null },
      // Add other models as needed
    } as unknown as PrismaClient;
  }

  if (!isServer) {
    throw new Error("PrismaClient cannot be instantiated in the browser");
  }

  // Resolve connection base URL with a graceful fallback
  const connectionBaseUrl =
    process.env.DATABASE_URL_ROOT_USER ?? process.env.DATABASE_URL;
  if (!connectionBaseUrl) {
    throw new Error(
      "Missing DATABASE_URL_ROOT_USER or DATABASE_URL environment variables",
    );
  }

  const newClient = new PrismaClient({
    datasources: {
      db: {
        url: `${connectionBaseUrl}?connection_limit=${
          process.env.DB_CONNECTION_LIMIT || "15"
        }&pgbouncer=true&prepare=false`,
      },
    },
    log: [
      // { emit: "event", level: "query" },
      // { emit: "event", level: "error" },
      { emit: "stdout", level: "error" },
      { emit: "stdout", level: "warn" }, // Add warnings for connection issues
    ],
    // metrics: {
    //   enabled: true,
    //   pushGateway: { url: process.env.PROMETHEUS_PUSHGATEWAY_URL },
    // },
  });

  // Add error handling hooks
  newClient.$on("error", (e: { message: string; [key: string]: unknown }) => {
    console.error("[Prisma Error]", {
      message: e.message,
      target: (e as any).target,
      timestamp: new Date().toISOString(),
    });
  });

  // --------------------------------------------------------------------
  // SQL TIMING HOOKS (Phase-5)
  // --------------------------------------------------------------------
  // This listener captures every query, measures its duration and sends a
  // structured log record through the unified logging system. Slow queries
  // (default >200 ms, configurable via SLOW_QUERY_THRESHOLD_MS env var) are
  // upgraded to `warn` level so they stand out in dashboards.

  const SLOW_QUERY_THRESHOLD = Number(
    process.env.SLOW_QUERY_THRESHOLD_MS ?? "200",
  );

  // Middleware-based timing for queries (compatible with Prisma v6)
  const anyClient = newClient as unknown as { $use?: Function };
  if (typeof anyClient.$use === "function") {
    anyClient.$use(
      async (
        params: { model?: string; action: string },
        next: (p: any) => Promise<any>,
      ) => {
        const start = Date.now();
        const result = await next(params);
        const duration = Date.now() - start;

        const payload = {
          model: params.model,
          action: params.action,
          duration_ms: duration,
        };

        if (duration > SLOW_QUERY_THRESHOLD) {
          console.warn("⚠️  Slow Prisma operation detected", payload);
        } else if (process.env.NODE_ENV === "development") {
          console.debug("Prisma operation executed", payload);
        }

        return result;
      },
    );
  }

  globalForPrisma.prismaInstanceCount++;
  return newClient;
}

// Lazy-loaded Prisma client
export const prisma = globalForPrisma.prisma || createPrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Export Prisma types
export type { Prisma } from "@prisma/client";

/**
 * Utility function to safely handle JSON fields in Prisma
 * Prevents the 'set' property issue when updating JSON fields
 */
export function safeJsonField<T>(data: T): T {
  if (!data) return data;

  // Check if data has a 'set' property that needs unwrapping
  if (data && typeof data === "object" && "set" in data) {
    return (data as any).set;
  }

  return data;
}

/**
 * Prisma middleware to automatically unwrap 'set' properties in JSON fields
 */
type MiddlewareCapableClient = {
  $use: (
    middleware: (
      params: any,
      next: (params: any) => Promise<any>,
    ) => Promise<any>,
  ) => void;
};

export function addJsonFieldMiddleware<T extends MiddlewareCapableClient>(
  prismaClient: T,
): T {
  prismaClient.$use(async (params, next) => {
    // Only process update and create operations
    if (params.action === "update" || params.action === "create") {
      // Process JSON fields that might contain 'set' property
      const data = params.args.data;
      if (data && typeof data === "object") {
        // Look for potential JSON fields
        Object.keys(data).forEach((key) => {
          if (
            data[key] &&
            typeof data[key] === "object" &&
            "set" in data[key]
          ) {
            // Unwrap the 'set' property
            data[key] = data[key].set;
          }
        });
      }
    }
    return next(params);
  });

  return prismaClient;
}

export function getPrismaInstanceCount(): number {
  return globalForPrisma.prismaInstanceCount;
}

/**
 * Connection pool monitoring endpoint. Critical for:
 * - Identifying connection leaks
 * - Tuning pool sizes
 * - Diagnosing query bottlenecks
 */
export async function getConnectionPoolMetrics() {
  // Metrics disabled. Return static placeholders.
  return {
    connections: {
      active: 0,
      idle: 0,
      total: 0,
      max: parseInt(process.env.DB_CONNECTION_LIMIT || "15"),
    },
    queries: { active: 0, waiting: 0, total: 0 },
    leaks: { potential: 0, max_age_ms: 0 },
    health: { connection_utilization: "0%", avg_wait_time_ms: 0 },
  };
}

// Add environment validation (silent default)
if (!process.env.DB_CONNECTION_LIMIT) {
  // Defaulting to 15 connections when DB_CONNECTION_LIMIT is unset
}

// Import additional dependencies needed for the extended functions
import { detailedDiff, updatedDiff } from "deep-object-diff";
import { createId } from "@paralleldrive/cuid2";

// Utility functions to replace lodash dependencies
function camelCase(str: string): string {
  return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
    return index === 0 ? word.toLowerCase() : word.toUpperCase();
  }).replace(/\s+/g, '');
}

function isEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (typeof a !== typeof b) return false;
  
  if (typeof a === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    for (const key of keysA) {
      if (!keysB.includes(key)) return false;
      if (!isEqual(a[key], b[key])) return false;
    }
    
    return true;
  }
  
  return false;
}

function pick(obj: any, keys: string[]): any {
  const result: any = {};
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  return result;
}

// Base PrismaClient reference for use inside extensions where the extended type may hide core methods
const basePrisma = prisma as unknown as PrismaClient;

/**
 * Creates a guarded Prisma client instance with security context:
 * - Sets organization/user context for RLS policies
 * - Verifies membership/roles when accessing organization resources
 * - All queries run in transaction to ensure context consistency
 */
export function guardedPrisma({
  orgId,
  userId,
  roles,
  checkMembership = true,
}: {
  orgId?: string;
  userId?: string;
  roles?: string[];
  checkMembership?: boolean;
}) {
  // In Edge Runtime, return the mock client
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return prisma as PrismaClient;
  }

  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }: { args: any; query: any }) {
          // Transaction sequence:
          // 1. Set organization context for RLS
          // 2. Set user context for RLS
          // 3. Validate membership (if required)
          // 4. Execute original query
          const results = await basePrisma.$transaction([
            ...(orgId
              ? [
                  basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
                ]
              : []),
            ...(userId
              ? [
                  basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`,
                ]
              : []),
            ...(orgId && userId && roles && checkMembership
              ? [
                  basePrisma.member.findFirstOrThrow({
                    where: {
                      user_id: userId,
                      organization_id: orgId,
                      role_id: { in: roles },
                    },
                  }),
                ]
              : orgId && userId && checkMembership
                ? [
                    basePrisma.member.findFirstOrThrow({
                      where: { user_id: userId, organization_id: orgId },
                    }),
                  ]
                : []),
            query(args),
          ]);
          return results[results.length - 1]; // Return query result
        },
      },
    },
  }) as PrismaClient;
}

export function tenantGuardedPrisma(orgId: string) {
  // In Edge Runtime, return the mock client
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return prisma as PrismaClient;
  }

  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }: { args: any; query: any }) {
          const [, , result] = await basePrisma.$transaction([
            basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
            basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
}

export function userGuardedPrisma(userId: string) {
  // In Edge Runtime, return the mock client
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return prisma as PrismaClient;
  }

  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }: { args: any; query: any }) {
          const [, , result] = await basePrisma.$transaction([
            basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`,
            basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
}

/**
 * Bypass RLS client for administrative operations. Use with extreme caution.
 * Required for:
 * - Audit log writes
 * - System-level maintenance
 * - Emergency data repairs
 */
export const bypassedPrisma =
  typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge"
    ? prisma // In Edge Runtime, just return the mock prisma client
    : prisma.$extends({
        query: {
          $allModels: {
            async $allOperations({ args, query }: { args: any; query: any }) {
              const [, result] = await basePrisma.$transaction([
                basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
                query(args),
              ]);
              return result;
            },
          },
        },
      });

export function forOrganization(
  currentOrganizationId: string,
  currentUserId?: string,
) {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async $allOperations({ args, query }: { args: any; query: any }) {
            const [, , result] = await basePrisma.$transaction([
              basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${currentOrganizationId}, TRUE)`,
              basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${currentUserId}, TRUE)`,
              query(args),
            ]);
            return result;
          },
        },
      },
    });
}

export function forUser(currentUserId: string) {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async $allOperations({ args, query }: { args: any; query: any }) {
            const [, result] = await basePrisma.$transaction([
              basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${currentUserId}, TRUE)`,
              query(args),
            ]);
            return result;
          },
        },
      },
    });
}

export function bypassRLS() {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async $allOperations({ args, query }: { args: any; query: any }) {
            const [, result] = await basePrisma.$transaction([
              basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
              query(args),
            ]);
            return result;
          },
        },
      },
    });
}

// Types for audit logging
type AuditMetadata = {
  userId: string;
  actionDate: Date;
  action: string;
  data: any;
};

type OtherDataAuditLog = {
  id: string;
  data: any;
  message: string;
  event_name: string;
  date: Date;
};

type OtherData = {
  audit_log?: OtherDataAuditLog[];
};

// Simple HttpError implementation
class HttpError extends Error {
  status: number;
  isOperational: boolean;

  constructor(status: number, message: string, isOperational: boolean) {
    super(message);
    this.status = status;
    this.isOperational = isOperational;
  }
}

/**
 * Core audit logging system with three-phase workflow:
 * 1. Capture before-state
 * 2. Execute operation
 * 3. Record delta in audit_log table
 */
export function withAuditLogger({
  orgId,
  userId,
  message,
}: {
  orgId: string;
  userId: string;
  message?: string;
}) {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async update({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            // Phase 1: Pre-operation checks
            const payload = args.data;
            const where = args.where as { id: string };
            if (!where.id) {
              throw Error(
                "update operation with audit logger requires the 'where' clause to have the 'id' property",
              );
            }

            // Phase 2: State capture
            const existing = await (
              basePrisma[camelCase(model) as keyof PrismaClient] as any
            ).findFirst({ where });
            if (!existing) {
              throw new HttpError(404, "data to be updated is not found", true);
            }

            // Phase 3: Delta calculation and audit record
            const comparator = Object.keys(payload ?? {}) as string[];
            const skipUpdate = isEqual(
              pick(existing, comparator),
              pick(payload, comparator),
            );
            if (skipUpdate) return existing;

            const result = await query(args);

            const diff = getDiff(existing, result);
            await (basePrisma as PrismaClient).auditLog.create({
              data: {
                ref_id: where.id,
                event: "update",
                entity: model,
                message,
                new_value: diff.updated,
                old_value: { ...diff.updatedOriginal, ...diff.deleted },
                user_id: userId,
                organization_id: orgId,
              },
            });

            return result;
          },
          async create({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const result = await query(args);
            await (basePrisma as PrismaClient).auditLog.create({
              data: {
                ref_id: (result as any).id,
                event: "create",
                entity: model,
                message,
                new_value: result,
                user_id: userId,
                organization_id: orgId,
              },
            });
            return result;
          },
          async delete({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const result = await query(args);
            await (basePrisma as PrismaClient).auditLog.create({
              data: {
                ref_id: (result as any).id,
                event: "delete",
                entity: model,
                message,
                old_value: result,
                user_id: userId,
                organization_id: orgId,
              },
            });
            return result;
          },
        },
      },
    });
}

function getDiff(oldObj: object, newObj: object) {
  const results = detailedDiff(oldObj, newObj);
  results.deleted = updatedDiff(results.deleted, oldObj);
  const updatedOriginal = updatedDiff(results.updated, oldObj);
  return { ...results, updatedOriginal };
}

type AuditLogCreatePayload = {
  entity: string;
  ref_id: string;
  organization_id: string;
  message?: string | null;
  event?: string | null;
  user_id?: string | null;
  new_value?: unknown;
  old_value?: unknown;
  data?: unknown[] | null;
};

export async function auditLog(payload: AuditLogCreatePayload) {
  // Skip in Edge Runtime
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    console.warn("Audit logging skipped in Edge Runtime");
    return;
  }

  // Use the base prisma client directly to avoid extension conflicts
  await (basePrisma as PrismaClient).auditLog.create({
    data: {
      entity: payload.entity,
      ref_id: payload.ref_id,
      organization_id: payload.organization_id,
      message: payload.message ?? null,
      event: payload.event ?? null,
      user_id: payload.user_id ?? null,
      new_value: (payload.new_value as any) ?? undefined,
      old_value: (payload.old_value as any) ?? undefined,
      data: (payload.data as any) ?? undefined,
    },
  });
}

/**
 * Alternative audit strategy: Stores audit trail in JSONB field within records.
 * Used when:
 * - Need per-record audit history
 * - Faster access to audit trail
 * - Non-critical audit requirements
 */
export function otherDataAuditLog(
  optional_message?: string,
  optional_event_name?: string,
) {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async update({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const payload = JSON.parse(JSON.stringify(args.data));
            const where = args.where as { id: string };
            if (!where.id) {
              throw Error(
                "update operation with audit logger requires the 'where' clause to have the 'id' property",
              );
            }
            const existing = await (
              basePrisma[camelCase(model) as keyof PrismaClient] as any
            ).findUnique({ where });
            if (!existing) {
              throw new HttpError(404, "data to be updated is not found", true);
            }
            const new_audit_log: OtherDataAuditLog = {
              id: createId(),
              data: payload,
              message: optional_message || "",
              event_name: (optional_event_name || `${model} update`)
                .toLocaleLowerCase()
                .replace(/ /g, "_"),
              date: new Date(),
            };
            const existing_other_data = existing.other_data as OtherData;
            let auditLog: OtherDataAuditLog[] = [];
            try {
              if (
                existing_other_data?.audit_log &&
                Array.isArray(existing_other_data?.audit_log)
              ) {
                auditLog = [...existing_other_data?.audit_log, new_audit_log];
              } else {
                // other_data.audit_log is null, so insert the first one.
                auditLog.push(new_audit_log);
              }
              const result = await query(args);
              await (
                basePrisma[camelCase(model) as keyof PrismaClient] as any
              ).update({
                where: {
                  id: where.id,
                },
                data: {
                  other_data:
                    existing_other_data === undefined
                      ? { audit_log: auditLog }
                      : { ...existing_other_data, audit_log: auditLog },
                },
              });
              return result;
            } catch (error) {
              throw Error(JSON.stringify({ status: "error", message: error }));
            }
          },
        },
      },
    });
}

/**
 * Audit metadata extension tracks:
 * - User ID of modifier
 * - Exact modification timestamp
 * - Action type (create/update)
 * - Full data snapshot
 */
export function withAuditMetadataLogging({ userId }: { userId: string }) {
  // In Edge Runtime, return a simple extension function that just passes through
  if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
    return (client: any) => client;
  }

  return (client: PrismaClient) =>
    client.$extends({
      query: {
        $allModels: {
          async update({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const where = args.where as { id: string };
            if (!where.id) {
              throw Error(
                "update operation with this extension requires the 'where' clause to have the 'id' property",
              );
            }
            const existing = await (
              basePrisma[camelCase(model) as keyof PrismaClient] as any
            ).findFirst({ where });
            if (!existing) {
              throw new HttpError(404, "data to be updated is not found", true);
            }
            const audit_metadata = existing.audit_metadata as AuditMetadata[];
            const new_audit_metadata = {
              userId: userId,
              actionDate: new Date(),
              action: "update",
              data: args.data,
            } as AuditMetadata;
            audit_metadata.push(new_audit_metadata);
            await query(args);
            const updatedResult = await (
              basePrisma[camelCase(model) as keyof PrismaClient] as any
            ).update({
              where: {
                id: where.id,
              },
              data: {
                audit_metadata: audit_metadata,
              },
            });
            return updatedResult;
          },
          async createMany({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const result = (await query(args)) as any;
            return result;
          },
          async upsert({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const result = (await query(args)) as any;
            return result;
          },
          async create({
            model,
            args,
            query,
          }: {
            model: any;
            args: any;
            query: any;
          }) {
            const audit_metadata = {
              userId: userId,
              actionDate: new Date(),
              action: "create",
              data: args.data,
            };
            const result = (await query(args)) as any;
            const updatedResult = await (
              basePrisma[camelCase(model) as keyof PrismaClient] as any
            ).update({
              where: {
                id: result?.id,
              },
              data: {
                audit_metadata: [audit_metadata],
              },
            });
            return updatedResult;
          },
        },
      },
    });
}
