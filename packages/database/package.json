{"name": "@askinfosec/database", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"dev": "echo 'Database package - no dev server needed'", "db:generate": "prisma generate --schema=prisma/schema.prisma", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "build": "pnpm db:generate && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "echo 'No linting configured for database package'"}, "dependencies": {"@prisma/client": "6.15.0", "deep-object-diff": "^1.1.9", "@paralleldrive/cuid2": "^2.2.2"}, "devDependencies": {"@askinfosec/typescript-config": "workspace:*", "@types/node": "^20.11.0", "prisma": "6.15.0", "tsx": "^4.7.0", "typescript": "^5.5.0"}}