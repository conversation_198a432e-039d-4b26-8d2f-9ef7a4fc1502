import { eq, and, isNull, desc, asc, count } from 'drizzle-orm';
import { BaseRepository, ITenantRepository, EntityNotFoundError, DuplicateEntityError, SessionContext, IdGenerationConfig } from '../lib/base-repository.js';
import { organizations, members, users, type Organization, type NewOrganization } from '../schemas/schema.js';

export interface OrganizationCreateInput {
  companyName: string;
  mainOwnerId: string;
  settings?: any;
  openaiSettings?: any;
  companyInformation?: any;
  modules?: string[];
}

export interface OrganizationUpdateInput {
  companyName?: string;
  settings?: any;
  openaiSettings?: any;
  companyInformation?: any;
  modules?: string[];
  stripeCustomerId?: string;
}

export interface OrganizationWithOwner extends Organization {
  mainOwner: {
    id: string;
    name: string | null;
    email: string | null;
  };
}

export class OrganizationRepository 
  extends BaseRepository 
  implements ITenantRepository<Organization, OrganizationCreateInput, OrganizationUpdateInput> 
{
  protected readonly idConfig: IdGenerationConfig = {
    usePrefix: false,
    prefix: 'org'
  };
  
  async findById(id: string, context?: SessionContext): Promise<Organization | null> {
    if (context) {
      return this.executeQueryWithSession(async (tx) => {
        const result = await tx
          .select()
          .from(organizations)
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, context, 'findById');
    } else {
      return this.executeQuery(async () => {
        const result = await this.db
          .select()
          .from(organizations)
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, 'findById');
    }
  }

  async findByIdWithOwner(id: string, context?: SessionContext): Promise<OrganizationWithOwner | null> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const result = await this.db
          .select({
            id: organizations.id,
            companyName: organizations.companyName,
            mainOwnerId: organizations.mainOwnerId,
            settings: organizations.settings,
            openaiSettings: organizations.openaiSettings,
            currentUsage: organizations.currentUsage,
            createdAt: organizations.createdAt,
            updatedAt: organizations.updatedAt,
            deletedAt: organizations.deletedAt,
            otherData: organizations.otherData,
            companyInformation: organizations.companyInformation,
            auditTrail: organizations.auditTrail,
            modules: organizations.modules,
            stripeCustomerId: organizations.stripeCustomerId,
            mainOwner: {
              id: users.id,
              name: users.name,
              email: users.email,
            },
          })
          .from(organizations)
          .innerJoin(users, eq(organizations.mainOwnerId, users.id))
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, context, 'findByIdWithOwner');
    } else {
      return this.executeQuery(async () => {
        const result = await this.db
          .select({
            id: organizations.id,
            companyName: organizations.companyName,
            mainOwnerId: organizations.mainOwnerId,
            settings: organizations.settings,
            openaiSettings: organizations.openaiSettings,
            currentUsage: organizations.currentUsage,
            createdAt: organizations.createdAt,
            updatedAt: organizations.updatedAt,
            deletedAt: organizations.deletedAt,
            otherData: organizations.otherData,
            companyInformation: organizations.companyInformation,
            auditTrail: organizations.auditTrail,
            modules: organizations.modules,
            stripeCustomerId: organizations.stripeCustomerId,
            mainOwner: {
              id: users.id,
              name: users.name,
              email: users.email,
            },
          })
          .from(organizations)
          .innerJoin(users, eq(organizations.mainOwnerId, users.id))
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, 'findByIdWithOwner');
    }
  }

  async findByCompanyName(companyName: string, context?: SessionContext): Promise<Organization | null> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const result = await this.db
          .select()
          .from(organizations)
          .where(and(
            eq(organizations.companyName, companyName),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, context, 'findByCompanyName');
    } else {
      return this.executeQuery(async () => {
        const result = await this.db
          .select()
          .from(organizations)
          .where(and(
            eq(organizations.companyName, companyName),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result[0] || null;
      }, 'findByCompanyName');
    }
  }

  async findByUserId(userId: string, context?: SessionContext): Promise<Organization[]> {
    // Create proper session context for this operation
    // We need to ensure the userId in the session matches the userId we're querying for
    const sessionContext: SessionContext = {
      userId: context?.userId || userId, // Use context userId if available, otherwise use parameter
      organizationId: context?.organizationId, // Preserve organization context if available
      bypassRls: context?.bypassRls || false,
    };
    
    return this.executeQueryWithSession(async (db) => {
      return await db
        .select({
          id: organizations.id,
          companyName: organizations.companyName,
          mainOwnerId: organizations.mainOwnerId,
          settings: organizations.settings,
          openaiSettings: organizations.openaiSettings,
          currentUsage: organizations.currentUsage,
          createdAt: organizations.createdAt,
          updatedAt: organizations.updatedAt,
          deletedAt: organizations.deletedAt,
          otherData: organizations.otherData,
          companyInformation: organizations.companyInformation,
          auditTrail: organizations.auditTrail,
          modules: organizations.modules,
          stripeCustomerId: organizations.stripeCustomerId,
        })
        .from(organizations)
        // IMPORTANT: We don't need to join members table here because RLS has a policy that only allows users to see organizations they are members of
        // .innerJoin(members, eq(organizations.id, members.organizationId))
        // .where(and(
        //   eq(members.userId, userId),
        //   isNull(organizations.deletedAt),
        //   isNull(members.deletedAt)
        // ))
        .orderBy(desc(organizations.createdAt));
    }, sessionContext, 'findByUserId');
  }

  async create(input: OrganizationCreateInput, context?: SessionContext): Promise<Organization> {
    if (context) {
      return this.executeQueryWithSession(async (tx) => {
        // Check if company name already exists
        const existing = await this.findByCompanyName(input.companyName, context);
        if (existing) {
          throw new DuplicateEntityError('Organization', 'companyName', input.companyName);
        }

        const newOrg: NewOrganization = {
          id: this.generateId(),
          companyName: input.companyName,
          mainOwnerId: input.mainOwnerId,
          settings: input.settings,
          openaiSettings: input.openaiSettings,
          companyInformation: input.companyInformation,
          modules: input.modules || [],
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
          otherData: null,
          auditTrail: null,
          stripeCustomerId: null,
        };

        const result = await tx
          .insert(organizations)
          .values(newOrg)
          .returning();

        return result[0];
      }, context, 'create');
    } else {
      return this.executeQuery(async () => {
        // Check if company name already exists
        const existing = await this.findByCompanyName(input.companyName);
        if (existing) {
          throw new DuplicateEntityError('Organization', 'companyName', input.companyName);
        }

        const newOrg: NewOrganization = {
          id: this.generateId(),
          companyName: input.companyName,
          mainOwnerId: input.mainOwnerId,
          settings: input.settings,
          openaiSettings: input.openaiSettings,
          companyInformation: input.companyInformation,
          modules: input.modules || [],
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
          otherData: null,
          auditTrail: null,
          stripeCustomerId: null,
        };

        const result = await this.db
          .insert(organizations)
          .values(newOrg)
          .returning();

        return result[0];
      }, 'create');
    }
  }

  async update(id: string, input: OrganizationUpdateInput, context?: SessionContext): Promise<Organization> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const existing = await this.findById(id, context);
        if (!existing) {
          throw new EntityNotFoundError('Organization', id);
        }

        const updateData: Partial<Organization> = {
          ...input,
          updatedAt: new Date(),
        };

        const result = await this.db
          .update(organizations)
          .set(updateData)
          .where(eq(organizations.id, id))
          .returning();

        return result[0];
      }, context, 'update');
    } else {
      return this.executeQuery(async () => {
        const existing = await this.findById(id);
        if (!existing) {
          throw new EntityNotFoundError('Organization', id);
        }

        const updateData: Partial<Organization> = {
          ...input,
          updatedAt: new Date(),
        };

        const result = await this.db
          .update(organizations)
          .set(updateData)
          .where(eq(organizations.id, id))
          .returning();

        return result[0];
      }, 'update');
    }
  }

  async delete(id: string, context?: SessionContext): Promise<void> {
    if (context) {
      await this.executeQueryWithSession(async () => {
        const existing = await this.findById(id, context);
        if (!existing) {
          throw new EntityNotFoundError('Organization', id);
        }

        await this.db
          .update(organizations)
          .set({ deletedAt: new Date() })
          .where(eq(organizations.id, id));
      }, context, 'delete');
    } else {
      await this.executeQuery(async () => {
        const existing = await this.findById(id);
        if (!existing) {
          throw new EntityNotFoundError('Organization', id);
        }

        await this.db
          .update(organizations)
          .set({ deletedAt: new Date() })
          .where(eq(organizations.id, id));
      }, 'delete');
    }
  }

  async findMany(options?: {
    limit?: number;
    offset?: number;
    orderDirection?: 'asc' | 'desc';
  }, context?: SessionContext): Promise<Organization[]> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const { limit, offset, orderDirection = 'desc' } = options || {};
        
        const baseQuery = this.db.select().from(organizations).where(isNull(organizations.deletedAt));
        
        if (orderDirection === 'asc') {
          return await baseQuery.orderBy(asc(organizations.createdAt)).limit(limit || 50).offset(offset || 0);
        } else {
          return await baseQuery.orderBy(desc(organizations.createdAt)).limit(limit || 50).offset(offset || 0);
        }
      }, context, 'findMany');
    } else {
      return this.executeQuery(async () => {
        const { limit, offset, orderDirection = 'desc' } = options || {};
        
        const baseQuery = this.db.select().from(organizations).where(isNull(organizations.deletedAt));
        
        if (orderDirection === 'asc') {
          return await baseQuery.orderBy(asc(organizations.createdAt)).limit(limit || 50).offset(offset || 0);
        } else {
          return await baseQuery.orderBy(desc(organizations.createdAt)).limit(limit || 50).offset(offset || 0);
        }
      }, 'findMany');
    }
  }

  async count(context?: SessionContext): Promise<number> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const result = await this.db
          .select({ count: count() })
          .from(organizations)
          .where(isNull(organizations.deletedAt));
        
        return Number(result[0]?.count || 0);
      }, context, 'count');
    } else {
      return this.executeQuery(async () => {
        const result = await this.db
          .select({ count: count() })
          .from(organizations)
          .where(isNull(organizations.deletedAt));
        
        return Number(result[0]?.count || 0);
      }, 'count');
    }
  }

  async exists(id: string, context?: SessionContext): Promise<boolean> {
    if (context) {
      return this.executeQueryWithSession(async () => {
        const result = await this.db
          .select({ id: organizations.id })
          .from(organizations)
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result.length > 0;
      }, context, 'exists');
    } else {
      return this.executeQuery(async () => {
        const result = await this.db
          .select({ id: organizations.id })
          .from(organizations)
          .where(and(
            eq(organizations.id, id),
            isNull(organizations.deletedAt)
          ))
          .limit(1);
        
        return result.length > 0;
      }, 'exists');
    }
  }

  async findByOrganization(
    organizationId: string,
    options?: {
      limit?: number;
      offset?: number;
    },
    context?: SessionContext
  ): Promise<Organization[]> {
    // For organization-scoped queries, we need the organization context
    const sessionContext: SessionContext = context || { organizationId, bypassRls: false };
    
    return this.executeQueryWithSession(async () => {
      const { limit, offset } = options || {};
      
      const baseQuery = this.db
        .select()
        .from(organizations)
        .where(and(
          eq(organizations.id, organizationId),
          isNull(organizations.deletedAt)
        ));

      if (limit) {
        return await baseQuery.limit(limit).offset(offset || 0);
      } else {
        return await baseQuery.offset(offset || 0);
      }
    }, sessionContext, 'findByOrganization');
  }

  async countByOrganization(organizationId: string, context?: SessionContext): Promise<number> {
    // For organization-scoped queries, we need the organization context
    const sessionContext: SessionContext = context || { organizationId, bypassRls: false };
    
    return this.executeQueryWithSession(async () => {
      const result = await this.db
        .select({ count: count() })
        .from(organizations)
        .where(and(
          eq(organizations.id, organizationId),
          isNull(organizations.deletedAt)
        ));
      
      return Number(result[0]?.count || 0);
    }, sessionContext, 'countByOrganization');
  }

}
