import { eq, and, asc, desc, inArray } from 'drizzle-orm';
import { BaseRepository, SessionContext, IdGenerationConfig } from '../lib/base-repository.js';
import { 
  organizations, 
  organizationModuleOverrides,
  systemModules,
  moduleDependencies,
  products,
  productModules,
  type SystemModule,
  type OrganizationModuleOverride
} from '../schemas/schema.js';

export interface OrganizationModuleAccess {
  id: string;
  name: string;
  displayName: string;
  description: string | null;
  isEnabled: boolean;
  source: 'subscription' | 'override' | 'dependency' | 'core';
  sourceDetails: {
    productId?: string;
    productName?: string;
    subscriptionId?: string;
    originalSource?: string;
    originalProductId?: string;
    requiredBy?: string[];
    isCore?: boolean;
  };
  isOverride: boolean;
  overrideReason: string | null;
}

export interface OrganizationModuleOverrideInput {
  moduleId: string;
  isEnabled: boolean;
  reason?: string;
}

export class OrganizationModuleRepository extends BaseRepository {
  protected readonly idConfig: IdGenerationConfig = {
    usePrefix: false,
    prefix: 'clm'
  };
  
  async getOrganizationModuleAccess(organizationId: string, context?: SessionContext): Promise<OrganizationModuleAccess[]> {
    return this.executeQueryWithSession(async (db) => {
      // Get organization details
      const organization = await db
        .select()
        .from(organizations)
        .where(eq(organizations.id, organizationId))
        .limit(1);

      if (!organization[0]) {
        throw new Error('Organization not found');
      }

      const org = organization[0];

      // Get all modules from organization.modules field (subscription-based)
      const subscriptionModules = org.modules || [];
      
      // Get organization module overrides
      const overridesResult = await db
        .select({
          moduleId: organizationModuleOverrides.moduleId,
          name: systemModules.name,
          isEnabled: organizationModuleOverrides.isEnabled,
          reason: organizationModuleOverrides.reason,
        })
        .from(organizationModuleOverrides)
        .innerJoin(systemModules, eq(organizationModuleOverrides.moduleId, systemModules.id))
        .where(eq(organizationModuleOverrides.organizationId, organizationId));

      // Create a map of overrides
      const overridesMap = new Map<string, { isEnabled: boolean; reason: string | null; moduleId: string }>();
      overridesResult.forEach(override => {
        overridesMap.set(override.name, {
          isEnabled: override.isEnabled ?? true,
          reason: override.reason,
          moduleId: override.moduleId,
        });
      });

      // Get all modules that the organization has access to
      const allModuleIds = new Set<string>();
      
      // Add subscription modules
      subscriptionModules.forEach(moduleName => {
        allModuleIds.add(moduleName);
      });

      // Add override modules
      overridesMap.forEach((_, name) => {
        allModuleIds.add(name);
      });

      // Get module details for all modules
      const modulesResult = await db
        .select()
        .from(systemModules)
        .where(inArray(systemModules.name, Array.from(allModuleIds)));

      // Create a map of modules by name
      const modulesMap = new Map<string, SystemModule>();
      modulesResult.forEach(module => {
        modulesMap.set(module.name, module);
      });

      // Build the result
      const result: OrganizationModuleAccess[] = [];

      // Process subscription modules
      subscriptionModules.forEach(moduleName => {
        const module = modulesMap.get(moduleName);
        if (!module) return;

        const override = overridesMap.get(module.name);
        
        if (override) {
          // Override exists - use override values
          result.push({
            id: module.id,
            name: module.name,
            displayName: module.displayName,
            description: module.description,
            isEnabled: override.isEnabled,
            source: 'override',
            sourceDetails: {
              originalSource: 'subscription',
              originalProductId: this.getProductIdForModule(moduleName), // Mock for now
            },
            isOverride: true,
            overrideReason: override.reason,
          });
        } else {
          // No override - use subscription values
          result.push({
            id: module.id,
            name: module.name,
            displayName: module.displayName,
            description: module.description,
            isEnabled: true,
            source: 'subscription',
            sourceDetails: {
              productId: this.getProductIdForModule(moduleName), // Mock for now
              productName: this.getProductNameForModule(moduleName), // Mock for now
              subscriptionId: `sub-${organizationId}`, // Mock for now
            },
            isOverride: false,
            overrideReason: null,
          });
        }
      });

      // Process override-only modules (not in subscription)
      overridesMap.forEach((override, name) => {
        const module = modulesResult.find(m => m.name === name);
        if (!module) return;

        // Check if this module is already processed (from subscription)
        const alreadyProcessed = result.some(r => r.id === module.id);
        if (alreadyProcessed) return;

        result.push({
          id: module.id,
          name: module.name,
          displayName: module.displayName,
          description: module.description,
          isEnabled: override.isEnabled,
          source: 'override',
          sourceDetails: {
            originalSource: undefined,
            originalProductId: undefined,
          },
          isOverride: true,
          overrideReason: override.reason,
        });
      });

      // Add core modules (always available)
      const coreModules = modulesResult.filter(m => m.isCore);
      coreModules.forEach(module => {
        const alreadyProcessed = result.some(r => r.id === module.id);
        if (alreadyProcessed) return;

        result.push({
          id: module.id,
          name: module.name,
          displayName: module.displayName,
          description: module.description,
          isEnabled: true,
          source: 'core',
          sourceDetails: {
            isCore: true,
          },
          isOverride: false,
          overrideReason: null,
        });
      });

      // Add dependencies for all enabled modules
      const enabledModuleIds = result
        .filter(r => r.isEnabled)
        .map(r => r.id);

      for (const moduleId of enabledModuleIds) {
        const dependencies = await this.getModuleDependencies(moduleId);
        dependencies.forEach(dependency => {
          const alreadyProcessed = result.some(r => r.id === dependency.id);
          if (alreadyProcessed) return;

          result.push({
            id: dependency.id,
            name: dependency.name,
            displayName: dependency.displayName,
            description: dependency.description,
            isEnabled: true,
            source: 'dependency',
            sourceDetails: {
              requiredBy: [moduleId],
            },
            isOverride: false,
            overrideReason: null,
          });
        });
      }

      return result.sort((a, b) => a.displayName.localeCompare(b.displayName));
    }, context || { organizationId, userId: undefined, bypassRls: false }, 'getOrganizationModuleAccess');
  }

  async addModuleOverride(
    organizationId: string, 
    input: OrganizationModuleOverrideInput
  ): Promise<OrganizationModuleOverride> {
    return this.executeQuery(async () => {
      // Verify module exists
      const module = await this.db
        .select()
        .from(systemModules)
        .where(eq(systemModules.id, input.moduleId))
        .limit(1);

      if (!module[0]) {
        throw new Error('Module not found');
      }

      // Check if override already exists
      const existingOverride = await this.db
        .select()
        .from(organizationModuleOverrides)
        .where(and(
          eq(organizationModuleOverrides.organizationId, organizationId),
          eq(organizationModuleOverrides.moduleId, input.moduleId)
        ))
        .limit(1);

      if (existingOverride[0]) {
        // Update existing override
        const result = await this.db
          .update(organizationModuleOverrides)
          .set({
            isEnabled: input.isEnabled,
            reason: input.reason,
            updatedAt: new Date(),
          })
          .where(and(
            eq(organizationModuleOverrides.organizationId, organizationId),
            eq(organizationModuleOverrides.moduleId, input.moduleId)
          ))
          .returning();

        return result[0];
      } else {
        // Create new override
        const result = await this.db
          .insert(organizationModuleOverrides)
          .values({
            id: this.generateId(),
            organizationId,
            moduleId: input.moduleId,
            isEnabled: input.isEnabled,
            reason: input.reason,
          })
          .returning();

        return result[0];
      }
    }, 'addModuleOverride');
  }

  async removeModuleOverride(organizationId: string, moduleId: string): Promise<boolean> {
    return this.executeQuery(async () => {
      const result = await this.db
        .delete(organizationModuleOverrides)
        .where(and(
          eq(organizationModuleOverrides.organizationId, organizationId),
          eq(organizationModuleOverrides.moduleId, moduleId)
        ))
        .returning({ id: organizationModuleOverrides.id });

      return result.length > 0;
    }, 'removeModuleOverride');
  }

  async getOrganizationOverrides(organizationId: string): Promise<OrganizationModuleOverride[]> {
    return this.executeQuery(async () => {
      const result = await this.db
        .select()
        .from(organizationModuleOverrides)
        .where(eq(organizationModuleOverrides.organizationId, organizationId))
        .orderBy(asc(organizationModuleOverrides.createdAt));

      return result;
    }, 'getOrganizationOverrides');
  }

  async syncOrganizationModules(organizationId: string): Promise<void> {
    return this.executeQuery(async () => {
      // Get current module access
      const moduleAccess = await this.getOrganizationModuleAccess(organizationId);
      
      // Extract enabled module names
      const enabledModuleNames = moduleAccess
        .filter(m => m.isEnabled)
        .map(m => m.name);

      // Update organization.modules field
      await this.db
        .update(organizations)
        .set({
          modules: enabledModuleNames,
          updatedAt: new Date(),
        })
        .where(eq(organizations.id, organizationId));
    }, 'syncOrganizationModules');
  }

  /**
   * Retrieves all modules associated with a specific Stripe product ID.
   *
   * This method queries the database to find all system modules that are linked
   * to a product through the product_modules junction table. It returns the modules
   * formatted as OrganizationModuleAccess objects with subscription source information.
   *
   * @param stripeProductId - The Stripe product ID to find associated modules for
   * @returns Promise<OrganizationModuleAccess[]> - Array of modules associated with the product
   * 
   * @example
   * ```typescript
   * const modules = await repository.getModulesByInternalProductId('prod_1234567890');
   * // Returns modules like: Security Awareness Training, Risk Assessment, etc.
   * ```
   * 
   * @note This method does not resolve module dependencies or handle duplicates.
   *       Callers should handle deduplication and dependency resolution as needed.
   */
  async getModulesByInternalProductId(stripeProductId: string): Promise<OrganizationModuleAccess[]> {
    return this.executeQuery(async () => {
      const result = await this.db
        .select()
        .from(productModules)
        .innerJoin(systemModules, eq(productModules.moduleId, systemModules.id))
        .innerJoin(products, eq(productModules.productId, products.id))
        .where(eq(products.stripeProductId, stripeProductId));
      return result.map(m => ({
        id: m.module.id,
        name: m.module.name,
        displayName: m.module.displayName,
        description: m.module.description,
        isEnabled: true,
        source: 'subscription',
        sourceDetails: {
          productId: stripeProductId,
          productName: m.product.name,
          subscriptionId: "not-available-and-not-needed", // TODO: retrieve from stripe subscription
        },
        isOverride: false,
        overrideReason: null,
      })) as OrganizationModuleAccess[];
    }, 'getModulesByInternalProductId');
  }

  private async getModuleDependencies(moduleId: string): Promise<SystemModule[]> {
    const result = await this.db
      .select({
        id: systemModules.id,
        name: systemModules.name,
        displayName: systemModules.displayName,
        description: systemModules.description,
        isCore: systemModules.isCore,
        createdAt: systemModules.createdAt,
        updatedAt: systemModules.updatedAt,
      })
      .from(moduleDependencies)
      .innerJoin(systemModules, eq(moduleDependencies.dependencyId, systemModules.id))
      .where(eq(moduleDependencies.moduleId, moduleId));

    return result;
  }

  // Mock methods for product mapping (in real implementation, this would query the database)
  private getProductIdForModule(moduleName: string): string {
    // Mock implementation - in reality, this would query product_modules table
    const moduleToProductMap: Record<string, string> = {
      'compliance': 'prod-compliance',
      'controls': 'prod-compliance',
      'documents': 'prod-essentials',
      'policies': 'prod-essentials',
    };
    return moduleToProductMap[moduleName] || 'prod-default';
  }

  private getProductNameForModule(moduleName: string): string {
    // Mock implementation - in reality, this would query products table
    const moduleToProductMap: Record<string, string> = {
      'compliance': 'Compliance Suite',
      'controls': 'Compliance Suite',
      'documents': 'Information Security Essentials',
      'policies': 'Information Security Essentials',
    };
    return moduleToProductMap[moduleName] || 'Default Product';
  }
}
