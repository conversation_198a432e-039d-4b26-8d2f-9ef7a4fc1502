name: Build (Web)

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "The environment to build for"
    outputs:
      image:
        value: ${{ jobs.Build.outputs.image }}

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  Build:
    name: Build
    environment: ${{ inputs.environment }}-web-build
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.build.outputs.image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ vars.AWS_REGION }}
          role-duration-seconds: 2400
          role-skip-session-tagging: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Create environment file from GitHub variables
        run: |
          echo "Creating .env file for ${{ inputs.environment }} environment"
          echo "# Environment: ${{ inputs.environment }}" > apps/web-frontend/.env
          echo "# Generated at build time: $(date)" >> apps/web-frontend/.env
          echo "${{ vars.BUILD_ENV_VARS }}" >> apps/web-frontend/.env
          echo "Environment file created:"
          cat apps/web-frontend/.env

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push to Amazon ECR
        id: build
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ vars.IMAGE_REPOSITORY }}
        run: |
          IMAGE_TAG=${{ inputs.environment }}.${{ github.sha }}
          IMAGE=$REGISTRY/$REPOSITORY:$IMAGE_TAG

          IMAGE_META="$( aws ecr batch-get-image --repository-name=$REPOSITORY --image-ids=imageTag=$IMAGE_TAG --query 'images[].imageId.imageTag' --output text )"
          if [[ $IMAGE_META == $IMAGE_TAG ]]; then
            echo "Image exists, skipping build"
            echo "image=$IMAGE" >> "$GITHUB_OUTPUT"
          else
            echo "Image does not exist, building and pushing"
            docker buildx build \
              --cache-from type=gha \
              --cache-to type=gha,mode=max \
              --tag $IMAGE \
              --tag $REGISTRY/$REPOSITORY:${{ inputs.environment }}.latest \
              --file apps/api/Dockerfile \
              --push .
            echo "image=$IMAGE" >> "$GITHUB_OUTPUT"
          fi