name: Manual Build and Deploy Workflow
run-name: Manual Trigger (${{ inputs.app }}) (${{ inputs.environment }})

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Deploy to"
        type: choice
        required: true
        options:
          - dev
      app:
        description: "Application to deploy"
        type: choice
        required: true
        options:
          - all
          - api
          - web

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  Build-API:
    if: |
      (inputs.environment != 'prod' || github.ref == 'refs/heads/main') &&
      (inputs.app == 'all' || inputs.app == 'api')
    uses: ./.github/workflows/build-api.yml
    with:
      environment: ${{ inputs.environment }}
    secrets: inherit

  Build-Web:
    if: |
      (inputs.environment != 'prod' || github.ref == 'refs/heads/main') &&
      (inputs.app == 'all' || inputs.app == 'web')
    uses: ./.github/workflows/build-web.yml
    with:
      environment: ${{ inputs.environment }}
    secrets: inherit

  # Deploy-API:
  #   needs: Build-API
  #   if: |
  #     (inputs.environment != 'prod' || github.ref == 'refs/heads/main') &&
  #     (inputs.app == 'all' || inputs.app == 'api')
  #   uses: ./.github/workflows/deploy.yml
  #   with:
  #     image: ${{ needs.Build-API.outputs.image }}
  #     environment: ${{ inputs.environment }}
  #     app: api
  #   secrets: inherit
