{"name": "askinfosec", "version": "0.0.7", "private": true, "packageManager": "pnpm@10.14.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"clean-all": "rm -rf node_modules apps/*/node_modules packages/*/node_modules apps/*/.next apps/*/dist packages/*/dist .turbo .next && pnpm install", "dev": "pnpm --filter @askinfosec/database db:generate && turbo run dev --parallel", "dev:https": "pnpm --filter @askinfosec/database db:generate && turbo run dev:https --parallel", "dev:web": "pnpm --filter @askinfosec/database db:generate && turbo run dev --filter=@web", "dev:web:https": "pnpm --filter @askinfosec/database db:generate && turbo run dev:https --filter=@web", "dev:web-frontend": "turbo run dev --filter=@askinfosec/web-frontend", "dev:web-frontend:https": "turbo run dev:https --filter=@askinfosec/web-frontend", "build:web-frontend": "pnpm --filter @askinfosec/web-frontend build", "dev:admin": "pnpm --filter @askinfosec/database db:generate && turbo run dev --filter=@web-admin", "dev:admin:https": "pnpm --filter @askinfosec/database db:generate && turbo run dev:https --filter=@web-admin", "dev:api": "pnpm --filter @askinfosec/database db:generate && turbo run dev --filter=@askinfosec/api", "dev:api:https": "pnpm --filter @askinfosec/database db:generate && turbo run dev:https --filter=@askinfosec/api", "dev:trust-center": "turbo run dev --filter=@askinfosec/trust-center", "dev:trust-center:https": "turbo run dev:https --filter=@askinfosec/trust-center", "dev:ai-chat-widget": "turbo run dev --filter=@askinfosec/ai-chat-widget", "build": "pnpm --filter @askinfosec/database build && turbo run build --filter='!@web-admin' --filter='!@web'", "build:packages": "turbo run build --filter=./packages/*", "build:apps": "turbo run build --filter=./apps/*", "build:database": "pnpm --filter @askinfosec/database db:deploy && pnpm --filter @askinfosec/database db:generate && pnpm --filter @askinfosec/database build", "build:database-drizzle": "pnpm --filter @askinfosec/database-drizzle build", "build:web": "pnpm --filter @web build", "build:admin": "pnpm build:database && pnpm --filter @web-admin build", "build:api": "pnpm build:packages && pnpm --filter @askinfosec/api build", "build:trust-center": "pnpm --filter @askinfosec/trust-center build", "build:ai-chat-widget": "pnpm --filter @askinfosec/ai-chat-widget build", "build:vercel": "pnpm --filter @askinfosec/database db:generate && pnpm --filter @askinfosec/database build && pnpm --filter @askinfosec/shadcn-ui build && pnpm --filter @web prisma:generate && pnpm --filter @web build", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "format": "turbo run format", "check-format": "turbo run check-format", "check-lint": "turbo run check-lint", "check-types": "turbo run check-types", "check-all": "turbo run check-all", "copy:tinymce": "copyfiles -u 1 './node_modules/tinymce/**/*' './public/'", "start": "next start", "snyk-protect": "snyk-protect", "preview-email": "email dev", "release-no-cli": "release-it --ci", "release-candidate": "release-it --preRelease=rc", "release-patch": "release-it patch", "release-minor": "release-it minor", "release-major": "release-it major", "release": "release-it", "release-it": "release-it", "release-it-dry-run": "release-it --dry-run", "release-it-ci": "release-it --ci", "release-it-patch": "release-it patch", "release-it-minor": "release-it minor", "release-it-major": "release-it major", "release-it-rc": "release-it --preRelease=rc", "release-it-beta": "release-it --preRelease=beta", "release-it-alpha": "release-it --preRelease=alpha", "release-it-patch-rc": "release-it patch --preRelease=rc", "release-it-minor-rc": "release-it minor --preRelease=rc", "release-it-major-rc": "release-it major --preRelease=rc", "release-it-patch-beta": "release-it patch --preRelease=beta", "release-it-minor-beta": "release-it minor --preRelease=beta", "release-it-major-beta": "release-it major --preRelease=beta", "release-it-patch-alpha": "release-it patch --preRelease=alpha", "release-it-minor-alpha": "release-it minor --preRelease=alpha", "release-it-major-alpha": "release-it major --preRelease=alpha", "release-it-patch-ci": "release-it patch --ci", "release-it-minor-ci": "release-it minor --ci", "release-it-major-ci": "release-it major --ci", "release-it-patch-rc-ci": "release-it patch --preRelease=rc --ci", "release-it-minor-rc-ci": "release-it minor --preRelease=rc --ci", "release-it-major-rc-ci": "release-it major --preRelease=rc --ci", "release-it-patch-beta-ci": "release-it patch --preRelease=beta --ci", "release-it-minor-beta-ci": "release-it minor --preRelease=beta --ci", "release-it-major-beta-ci": "release-it major --preRelease=beta --ci", "release-it-patch-alpha-ci": "release-it patch --preRelease=alpha --ci", "release-it-minor-alpha-ci": "release-it minor --preRelease=alpha --ci", "release-it-major-alpha-ci": "release-it major --preRelease=alpha --ci"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} packages/database/prisma/seed.ts"}, "dependencies": {"@askinfosec/database": "workspace:*", "@nestjs/swagger": "^11.2.0", "@prisma/client": "6.14.0", "drizzle-orm": "^0.44.4", "kysely": "0.28.4", "pg": "8.16.0", "pgvector": "0.2.1", "stripe": "18.5.0"}, "devDependencies": {"@askinfosec/database": "workspace:*", "@askinfosec/typescript-config": "workspace:*", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.28.0", "@snyk/protect": "1.1297.1", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "22.16.3", "@types/pg": "^8.15.5", "@types/react": "19.1.0", "@types/react-dom": "19.1.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "cypress": "14.4.1", "drizzle-orm": "^0.44.5", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "^5.4.1", "husky": "9.1.7", "jsonwebtoken": "^9.0.2", "nanoid": "^5.1.5", "prettier": "3.5.3", "prisma-kysely": "1.8.0", "rimraf": "^6.0.1", "turbo": "^2.5.5"}, "snyk": true, "pnpm": {"overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.0", "d3-dsv": "^2.0.0", "date-fns": "^3.0.0", "prettier": "3.4.2", "drizzle-orm": "^0.44.5", "zod": "^4.1.5", "prisma": "6.15.0"}, "onlyBuiltDependencies": ["@prisma/client"], "ignoredBuiltDependencies": ["@heroui/shared-utils"]}}