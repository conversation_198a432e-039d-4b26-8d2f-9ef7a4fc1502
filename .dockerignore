# Dependency directories
**/node_modules
**/dist
**/.turbo

# Build artifacts
**/build
**/prod-build
**/dist
**/tsconfig.tsbuildinfo

# Logs and debug
**/logs
**/npm-debug.log
**/*.log

# Environment files
**/.env.local
**/.env.*.local
**/.npmrc

# Version control, CI, and config
**/.git
**/.gitignore
**/.prettierignore
**/.prettierrc.json
**/.github
**/.bolt
**/.changeset
**/.codacy
**/.dockerignore
**/.vscode
**/.cursor
**/.idea
**/.DS_Store
**/*.code-workspace
**/.dcignore
**/.husky
**/.release-it.json
**/.snyk
**/.vercelignore
**/ci
**/vercel.json

# Coverage and test output
**/coverage
**/.nyc_output
**/.codacy
**/cypress
**/cypress.config.ts

# Database migrations (run manually, not in container)
**/migrations
**/seeds
**/seed.ts

# Documentation
**/docs
**/*.md
**/*.txt
**/requirements

# Scripts and misc
**/*.sh
**/test-*.js
**/test_*.js
**/debug-*.js
**/scripts
**/docker-compose*.yml

# Local Development
**/certs