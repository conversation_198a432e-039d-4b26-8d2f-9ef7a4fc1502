// TODO: Make this library uniform. Use the same types for all the models. No a combination of prisma and kysely types.
import { CourseSection } from "@askinfosec/database";
import {
  Course,
  CourseAttachment,
  QuizSet,
  UserCourseSectionProgress,
  UserQuiz,
} from "../types/prisma-kysely";

export type CourseWithProgressWithCategory = Course & {
  category: CourseCategory | null;
  chapters: { id: string }[];
  progress: number | null;
};

export type CourseWithChaptersWithUserProgress = Course & {
  chapters: (CourseSection & {
    userProgress: UserCourseSectionProgress[] | null;
  })[];
  attachments: CourseAttachment[];
};

export interface CourseCategory {
  id: string;
  name: string;
}

export const CourseCategories: { id: string; name: string }[] = [
  { id: "SAT", name: "Security Awareness Training" },
];

export interface AnswerChoices {
  a: string;
  b: string;
  c: string;
  d: string;
}

export interface QuizQuestion {
  question: string;
  answerChoices: AnswerChoices;
  correctAnswer: keyof AnswerChoices;
}

export interface QuizState {
  currentQuestionIndex: number;
  answers: Record<number, keyof AnswerChoices>;
  isComplete: boolean;
}

export type QuizQuestionWithQuestionId = QuizQuestion & {
  questionId: string;
};

export type QuizSetWithQuestionsAndAnswers = QuizSet & {
  quizzes: QuizQuestionWithQuestionId[];
  userQuiz: UserQuiz | null;
};
