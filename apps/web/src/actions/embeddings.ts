"use server";
import { Document as PrismaDocument } from "@langchain/core/documents";
import { DocumentVector } from "@askinfosec/database";

import { getDocContents } from "@/services/server/docs";
import { tenantGuardedPrisma } from "@/lib/prisma";
import {
  initVectorStore,
  ID_COLUMN,
  CONTENT_COLUMN,
} from "@/lib/langchain/vector-store";
import { LogLevel, LogManager } from "@/observability/migration-shim";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { embedData } from "@/lib/langchain/genai";
import { nanoid } from "nanoid";
import { getServerSession } from "@/lib/get-session";
import { ServerActionResponse } from "./types";
import { revalidatePath } from "next/cache";

interface EmbedType {
  message: string;
}

export async function EmbedFile(
  org: string,
  file: string,
): Promise<ServerActionResponse<EmbedType>> {
  const session = await getServerSession();

  if (!session) {
    throw Error("User does not exists");
  }
  const userId = session.user.id;
  const logManager = new LogManager(LogLevel.info, org, userId);
  const db = tenantGuardedPrisma(org);

  try {
    await db.file.update({
      where: {
        id: file,
      },
      data: {
        trained_at: new Date(),
      },
    });

    const vectorStore = initVectorStore<DocumentVector>({
      tableName: "DocumentVector",
      vectorColumnName: "vector",
      columns: { id: ID_COLUMN, content: CONTENT_COLUMN },
      db,
      org,
    });
    const docs = await getDocContents(file, org);
    const res: PrismaDocument<Record<string, unknown>>[] = docs?.map((doc) => {
      return { pageContent: doc.content, metadata: doc };
    })!;
    const transactionId = nanoid();
    await embedData({
      transactionId,
      featureType: "document_upload",
      vectorStore,
      data: res,
      logManager,
      userId,
      org,
      reqId: nanoid(),
    });
    logger.info(
      logManager.eventLogsList,
      `ai:api:org:${org}:docs:${file}:updateDocsHandler`,
    );
    revalidatePath(`/${org}/docs`, "page");
    return { data: { message: "Files Embedded Successfully." } };
  } catch (e) {
    console.log("Error training document: ", e);
    return { error: { msg: "Error training document.", httpStatus: 400 } };
  }
}
