"use server";

import {
  CONTENT_COLUMN,
  ID_COLUMN,
  initVectorStore,
} from "@/lib/langchain/vector-store";
import { tenantGuardedPrisma } from "@/lib/prisma";
import { DocumentVector, Prisma } from "@askinfosec/database";
import { ServerActionResponse } from "./types";

export async function DocSimilaritySearch(
  org: string,
  prompt: string,
): Promise<ServerActionResponse<any>> {
  const db = tenantGuardedPrisma(org);
  const vectorStore = initVectorStore<DocumentVector>({
    tableName: "DocumentVector",
    vectorColumnName: "vector",
    columns: {
      id: ID_COLUMN,
      content: CONTENT_COLUMN,
      file_id: Symbol("file_id"),
    },
    db,
    org,
  });

  const searchData = await vectorStore.similaritySearch(prompt, 3);
  if (searchData && searchData.length >= 1) {
    const filtered = await Promise.all(
      searchData.map(async (d) => {
        const { metadata } = d;
        const { file_id } = metadata;
        const f = await db.file.findFirst({
          where: {
            id: file_id as string,
          },
        });
        return f!.name;
      }),
    );

    const data = JSON.parse(JSON.stringify(Array.from(new Set(filtered))));
    return { data: { result: data } };
  }
  return { data: [] };
}
