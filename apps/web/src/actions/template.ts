"use server";

import { Template } from "@/app/[lang]/(private)/[org]/questionnaires/upload/main-container";
import { prisma, tenantGuardedPrisma } from "@/lib/prisma";
// TODO: Import Kysely types and use them as DTOs for data returned to client components
// Example: import { Template as KyselyTemplate } from "@/types/prisma-kysely"

import {
  SubscriptionManager,
  businessPlan,
  freePlan,
} from "@/lib/subscription/subscription-manager";
import { getOrg } from "@/services/server/organization";
import { Questionnaire } from "@askinfosec/database";
import { getServerSession } from "@/lib/get-session";
import { revalidatePath } from "next/cache";
import { ServerActionErrors, ServerActionResponse } from "./types";
import { createId } from "@paralleldrive/cuid2";

/**
 * Creates a new template
 * @param template - template name
 * @param questions - template questions
 */
export async function createTemplate({
  org,
  template,
  questions,
}: {
  org: string;
  template: string;
  questions: string[];
}): Promise<ServerActionResponse<Template>> {
  const session = await getServerSession();

  if (!session) {
    throw Error("User does not exists");
  }
  const userId = session.user.id;
  const currentOrg = await getOrg({ userId, orgId: org });

  if (currentOrg?.current_user_role === "user")
    return { error: ServerActionErrors.Unauthorized };

  if (!currentOrg) return { error: ServerActionErrors.Unauthorized };
  const dbTenantGuardedPrisma = tenantGuardedPrisma(org);

  const queryTemplate = await dbTenantGuardedPrisma.template.findFirst({
    where: {
      name: template,
      organization_id: org,
    },
  });

  if (queryTemplate) {
    return { error: ServerActionErrors.Conflict };
  } else {
    const newTemplate = await dbTenantGuardedPrisma.template.create({
      data: {
        id: createId(),
        name: template,
        organization_id: org,
        created_by_id: userId,
      },
    });

    const allQuestions = await Promise.all(
      questions.map(async (q) => {
        return await dbTenantGuardedPrisma.templateQuestions.create({
          data: {
            question: q,
            template_id: newTemplate.id,
            updated_by_id: userId,
          },
        });
      }),
    );

    revalidatePath(`/${org}/settings`, "page");
    return {
      data: {
        id: newTemplate.id,
        name: template,
        organization_id: org,
        questions: allQuestions,
      },
    };
  }
}

interface uploadTemplateProps {
  org: string;
  questionnaire: {
    questionnaire_name: string;
    vendor?: string | undefined;
    due_date?: Date | undefined;
    assigned: string[];
    scope_id?: string | undefined;
    template?: string;
  };
}

/**
 * Uploads the questions based on the templates.
 * @param org - organization string
 * @param questionnaire - contains all the questionnaire table attributes
 */
export async function uploadTemplate({
  org,
  questionnaire,
}: uploadTemplateProps): Promise<ServerActionResponse<Questionnaire>> {
  const session = await getServerSession();

  if (!session) return { error: ServerActionErrors.Unauthorized };
  const userId = session.user.id;
  const currentOrg = await getOrg({ userId, orgId: org });

  if (currentOrg?.current_user_role === "user")
    return { error: ServerActionErrors.Unauthorized };

  if (!currentOrg) return { error: ServerActionErrors.Unauthorized };

  // Default to business plan if no subscription is found
  const subscriptionPlan =
    currentOrg.subscription?.name === "free" ? freePlan : businessPlan;
  const subscriptionManager = new SubscriptionManager(subscriptionPlan);

  const currentCount =
    currentOrg?.current_usage?.maxBulkQuestionUpload !== null &&
    currentOrg?.current_usage?.maxBulkQuestionUpload !== undefined
      ? currentOrg.current_usage.maxBulkQuestionUpload
      : 1000;
  const isBulkQuestionLimitReached = subscriptionManager.hasReachedLimit(
    "maxBulkQuestionUpload",
    currentCount,
  );
  if (isBulkQuestionLimitReached)
    return { error: ServerActionErrors.PaymentRequired };

  const db = tenantGuardedPrisma(org);

  const scope = await db.scope.findFirst({
    where: {
      id: questionnaire.scope_id,
    },
  });

  const template = await prisma.template.findFirst({
    where: {
      id: questionnaire.template,
    },
  });

  if (!template)
    return { error: ServerActionErrors.notFound("Template does not exists.") };
  const templateQuestions = await prisma.templateQuestions.findMany({
    where: {
      template_id: template.id,
    },
  });

  const q = await db.questionnaire.create({
    data: {
      name: questionnaire.questionnaire_name,
      vendor_id: questionnaire.vendor || null,
      due_date: questionnaire.due_date,
      assigned: questionnaire.assigned,
      organization_id: org,
      status: "in_review",
      created_by: userId,
      scope_id: questionnaire.scope_id,
      is_assessor: scope?.name === "Outgoing" ? true : false,
    },
  });

  await db.$transaction(async (tx) => {
    templateQuestions.map(async (question) => {
      await tx.question.create({
        data: {
          organization_id: org,
          status: "open",
          updated_at: new Date(),
          questionnaire_id: q.id,
          created_by_id: userId,
          created_at: new Date(),
          answer_detail: question.answer_detail,
          question: question.question,
          answer_yes_no_na: question.answer_yes_no_na,
          last_update_by_id: userId,
        },
      });
    });
  });

  return { data: { ...q } };
}

export async function updateTemplate({
  template,
}: {
  template: Template;
}): Promise<ServerActionResponse<Template>> {
  const session = await getServerSession();

  if (!session) return { error: ServerActionErrors.Unauthorized };

  // Template org here always has value.
  const db = tenantGuardedPrisma(template.organization_id as string);

  const queryTemplateName = await db.template.findFirst({
    where: {
      name: template.name,
      organization_id: template.organization_id as string,
      updated_at: new Date(),
    },
  });

  if (queryTemplateName) return { error: ServerActionErrors.Conflict };

  const templateToUpdate = await db.template.update({
    where: {
      id: template.id,
    },
    data: {
      name: template.name,
    },
  });

  const questionsToUpdate = template.questions.map((q) => {
    return {
      id: q.id,
      question: q.question,
      template_id: q.template_id,
      updated_at: new Date(),
    };
  });

  const allQuestionsUpdate = await Promise.all(
    questionsToUpdate.map(async (q) => {
      return await db.templateQuestions.update({
        where: {
          id: q.id,
        },
        data: {
          question: q.question,
          template_id: q.template_id,
          updated_at: new Date(),
        },
      });
    }),
  );

  revalidatePath(`/${template.organization_id}/settings`, "page");
  return {
    data: {
      id: templateToUpdate.id,
      name: template.name,
      organization_id: template.organization_id,
      questions: allQuestionsUpdate,
    },
  };
}

/**
 * Delete  template
 */
export async function deleteTemplate({
  org,
  id,
}: {
  org: string;
  id: string;
}): Promise<ServerActionResponse<string>> {
  const session = await getServerSession();

  if (!session) return { error: ServerActionErrors.Unauthorized };

  const db = tenantGuardedPrisma(org);
  const queryTemplate = await db.template.findFirst({
    where: {
      id: id,
    },
  });
  if (!queryTemplate)
    return { error: ServerActionErrors.notFound("Template does not exists") };
  await db.template.delete({
    where: {
      id: id,
    },
  });
  revalidatePath(`/${org}/settings`, "page");
  return { data: "Template deleted successfully" };
}
