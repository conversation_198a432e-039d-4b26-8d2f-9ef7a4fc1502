import { bypassedPrisma } from "@/lib/prisma";
import { MfaMethod } from "@/types/mfa-types";
import { PrismaClient } from "@askinfosec/database";

/**
 * Check if MFA is required for a user
 * @param userId User ID
 * @returns Object with MFA status and default method
 */
export async function checkMfaRequired(userId: string): Promise<{
  required: boolean;
  method: MfaMethod | null;
}> {
  try {
    // Get the user's MFA settings
    const prisma = bypassedPrisma as unknown as PrismaClient;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        mfaEnabled: true,
        defaultMfaMethod: true,
      },
    });

    if (!user) {
      return { required: false, method: null };
    }

    return {
      required: user.mfaEnabled,
      method: user.defaultMfaMethod as MfaMethod | null,
    };
  } catch (error) {
    console.error("Error checking MFA requirements:", error);
    // Default to not requiring MFA in case of error
    return { required: false, method: null };
  }
}

/**
 * Check if a user has MFA verified in their session
 * @param session User session
 * @returns Boolean indicating if MFA is verified
 */
export function isMfaVerified(session: any): boolean {
  return session?.user?.mfaVerified === true;
}

/**
 * Set MFA verification status in the session
 * @param session User session
 * @param verified Boolean indicating if MFA is verified
 */
export function setMfaVerified(session: any, verified: boolean): void {
  if (session?.user) {
    session.user.mfaVerified = verified;
  }
}
