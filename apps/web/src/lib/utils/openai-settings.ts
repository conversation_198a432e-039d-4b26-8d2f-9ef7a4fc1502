"use server";

import { PrismaClient } from "@askinfosec/database";
import { OpenAISettings } from "~/src/models/organization";
import logger from "@/lib/logger-new";

/**
 * Safely updates the openai_settings field for an organization
 *
 * This utility function ensures that the openai_settings JSON field is properly
 * updated without wrapping it in a 'set' property, which can cause issues with
 * the data structure.
 *
 * @param prisma - Prisma client instance
 * @param orgId - Organization ID
 * @param settings - New settings to store
 * @param options - Additional options
 * @returns True if the update was successful
 */
export async function safeUpdateOpenAISettings(
  prisma: PrismaClient,
  orgId: string,
  settings: Partial<OpenAISettings>,
  options?: {
    logActivity?: boolean;
    mergeWithExisting?: boolean;
  },
): Promise<boolean> {
  const { logActivity = true, mergeWithExisting = true } = options || {};

  try {
    if (logActivity) {
      logger.debug(`Updating openai_settings for organization ${orgId}`);
    }

    // Recursively unwrap 'set' property if it exists anywhere in the settings object
    const recursivelyUnwrapSet = (obj: any): any => {
      if (!obj || typeof obj !== "object") return obj;

      // Handle arrays
      if (Array.isArray(obj)) {
        return obj.map((item) => recursivelyUnwrapSet(item));
      }

      // Handle direct 'set' property at this level
      if ("set" in obj) {
        return recursivelyUnwrapSet(obj.set);
      }

      // Process all properties recursively
      const result: any = {};
      for (const key in obj) {
        result[key] = recursivelyUnwrapSet(obj[key]);
      }
      return result;
    };

    // Apply recursive unwrapping to settings
    const settingsToStore = recursivelyUnwrapSet(settings);

    if (mergeWithExisting) {
      // Get existing settings
      const org = await prisma.organization.findUnique({
        where: { id: orgId },
        select: { openai_settings: true },
      });

      if (org?.openai_settings) {
        // Unwrap existing settings recursively
        const existingSettings = recursivelyUnwrapSet(org.openai_settings);

        // Merge existing settings with new settings
        await prisma.organization.update({
          where: { id: orgId },
          data: {
            openai_settings: {
              ...existingSettings,
              ...settingsToStore,
            },
          },
        });
      } else {
        // No existing settings, just use the new settings
        await prisma.organization.update({
          where: { id: orgId },
          data: {
            openai_settings: settingsToStore,
          },
        });
      }
    } else {
      // Replace existing settings entirely
      await prisma.organization.update({
        where: { id: orgId },
        data: {
          openai_settings: settingsToStore,
        },
      });
    }

    return true;
  } catch (error) {
    logger.error(
      `Error updating openai_settings for organization ${orgId}: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
    return false;
  }
}

/**
 * Safely retrieves the openai_settings field for an organization,
 * handling the 'set' property if it exists
 *
 * @param settings - The openai_settings field from the database
 * @param logIssue - Whether to log if a 'set' property is found
 * @returns The unwrapped settings
 */
export async function unwrapOpenAISettings(
  settings: any,
  options?: {
    logIssue?: boolean;
    orgId?: string;
  },
): Promise<Partial<OpenAISettings>> {
  const { logIssue = true, orgId } = options || {};

  if (!settings) {
    return {};
  }

  // Check if settings has a 'set' property
  if (settings.set) {
    if (logIssue) {
      logger.warn(
        `Found 'set' property in openai_settings${orgId ? ` for organization ${orgId}` : ""}`,
      );
    }
    return settings.set as Partial<OpenAISettings>;
  }

  return settings as Partial<OpenAISettings>;
}
