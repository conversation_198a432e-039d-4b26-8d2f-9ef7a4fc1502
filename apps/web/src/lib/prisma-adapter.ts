import { bypassedPrisma } from "@/lib/prisma";
import { Adapter } from "next-auth/adapters";
import { PrismaClient } from "@askinfosec/database";

// Check for Edge Runtime
const isEdgeRuntime =
  typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Simplified mock adapter for Edge Runtime
 */
function createMockAdapter(): Adapter {
  return {
    createUser: async (data) => {
      // Create a valid AdapterUser object
      return {
        id: "mock-id",
        email: data.email || "<EMAIL>",
        emailVerified: null,
        name: data.name || null,
        image: data.image || null,
      };
    },
    getUser: async () => null,
    getUserByEmail: async () => null,
    getUserByAccount: async () => null,
    updateUser: async (data) => {
      // Create a valid AdapterUser object
      return {
        id: data.id,
        email: data.email || "<EMAIL>",
        emailVerified: data.emailVerified || null,
        name: data.name || null,
        image: data.image || null,
      };
    },
    deleteUser: async () => {},
    linkAccount: async (data) => data,
    unlinkAccount: async () => {},
    createSession: async (data) => {
      // Create a valid AdapterSession object
      return {
        sessionToken: data.sessionToken,
        userId: data.userId,
        expires: data.expires || new Date(Date.now() + 24 * 60 * 60 * 1000), // Default to 1 day from now
      };
    },
    getSessionAndUser: async () => null,
    updateSession: async (data) => {
      // Create a valid AdapterSession object
      return {
        sessionToken: data.sessionToken,
        userId: data.userId || "mock-user-id",
        expires: data.expires || new Date(Date.now() + 24 * 60 * 60 * 1000), // Default to 1 day from now
      };
    },
    deleteSession: async () => {},
    createVerificationToken: async (data) => data,
    useVerificationToken: async () => null,
  };
}

/**
 * Custom Prisma Adapter for NextAuth.js v5
 * @returns Adapter implementation
 */
export function PrismaAdapter(): Adapter {
  // In Edge Runtime, return a minimal adapter that doesn't do anything
  if (isEdgeRuntime) {
    console.warn("Using mock PrismaAdapter in Edge Runtime");
    return createMockAdapter();
  }

  // For regular Node.js runtime, use our custom adapter implementation
  return {
    // User operations
    createUser: async (data) => {
      const prismaUser = await prisma.user.create({ data });
      return {
        id: prismaUser.id,
        email: prismaUser.email || "",
        emailVerified: prismaUser.emailVerified,
        name: prismaUser.name || null,
        image: prismaUser.image || null,
      };
    },

    getUser: async (id) => {
      const user = await prisma.user.findUnique({ where: { id } });
      if (!user) return null;

      return {
        id: user.id,
        email: user.email || "",
        emailVerified: user.emailVerified,
        name: user.name || null,
        image: user.image || null,
      };
    },

    getUserByEmail: async (email) => {
      const user = await prisma.user.findUnique({ where: { email } });
      if (!user) return null;

      return {
        id: user.id,
        email: user.email || "",
        emailVerified: user.emailVerified,
        name: user.name || null,
        image: user.image || null,
      };
    },

    getUserByAccount: async ({ provider, providerAccountId }) => {
      const account = await prisma.account.findUnique({
        where: {
          provider_providerAccountId: { provider, providerAccountId },
        },
        include: { user: true },
      });

      if (!account?.user) return null;

      return {
        id: account.user.id,
        email: account.user.email || "",
        emailVerified: account.user.emailVerified,
        name: account.user.name || null,
        image: account.user.image || null,
      };
    },

    updateUser: async (data) => {
      const { id, ...userData } = data;
      const user = await prisma.user.update({
        where: { id },
        data: userData,
      });

      return {
        id: user.id,
        email: user.email || "",
        emailVerified: user.emailVerified,
        name: user.name || null,
        image: user.image || null,
      };
    },

    deleteUser: async (userId) => {
      await prisma.user.delete({ where: { id: userId } });
    },

    // Account operations
    linkAccount: async (data) => {
      await prisma.account.create({ data });
      return data;
    },

    unlinkAccount: async ({ provider, providerAccountId }) => {
      await prisma.account.delete({
        where: {
          provider_providerAccountId: { provider, providerAccountId },
        },
      });
    },

    // Session operations
    createSession: async (data) => {
      const session = await prisma.session.create({ data });
      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      };
    },

    getSessionAndUser: async (sessionToken) => {
      const session = await prisma.session.findUnique({
        where: { sessionToken },
        include: { user: true },
      });

      if (!session) return null;

      return {
        session: {
          sessionToken: session.sessionToken,
          userId: session.userId,
          expires: session.expires,
        },
        user: {
          id: session.user.id,
          email: session.user.email || "",
          emailVerified: session.user.emailVerified,
          name: session.user.name || null,
          image: session.user.image || null,
        },
      };
    },

    updateSession: async (data) => {
      const session = await prisma.session.update({
        where: { sessionToken: data.sessionToken },
        data,
      });

      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      };
    },

    deleteSession: async (sessionToken) => {
      await prisma.session.delete({ where: { sessionToken } });
    },

    // Verification token operations
    createVerificationToken: async (data) => {
      const verificationToken = await prisma.verificationToken.create({ data });
      const { id: _omit, ...rest } = verificationToken as any;
      return rest;
    },

    useVerificationToken: async ({ identifier, token }) => {
      try {
        const verificationToken = await prisma.verificationToken.delete({
          where: { identifier_token: { identifier, token } },
        });
        const { id: _omit, ...rest } = verificationToken as any;
        return rest;
      } catch (error) {
        // If token already used/deleted, just return null
        if ((error as Error).name === "PrismaClientKnownRequestError") {
          return null;
        }
        throw error;
      }
    },
  };
}
