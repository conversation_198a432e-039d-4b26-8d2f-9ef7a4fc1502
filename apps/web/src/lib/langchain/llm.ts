import { Chat<PERSON>penAI, OpenAI } from "@langchain/openai";
import { Cohere } from "@langchain/cohere";
import { Callbacks } from "@langchain/core/callbacks/manager";
import { LogManager, UsageType } from "@/observability/migration-shim";
import { tenantGuardedPrisma } from "../prisma";
import { Prisma } from "@askinfosec/database";
import { aiSettings } from "@/services/server/ai-settings";
import { AISettings } from "@/types/ai-settings";
export const llmService = async (
  transactionId: string,
  featureType: "chat" | "questions_bulk_upload",
  logManager: LogManager,
  userId: string,
  org: string,
  reqId: string,
  opts = {},
) => {
  const aiConfig = await aiSettings(org);
  switch (process.env.LLM_SERVICE) {
    case "cohere":
      if (!process.env.COHERE_API_KEY) {
        throw new Error("COHERE_API_KEY is required to use cohere llm service");
      }
      return new Cohere({
        ...{
          model: "command",
          temperature: aiConfig.temperature,
          maxTokens: aiConfig.maxTokens,
        },
        callbacks: callbacks(
          transactionId,
          featureType,
          logManager,
          userId,
          org,
          reqId,
        ),
        ...opts,
      });
    default:
      return new OpenAI({
        ...{
          temperature: aiConfig.temperature,
          maxTokens: aiConfig.maxTokens,
        },
        callbacks: callbacks(
          transactionId,
          featureType,
          logManager,
          userId,
          org,
          reqId,
        ),
        ...opts,
      });
  }
};

export const chatllm = async (
  transactionId: string,
  featureType: "chat" | "questions_bulk_upload",
  logManager: LogManager,
  userId: string,
  org: string,
  reqId: string,
  aiConfig: AISettings,
) => {
  return new ChatOpenAI({
    ...{ temperature: aiConfig.temperature, maxTokens: aiConfig.maxTokens },
    callbacks: callbacks(
      transactionId,
      featureType,
      logManager,
      userId,
      org,
      reqId,
    ),
  });
};

const callbacks = (
  transactionId: string,
  featureType: "chat" | "questions_bulk_upload",
  logManager: LogManager,
  userId: string,
  org: string,
  reqId: string,
) => {
  const db = tenantGuardedPrisma(org);
  const callbacks: Callbacks = [
    {
      async handleLLMStart(
        llm,
        prompts,
        runId,
        parentRunId,
        extraParams,
        tags,
        metadata,
        name,
      ) {
        const data = {
          llm: JSON.stringify(llm),
          prompts,
          runId,
          parentRunId,
          extraParams,
          tags,
          metadata,
          name,
        };
        await db.aIUsage.create({
          data: {
            message: "LLM Start",
            model_class: UsageType.prompt,
            ai_feature_type: featureType,
            user_id: userId,
            organization_id: org,
            data: data as Prisma.JsonObject,
            transaction_id: transactionId,
            log_ref_id: reqId,
          },
        });
        logManager.addLog({
          usageType: UsageType.prompt,
          message: "LLM Start",
          obj: data,
        });
      },
      async handleLLMEnd(output) {
        await db.aIUsage.create({
          data: {
            message: "LLM End",
            model_class: UsageType.prompt,
            ai_feature_type: featureType,
            user_id: userId,
            organization_id: org,
            data: { output: JSON.stringify(output) },
            transaction_id: transactionId,
            log_ref_id: reqId,
          },
        });
        logManager.addLog({
          usageType: UsageType.prompt,
          message: "LLM End",
          obj: { output },
        });
      },
      async handleLLMError(err, runId, parentRunId, tags) {
        const data = { err: JSON.stringify(err), runId, parentRunId, tags };
        await db.aIUsage.create({
          data: {
            message: "LLM Error",
            model_class: UsageType.prompt,
            ai_feature_type: featureType,
            user_id: userId,
            organization_id: org,
            data: data,
            transaction_id: transactionId,
            log_ref_id: reqId,
          },
        });
        logManager.addLog({
          usageType: UsageType.prompt,
          message: "LLM Error",
          obj: data,
        });
      },
    },
  ];
  return callbacks;
};
