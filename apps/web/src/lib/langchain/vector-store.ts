import {
  DocumentVector,
  KnowledgeBase,
  Prisma,
  PrismaClient,
} from "@askinfosec/database";
import { AISPrismaVectorStore, ModelColumns } from "./prisma";
import embeddingService from "./embeddings";
import { DefaultArgs } from "@askinfosec/database";
import { PrismaSqlFilter } from "@langchain/community/vectorstores/prisma";

export const ID_COLUMN = AISPrismaVectorStore.IdColumn;
export const CONTENT_COLUMN = AISPrismaVectorStore.ContentColumn;

/**
 * Initialize Prisma Vectore Store
 * @param param0
 * @returns
 */
export const initVectorStore = <T extends DocumentVector | KnowledgeBase>({
  tableName,
  vectorColumnName,
  columns,
  db,
  org,
}: {
  tableName: "DocumentVector" | "KnowledgeBase";
  vectorColumnName: string;
  columns: ModelColumns<T>;
  db: PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>;
  org: string;
}) => {
  return AISPrismaVectorStore.withModel<T>(db).create(embeddingService(), {
    prisma: Prisma,
    tableName,
    vectorColumnName,
    columns,
    filter: { organization_id: { equals: org } } as PrismaSqlFilter<T>,
  });
};
