import { createStuffDocume<PERSON><PERSON>hain } from "langchain/chains/combine_documents";
import { z } from "zod";
import { ChatOpenAI, OpenAICallOptions } from "@langchain/openai";
import { Document } from "@langchain/core/documents";
import { StructuredOutputParser } from "langchain/output_parsers";
import { PrismaSqlFilter } from "@langchain/community/vectorstores/prisma";
import { PromptTemplate } from "@langchain/core/prompts";
// @ts-ignore
import {
  $Enums,
  KnowledgeBase,
  DocumentVector,
  Prisma,
  PrismaClient,
} from "@askinfosec/database";
import { tenantGuardedPrisma } from "../prisma";
import { DefaultArgs } from "@askinfosec/database";
import { CONTENT_COLUMN, ID_COLUMN, initVectorStore } from "./vector-store";
import { chatllm } from "./llm";
import { LogManager, UsageType } from "@/observability/migration-shim";
import { getOrg } from "@/services/server/organization";
import { isLimitReached } from "@/services/server/ai-usage";
import { aiSettings } from "@/services/server/ai-settings";
import { AISettings } from "@/types/ai-settings";
import { prompts } from "@/lib/api/openai/config";
import { AISPrismaVectorStore, ModelColumns } from "./prisma";

const zodSchema = z.object({
  yesNoAnswer: z
    .enum(["Yes", "No", "N/A"])
    .default("N/A")
    .describe(
      "Yes or No answer if not answerable by yes or no, N/A by default. Remove whitespace",
    ),
  answer: z.string().describe("The answer. Remove whitespace"),
  sources: z
    .array(z.string())
    .default([])
    .describe(
      "If an answer to the question is provided. List of headings in the context provided. If there is no source provide an empty array. Remove whitespace",
    ),
});

interface DocModelColumn extends Record<string, unknown> {
  id: string;
  content: string;
  metadata: Prisma.JsonValue;
  organization_id: string;
  file_id: string;
  created_at: Date;
  updated_at: Date;
}

interface KBModelColumn extends Record<string, unknown> {
  id: string;
  created_at: Date;
  question: string;
  answer: string | null;
  answer_yes_no_na: $Enums.ShortAnswer | null;
  ai_accepted_answer_from_docs: string | null;
  comment: string | null;
  reference: string | null;
  created_by_id: string;
  owned_by_id: string | null;
  assigned_id: string | null;
  organization_id: string;
  last_update_by_id: string;
  category_id: string | null;
  last_verification_date: Date | null;
  updated_at: Date;
  status: string;
  next_verification: string | null;
  scope_id: string | null;
  access_level: string | null;
  tags: string[];
}

type VectorStoreType =
  | AISPrismaVectorStore<
      Record<string, unknown>,
      "DocumentVector",
      ModelColumns<DocModelColumn>,
      PrismaSqlFilter<DocModelColumn>
    >
  | AISPrismaVectorStore<
      Record<string, unknown>,
      "KnowledgeBase",
      ModelColumns<KBModelColumn>,
      PrismaSqlFilter<KBModelColumn>
    >;

export type Answer = z.infer<typeof zodSchema>;

export interface SourceId {
  type: "kb" | "document";
  refId: string;
  ref_id?: string;
  fileName?: string;
}

export interface AIAnswer {
  question_prompt: string;
  answer: string | undefined;
  sourceHeaders?: string[];
  source: "kb" | "document" | "combined" | undefined;
  sourceId: SourceId[] | undefined;
  resultQuestion: string | undefined;
  shortAnswer?: string | undefined;
}

interface GetAnswerParams {
  transactionId: string;
  featureType: "chat" | "questions_bulk_upload";
  question: string;
  org: string;
  logManager: LogManager;
  userId: string;
  reqId: string;
  scope?: string | null;
  addInfo?: string;
  selectedDocs: string[];
}

interface DocAndKbParams {
  transactionId: string;
  featureType: "chat" | "questions_bulk_upload";
  prompt: string;
  scope: string | null;
  model: ChatOpenAI<OpenAICallOptions>;
  db: PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>;
  userId: string;
  org: string;
  reqId: string;
  addInfo?: string;
  logManager: LogManager;
  aiSetting: AISettings;
  selectedDocs: string[];
}

export const getAnswerKb = async ({
  transactionId,
  question,
  org,
  userId,
  reqId,
  logManager,
}: {
  transactionId: string;
  question: string;
  userId: string;
  org: string;
  reqId: string;
  logManager: LogManager;
}) => {
  try {
    logManager.addLog({
      message: "Starting KB answer retrieval",
      obj: { transactionId, question, org, userId },
    });

    const db = tenantGuardedPrisma(org);
    const { maxPromptLength } = await aiSettings(org);

    const prompt = question?.substring(0, maxPromptLength) || "";
    const search =
      question
        ?.split(" | ")
        ?.map((n) =>
          n.includes("&")
            ? n
                .split(" & ")
                ?.map((n) => `'${n}'`)
                ?.join(" & ")
            : `'${n}'`,
        )
        ?.join(" | ") || "";

    logManager.addLog({
      message: "Executing KB search query",
      obj: { search },
    });

    const answers = await db.knowledgeBase.findMany({
      where: { question: { search } },
      include: { owned_by: true, created_by: true, scope: true },
    });

    logManager.addLog({ message: `Found ${answers.length} KB matches` });

    if (answers.length > 0) {
      const kbs = answers.map((a: any) => {
        const category = a.category_id?.replace("_", " ") || "";
        const categoryName = category
          ? category[0]?.toUpperCase() + category.slice(1)
          : "";

        return {
          answer: a.answer,
          assigned_to: a.created_by,
          category: categoryName,
          created_at: a.created_at,
          id: a.id,
          last_update_by_id: a.last_update_by_id,
          last_verification_date: a.last_verification_date,
          next_verification_date: a.next_verification,
          owned_by: a.owned_by,
          question: a.question,
          ref_num: a.ref_num,
          scope: { label: a.scope?.name, value: a.scope_id },
          scope_id: a.scope_id,
          status: a.status,
          tags: a.tags?.length >= 1 ? a.tags : [],
          updated_at: a.updated_at,
        };
      });

      logManager.addLog({ message: "Returning full-text search results" });
      return { source: "FullTextSearch", data: kbs };
    }

    logManager.addLog({ message: "Initializing KB vector store" });
    const kbVectorStore = initVectorStore<KnowledgeBase>({
      tableName: "KnowledgeBase",
      vectorColumnName: "vector",
      columns: {
        id: ID_COLUMN,
        question: CONTENT_COLUMN,
        answer: Symbol("answer"),
        category_id: Symbol("category_id"),
        assigned_id: Symbol("assigned_id"),
        created_at: Symbol("created_at"),
        last_update_by_id: Symbol("last_update_by_id"),
        ref_num: Symbol("ref_num"),
        status: Symbol("status"),
        updated_at: Symbol("updated_at_"),
        last_verification_date: Symbol("last_verification_date"),
        next_verification: Symbol("last_verification_date"),
        scope_id: Symbol("scope_id"),
        tags: Symbol("tags"),
      },
      db,
      org,
    });

    const kbContext = await similaritySearch({
      transactionId,
      featureType: "kb_search",
      vectorStore: kbVectorStore,
      prompt,
      logManager,
      k: 10,
      userId,
      org,
      reqId,
    });

    logManager.addLog({
      message: `KB vector search returned ${kbContext.length} items`,
    });

    const processedKbs = await Promise.all(
      kbContext.map(async (kb: any) => {
        try {
          const tags =
            kb.metadata.tags?.length >= 1
              ? await Promise.all(
                  kb.metadata.tags.map(async (tag: any) => {
                    const t = await db.tag.findFirst({
                      where: { id: tag, organization_id: org },
                    });
                    return t ? { value: tag, label: t.name } : null;
                  }),
                )
              : [];

          const category = kb.metadata.category_id?.replace("_", " ") || "";
          const categoryName = category
            ? category[0]?.toUpperCase() + category.slice(1)
            : "";
          const scope = await db.scope.findFirst({
            where: { id: kb.scope_id },
          });

          return {
            answer: kb.metadata.answer,
            assigned_to: kb.metadata.created_by,
            category: categoryName,
            created_at: kb.metadata.created_at,
            id: kb.metadata.id,
            last_update_by_id: kb.metadata.last_update_by_id,
            last_verification_date: kb.metadata.last_verification_date,
            next_verification_date: kb.metadata.next_verification,
            owned_by: kb.metadata.owned_by,
            question: kb.metadata.question,
            ref_num: kb.metadata.ref_num,
            scope: { label: scope?.name, value: scope?.id },
            scope_id: kb.metadata.scope_id,
            status: kb.metadata.status,
            tags: tags.filter(Boolean),
            updated_at_: kb.metadata.updated_at,
            distance: kb.metadata._distance,
          };
        } catch (error) {
          logManager.addLog({
            message: `Error processing KB item ${kb.metadata.id}`,
            obj: error instanceof Error ? error : { message: String(error) },
          });
          return null;
        }
      }),
    );

    const filteredKbs = processedKbs
      .filter((kb): kb is NonNullable<typeof kb> => kb !== null)
      .filter((kb: { distance: number }) => kb.distance < 0.2);

    logManager.addLog({
      message: `Final KB results after filtering: ${filteredKbs.length}`,
      obj: { distances: filteredKbs.map((k) => k.distance) },
    });

    return { source: "AI", data: filteredKbs };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logManager.addLog({
      message: `Critical error in getAnswerKb: ${errorMessage}`,
      obj: error instanceof Error ? error : { message: String(error) },
    });
    throw error;
  } finally {
    logManager.addLog({ message: "Completed KB answer retrieval" });
    logManager.printAll();
  }
};

export const getAnswer = async ({
  transactionId,
  featureType,
  question,
  scope = null,
  org,
  userId,
  logManager,
  reqId,
  addInfo,
  selectedDocs = [],
}: GetAnswerParams): Promise<AIAnswer> => {
  try {
    logManager.addLog({
      message: `getAnswer() started`,
      obj: {
        transactionId: transactionId,
        org: org,
        userId: userId,
        question: question,
      },
    });

    const db = tenantGuardedPrisma(org);
    // Get the AI settings for the organization as global settings
    const aiSetting = await aiSettings(org);
    // Initialize the model to use - this is not executing the chat completion yet
    const model = await chatllm(
      transactionId,
      featureType,
      logManager,
      userId,
      org,
      reqId,
      aiSetting,
    );
    const prompt = (question as string)?.substring(
      0,
      aiSetting.maxPromptLength,
    );
    const answerParams = {
      transactionId,
      featureType,
      prompt,
      scope,
      model,
      db,
      userId,
      org,
      reqId,
      addInfo,
      logManager,
      aiSetting,
      selectedDocs,
    };

    const res = await combinedAnswer(answerParams);

    logManager.addLog({
      message: "getAnswer() completed",
      obj: { answerExists: !!res?.answer },
    });

    return (
      res || {
        question_prompt: question,
        answer: undefined,
        sourceHeaders: [],
        source: undefined,
        sourceId: undefined,
        resultQuestion: undefined,
      }
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logManager.addLog({
      message: `Critical error in getAnswer: ${errorMessage}`,
      obj: error instanceof Error ? error : { message: String(error) },
    });
    throw error;
  } finally {
    logManager.addLog({ message: "Finalizing getAnswer processing" });
    logManager.printAll();
  }
};

const combinedAnswer = async (
  params: DocAndKbParams,
): Promise<AIAnswer | undefined> => {
  const { logManager, org, userId, featureType } = params;

  try {
    logManager.addLog({
      message: "Starting combined answer processing",
      obj: { prompt: params.prompt },
    });

    logManager.addLog({
      message: `finding answer in combined. Question: ${params.prompt}`,
    });

    const currentOrg = await getOrg({ userId, orgId: org });
    if (!currentOrg) {
      logManager.addLog({ message: "Organization not found" });
      return undefined;
    }

    const answerWithKB = currentOrg?.settings?.ai_answer_domain
      ? currentOrg.settings.ai_answer_domain.includes("kb")
      : undefined;
    const answerWithDocs = currentOrg?.settings?.ai_answer_domain
      ? currentOrg.settings.ai_answer_domain.includes("document")
      : undefined;
    console.log(
      `answerWithKB: ${answerWithKB}, answerWithDocs: ${answerWithDocs}`,
    );
    const aiAnswer: AIAnswer = {
      question_prompt: params.prompt,
      answer: undefined,
      sourceHeaders: [],
      source: undefined,
      sourceId: undefined,
      resultQuestion: undefined,
      shortAnswer: undefined,
    };
    // if answer with KB in settings is undefined (if newly created org) consider as true
    // Disable kb temporarily
    const kbContext =
      // answerWithKB === undefined || answerWithKB
      answerWithKB
        ? await getKbContext(
            params.transactionId,
            featureType,
            params.prompt,
            params.scope,
            params.db,
            logManager,
            userId,
            params.reqId,
            org,
          )
        : [];
    // if answer with Doc in settings is undefined (if newly created org) consider as true
    const docContext =
      answerWithDocs === undefined || answerWithDocs
        ? await getDocContext(
            params.transactionId,
            featureType,
            params.prompt,
            params.scope,
            params.db,
            logManager,
            userId,
            params.reqId,
            org,
            params.selectedDocs,
          )
        : [];

    console.log(
      `KB Context: ${kbContext.length}, Doc Context: ${docContext.length}`,
    );
    if (kbContext.length === 0 && docContext.length === 0) {
      return aiAnswer;
    }

    const parser = StructuredOutputParser.fromZodSchema(zodSchema);

    const promptTemplate = (): PromptTemplate => {
      // Prepare the default prompt template which is combined docs and kb. Below we will override depending if both are enabled.
      const combinedDefault = new PromptTemplate({
        template: prompts.COMBINED_PROMPT,
        inputVariables: [
          "context",
          "kb_context",
          "format_instructions",
          "prompt",
          "additional_instructions",
        ],
      });
      if (answerWithKB && answerWithDocs) {
        return combinedDefault;
      }
      if (answerWithDocs) {
        return new PromptTemplate({
          template: prompts.DEFAULT_DOC_PROMPT,
          inputVariables: [
            "context",
            "format_instructions",
            "prompt",
            "additional_instructions",
          ],
        });
      }
      if (answerWithKB) {
        return new PromptTemplate({
          template: prompts.DEFAULT_KB_PROMPT,
          inputVariables: [
            "context",
            "format_instructions",
            "prompt",
            "additional_instructions",
          ],
        });
      }
      return combinedDefault;
    };

    const chain = await createStuffDocumentsChain({
      llm: params.model,
      prompt: promptTemplate(),
      outputParser: parser,
    });

    // Generate the answer
    if (await isLimitReached(org, userId, featureType)) {
      logManager.addLog({
        usageType: UsageType.prompt,
        message: "AI Usage limit reached!",
        obj: { prompt: params.prompt },
      });
      logManager.printAll();
      throw new Error("AI Usage limit reached!");
    }
    let answer: Answer;
    answer = await chain.invoke({
      format_instructions: parser.getFormatInstructions(),
      additional_instructions: params.addInfo || "",
      prompt: params.prompt,
      ...(answerWithDocs &&
        answerWithKB && {
          context: docContext,
          kb_context: JSON.stringify(
            kbContext.map((c) => ({
              question: c.pageContent,
              answer: c.metadata.answer,
            })),
          ),
        }),
      ...(answerWithDocs && !answerWithKB && { context: docContext }),
      ...(answerWithKB &&
        !answerWithDocs && {
          context: kbContext.map((c) => {
            return {
              pageContent: JSON.stringify({
                question: c.pageContent,
                answer: c.metadata.answer,
              }),
            };
          }),
        }),
    });

    // Let's be fair! This is a bonud. Only count as AI usage there is an answer. But in the higher level, try to not reach this function if possible of no answer is high.
    if (!answer.answer.toUpperCase().includes("NO_KNOWLEDGE")) {
      const db = tenantGuardedPrisma(org);
      await db.aIUsage.create({
        data: {
          message: "AI Response",
          model_class: UsageType.prompt,
          ai_feature_type: featureType,
          user_id: userId,
          organization_id: org,
          data: { response: answer },
          transaction_id: params.transactionId,
          log_ref_id: params.reqId,
        },
      });
    }

    const sourceId: SourceId[] = [];
    if (answerWithDocs) {
      // loop through docContext and push an object to sourceId array if value is not already present
      docContext.forEach((dc) => {
        if (!sourceId.find((x) => x.refId === dc.metadata.file_id)) {
          sourceId.push({
            refId: dc.metadata.file_id as string,
            type: "document",
          });
        }
      });
    }
    if (answerWithKB) {
      // loop through kbContext and push an object to sourceId array if value is not already present
      kbContext.forEach((dc) => {
        if (!sourceId.find((x) => x.refId === dc.metadata.id)) {
          sourceId.push({
            refId: dc.metadata.id as string,
            type: "kb",
          });
        }
      });
    }
    // Use the response as the answer.
    if (!answer.answer.toUpperCase().includes("NO_KNOWLEDGE")) {
      logManager.addLog({
        message: "Successfully generated answer",
        obj: { answerLength: answer.answer.length },
      });
      return {
        ...aiAnswer,
        answer: answer.answer,
        sourceHeaders: answer.sources,
        shortAnswer: answer.yesNoAnswer,
        source: "combined",
        sourceId,
      };
    }

    logManager.addLog({ message: "No valid answer generated" });
    return undefined;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logManager.addLog({
      message: `Critical error in combinedAnswer: ${errorMessage}`,
      obj: error instanceof Error ? error : { message: String(error) },
    });
    throw error;
  } finally {
    logManager.addLog({ message: "Completed combined answer processing" });
    logManager.printAll();
  }
};

const getKbContext = async (
  transactionId: string,
  featureType: "chat" | "questions_bulk_upload" | "kb_search",
  prompt: string,
  scope: string | null,
  db: PrismaClient,
  logManager: LogManager,
  userId: string,
  reqId: string,
  org: string,
) => {
  // Initialize Prisma Vector Store with knowledge_base table and return also the answer in the response
  // then filter by organization
  const vectorStore = initVectorStore<KnowledgeBase>({
    tableName: "KnowledgeBase",
    vectorColumnName: "vector",
    columns: {
      id: ID_COLUMN,
      question: CONTENT_COLUMN,
      answer: Symbol("answer"),
      scope_id: Symbol("scope_id"),
    },
    db,
    org,
  });
  // Search for the prompt and get the top 5 result
  const context = await similaritySearch({
    transactionId,
    featureType,
    vectorStore,
    prompt,
    logManager,
    k: 5,
    userId,
    org,
    reqId,
  });
  logManager.addLog({ message: `Similarity search found ${context.length}` });
  const relevantContext = [];
  // Get the most relevant result by removing objects that has distance greater than 0.15
  for await (const kbc of context) {
    if (
      kbc.metadata._distance !== null &&
      kbc.metadata._distance < 0.15 &&
      Boolean((kbc.metadata.answer as string).trim())
    ) {
      const scopeDb = await db.scope.findFirst({ where: { name: "Common" } });
      const allScope = scopeDb ? scopeDb.id : null;
      if (
        scope === allScope ||
        kbc.metadata.scope_id === scope ||
        kbc.metadata.scope_id === allScope
      )
        relevantContext.push(kbc);
    }
  }
  logManager.addLog({
    message: `Eliminating vector distance below 0.15 and scope is not ${scope}. Remaining relevant context is ${relevantContext.length}`,
  });
  logManager.addLog({ message: "Relevant context", obj: relevantContext });

  return relevantContext;
};

const getDocContext = async (
  transactionId: string,
  featureType: "chat" | "questions_bulk_upload" | "kb_search",
  prompt: string,
  scope: string | null,
  db: PrismaClient,
  logManager: LogManager,
  userId: string,
  reqId: string,
  org: string,
  selectedDocs: string[],
) => {
  // Initialize Prisma Vector Store with document_vector table and return also the file_id in the response
  // then filter by organization
  const vectorStore = initVectorStore<DocumentVector>({
    tableName: "DocumentVector",
    vectorColumnName: "vector",
    columns: {
      id: ID_COLUMN,
      content: CONTENT_COLUMN,
      file_id: Symbol("file_id"),
    },
    db,
    org,
  });

  // if selectedDocs is not empty means that we want to return all the context/embeddings of the files included in the selectedDocs array of fileId
  if (selectedDocs.length > 0) {
    const contents = await db.documentVector.findMany({
      where: { file_id: { in: selectedDocs } },
    });
    return vectorStore.convertToDocument(contents);
  }

  // Search for the prompt and get the 7 most relevant result
  const context = await similaritySearch({
    transactionId,
    featureType,
    vectorStore,
    prompt,
    logManager,
    k: 3,
    userId,
    org,
    reqId,
  });
  logManager.addLog({ message: `Similarity search found ${context.length}` });

  // Filter document context by scope
  const relevantContext = [];
  for await (const dc of context) {
    const file = await db.file.findUnique({
      where: { id: dc.metadata.file_id as string },
    });
    const file_scope_id = file?.scope_id || "";
    const scopeDb = await db.scope.findFirst({ where: { name: "Common" } });
    const allScope = scopeDb ? scopeDb.id : null;
    if (
      scope === allScope ||
      file_scope_id === scope ||
      file_scope_id === allScope
    )
      relevantContext.push(dc);
  }
  logManager.addLog({
    message: `Eliminating scope that is not ${scope}. Remaining relevant context is ${relevantContext.length}`,
  });
  logManager.addLog({ message: "Relevant context", obj: relevantContext });

  return relevantContext;
};

/**
 * Convert text to embeddings and save to database
 */
export const embedData = async ({
  transactionId,
  featureType,
  vectorStore,
  data,
  logManager,
  userId,
  org,
  reqId,
}: {
  transactionId: string;
  featureType: "document_upload" | "knowledge_base";
  vectorStore: VectorStoreType;
  data: Document<Record<string, unknown>>[];
  logManager: LogManager;
  userId: string;
  org: string;
  reqId: string;
}) => {
  const db = tenantGuardedPrisma(org);
  // We check if we already reached the limit of this feature for this organization.
  if (await isLimitReached(org, userId, featureType)) {
    logManager.addLog({
      usageType: UsageType.embedding,
      message: "AI Usage limit reached!",
      obj: { data },
    });
    throw new Error("AI Usage limit reached!");
  }
  await vectorStore.addDocuments(data);
  await db.aIUsage.create({
    data: {
      message: "Embed data for training",
      model_class: UsageType.embedding,
      ai_feature_type: featureType,
      user_id: userId,
      organization_id: org,
      data: { data: JSON.stringify(data) },
      transaction_id: transactionId,
      log_ref_id: reqId,
    },
  });
  logManager.addLog({
    usageType: UsageType.embedding,
    message: "Embed data for training",
    obj: { data },
  });
};

/**
 * Search text from vector store
 */
const similaritySearch = async ({
  transactionId,
  featureType,
  vectorStore,
  prompt,
  logManager,
  k = undefined,
  userId,
  org,
  reqId,
}: {
  transactionId: string;
  featureType: "chat" | "questions_bulk_upload" | "kb_search";
  vectorStore: VectorStoreType;
  prompt: string;
  logManager: LogManager;
  k: number | undefined;
  userId: string;
  org: string;
  reqId: string;
}) => {
  const db = tenantGuardedPrisma(org);
  if (await isLimitReached(org, userId, featureType || "kb_search")) {
    logManager.addLog({
      usageType: UsageType.embedding,
      message: "AI Usage limit reached!",
      obj: { prompt },
    });
    throw new Error("AI Usage limit reached!");
  }
  const searchData = await vectorStore.similaritySearch(prompt, k);
  if (searchData && searchData.length > 0) {
    await db.aIUsage.create({
      data: {
        message: "Embed prompt for similaritySearch",
        model_class: UsageType.embedding,
        ai_feature_type: featureType,
        user_id: userId,
        organization_id: org,
        data: { prompt },
        transaction_id: transactionId,
        log_ref_id: reqId,
      },
    });
  }
  logManager.addLog({
    usageType: UsageType.embedding,
    message: "Embed prompt for similaritySearch",
    obj: { prompt },
  });
  return searchData;
};
