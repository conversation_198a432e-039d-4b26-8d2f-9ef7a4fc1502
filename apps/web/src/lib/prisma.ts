export * from "@askinfosec/database";
// /**
//  * Prisma client instance with connection pool management
//  *
//  * TODO: For data returned to client components, ensure objects are mapped to
//  * Kysely DTOs from @/types/prisma-kysely before being returned.
//  * Example:
//  * - Import: import { User as KyselyUser } from '@/types/prisma-kysely';
//  * - Map: const userDto: KyselyUser = mapToKyselyUser(dbUser);
//  */

// import { prisma as databasePrisma, PrismaClient } from "@askinfosec/database";
// import { detailedDiff, updatedDiff } from "deep-object-diff";
// import { camelCase, isEqual, pick } from "../lib/utils";

// import { HttpError } from "./errors";
// import { AuditMetadata, OtherDataAuditLog } from "../models/audit-log";
// import { OtherData } from "../models/policy";
// import { createId } from "@paralleldrive/cuid2";

// // Re-export the base Prisma client from the database package
// export const prisma = databasePrisma;

// // Re-export additional functions from the database package
// export {
//   getPrismaInstanceCount,
//   getConnectionPoolMetrics,
// } from "@askinfosec/database";

// // Base PrismaClient reference for use inside extensions where the extended type may hide core methods
// const basePrisma = databasePrisma as unknown as PrismaClient;

// /**
//  * Creates a guarded Prisma client instance with security context:
//  * - Sets organization/user context for RLS policies
//  * - Verifies membership/roles when accessing organization resources
//  * - All queries run in transaction to ensure context consistency
//  */
// export function guardedPrisma({
//   orgId,
//   userId,
//   roles,
//   checkMembership = true,
// }: {
//   orgId?: string;
//   userId?: string;
//   roles?: string[];
//   checkMembership?: boolean;
// }) {
//   // In Edge Runtime, return the mock client
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return prisma as PrismaClient;
//   }

//   return prisma.$extends({
//     query: {
//       $allModels: {
//         async $allOperations({ args, query }: { args: any; query: any }) {
//           // Transaction sequence:
//           // 1. Set organization context for RLS
//           // 2. Set user context for RLS
//           // 3. Validate membership (if required)
//           // 4. Execute original query
//           const results = await basePrisma.$transaction([
//             ...(orgId
//               ? [
//                   basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
//                 ]
//               : []),
//             ...(userId
//               ? [
//                   basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`,
//                 ]
//               : []),
//             ...(orgId && userId && roles && checkMembership
//               ? [
//                   basePrisma.member.findFirstOrThrow({
//                     where: {
//                       user_id: userId,
//                       organization_id: orgId,
//                       role_id: { in: roles },
//                     },
//                   }),
//                 ]
//               : orgId && userId && checkMembership
//                 ? [
//                     basePrisma.member.findFirstOrThrow({
//                       where: { user_id: userId, organization_id: orgId },
//                     }),
//                   ]
//                 : []),
//             query(args),
//           ]);
//           return results[results.length - 1]; // Return query result
//         },
//       },
//     },
//   }) as PrismaClient;
// }

// export function tenantGuardedPrisma(orgId: string) {
//   // In Edge Runtime, return the mock client
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return prisma as PrismaClient;
//   }

//   return prisma.$extends({
//     query: {
//       $allModels: {
//         async $allOperations({ args, query }: { args: any; query: any }) {
//           const [, , result] = await basePrisma.$transaction([
//             basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
//             basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
//             query(args),
//           ]);
//           return result;
//         },
//       },
//     },
//   }) as PrismaClient;
// }

// export function userGuardedPrisma(userId: string) {
//   // In Edge Runtime, return the mock client
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return prisma as PrismaClient;
//   }

//   return prisma.$extends({
//     query: {
//       $allModels: {
//         async $allOperations({ args, query }: { args: any; query: any }) {
//           const [, , result] = await basePrisma.$transaction([
//             basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`,
//             basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
//             query(args),
//           ]);
//           return result;
//         },
//       },
//     },
//   }) as PrismaClient;
// }

// /**
//  * Bypass RLS client for administrative operations. Use with extreme caution.
//  * Required for:
//  * - Audit log writes
//  * - System-level maintenance
//  * - Emergency data repairs
//  */
// export const bypassedPrisma =
//   typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge"
//     ? prisma // In Edge Runtime, just return the mock prisma client
//     : prisma.$extends({
//         query: {
//           $allModels: {
//             async $allOperations({ args, query }: { args: any; query: any }) {
//               const [, result] = await basePrisma.$transaction([
//                 basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
//                 query(args),
//               ]);
//               return result;
//             },
//           },
//         },
//       });

// export function forOrganization(
//   currentOrganizationId: string,
//   currentUserId?: string,
// ) {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async $allOperations({ args, query }: { args: any; query: any }) {
//             const [, , result] = await basePrisma.$transaction([
//               basePrisma.$executeRaw`SELECT set_config('app.current_organization_id', ${currentOrganizationId}, TRUE)`,
//               basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${currentUserId}, TRUE)`,
//               query(args),
//             ]);
//             return result;
//           },
//         },
//       },
//     });
// }

// export function forUser(currentUserId: string) {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async $allOperations({ args, query }: { args: any; query: any }) {
//             const [, result] = await basePrisma.$transaction([
//               basePrisma.$executeRaw`SELECT set_config('app.current_user_id', ${currentUserId}, TRUE)`,
//               query(args),
//             ]);
//             return result;
//           },
//         },
//       },
//     });
// }

// export function bypassRLS() {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async $allOperations({ args, query }: { args: any; query: any }) {
//             const [, result] = await basePrisma.$transaction([
//               basePrisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
//               query(args),
//             ]);
//             return result;
//           },
//         },
//       },
//     });
// }

// /**
//  * Core audit logging system with three-phase workflow:
//  * 1. Capture before-state
//  * 2. Execute operation
//  * 3. Record delta in audit_log table
//  */
// export function withAuditLogger({
//   orgId,
//   userId,
//   message,
// }: {
//   orgId: string;
//   userId: string;
//   message?: string;
// }) {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async update({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             // Phase 1: Pre-operation checks
//             const payload = args.data;
//             const where = args.where as { id: string };
//             if (!where.id) {
//               throw Error(
//                 "update operation with audit logger requires the 'where' clause to have the 'id' property",
//               );
//             }

//             // Phase 2: State capture
//             const existing = await (
//               basePrisma[camelCase(model) as keyof PrismaClient] as any
//             ).findFirst({ where });
//             if (!existing) {
//               throw new HttpError(404, "data to be updated is not found", true);
//             }

//             // Phase 3: Delta calculation and audit record
//             const comparator = Object.keys(payload ?? {}) as string[];
//             const skipUpdate = isEqual(
//               pick(existing, comparator),
//               pick(payload, comparator),
//             );
//             if (skipUpdate) return existing;

//             const result = await query(args);

//             const diff = getDiff(existing, result);
//             await (basePrisma as PrismaClient).auditLog.create({
//               data: {
//                 ref_id: where.id,
//                 event: "update",
//                 entity: model,
//                 message,
//                 new_value: diff.updated,
//                 old_value: { ...diff.updatedOriginal, ...diff.deleted },
//                 user_id: userId,
//                 organization_id: orgId,
//               },
//             });

//             return result;
//           },
//           async create({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const result = await query(args);
//             await (basePrisma as PrismaClient).auditLog.create({
//               data: {
//                 ref_id: (result as any).id,
//                 event: "create",
//                 entity: model,
//                 message,
//                 new_value: result,
//                 user_id: userId,
//                 organization_id: orgId,
//               },
//             });
//             return result;
//           },
//           async delete({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const result = await query(args);
//             await (basePrisma as PrismaClient).auditLog.create({
//               data: {
//                 ref_id: (result as any).id,
//                 event: "delete",
//                 entity: model,
//                 message,
//                 old_value: result,
//                 user_id: userId,
//                 organization_id: orgId,
//               },
//             });
//             return result;
//           },
//         },
//       },
//     });
// }

// function getDiff(oldObj: object, newObj: object) {
//   const results = detailedDiff(oldObj, newObj);
//   results.deleted = updatedDiff(results.deleted, oldObj);
//   const updatedOriginal = updatedDiff(results.updated, oldObj);
//   return { ...results, updatedOriginal };
// }

// type AuditLogCreatePayload = {
//   entity: string;
//   ref_id: string;
//   organization_id: string;
//   message?: string | null;
//   event?: string | null;
//   user_id?: string | null;
//   new_value?: unknown;
//   old_value?: unknown;
//   data?: unknown[] | null;
// };

// export async function auditLog(payload: AuditLogCreatePayload) {
//   // Skip in Edge Runtime
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     console.warn("Audit logging skipped in Edge Runtime");
//     return;
//   }

//   // Use the base prisma client directly to avoid extension conflicts
//   await (basePrisma as PrismaClient).auditLog.create({
//     data: {
//       entity: payload.entity,
//       ref_id: payload.ref_id,
//       organization_id: payload.organization_id,
//       message: payload.message ?? null,
//       event: payload.event ?? null,
//       user_id: payload.user_id ?? null,
//       new_value: (payload.new_value as any) ?? undefined,
//       old_value: (payload.old_value as any) ?? undefined,
//       data: (payload.data as any) ?? undefined,
//     },
//   });
// }

// /**
//  * Alternative audit strategy: Stores audit trail in JSONB field within records.
//  * Used when:
//  * - Need per-record audit history
//  * - Faster access to audit trail
//  * - Non-critical audit requirements
//  */
// export function otherDataAuditLog(
//   optional_message?: string,
//   optional_event_name?: string,
// ) {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async update({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const payload = JSON.parse(JSON.stringify(args.data));
//             const where = args.where as { id: string };
//             if (!where.id) {
//               throw Error(
//                 "update operation with audit logger requires the 'where' clause to have the 'id' property",
//               );
//             }
//             const existing = await (
//               basePrisma[camelCase(model) as keyof PrismaClient] as any
//             ).findUnique({ where });
//             if (!existing) {
//               throw new HttpError(404, "data to be updated is not found", true);
//             }
//             const new_audit_log: OtherDataAuditLog = {
//               id: createId(),
//               data: payload,
//               message: optional_message || "",
//               event_name: (optional_event_name || `${model} update`)
//                 .toLocaleLowerCase()
//                 .replace(/ /g, "_"),
//               date: new Date(),
//             };
//             const existing_other_data = existing.other_data as OtherData;
//             let auditLog: OtherDataAuditLog[] = [];
//             try {
//               if (
//                 existing_other_data?.audit_log &&
//                 Array.isArray(existing_other_data?.audit_log)
//               ) {
//                 auditLog = [...existing_other_data?.audit_log, new_audit_log];
//               } else {
//                 // other_data.audit_log is null, so insert the first one.
//                 auditLog.push(new_audit_log);
//               }
//               const result = await query(args);
//               await (
//                 basePrisma[camelCase(model) as keyof PrismaClient] as any
//               ).update({
//                 where: {
//                   id: where.id,
//                 },
//                 data: {
//                   other_data:
//                     existing_other_data === undefined
//                       ? { audit_log: auditLog }
//                       : { ...existing_other_data, audit_log: auditLog },
//                 },
//               });
//               return result;
//             } catch (error) {
//               throw Error(JSON.stringify({ status: "error", message: error }));
//             }
//           },
//         },
//       },
//     });
// }

// /**
//  * Audit metadata extension tracks:
//  * - User ID of modifier
//  * - Exact modification timestamp
//  * - Action type (create/update)
//  * - Full data snapshot
//  */
// export function withAuditMetadataLogging({ userId }: { userId: string }) {
//   // In Edge Runtime, return a simple extension function that just passes through
//   if (typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge") {
//     return (client: any) => client;
//   }

//   return (client: PrismaClient) =>
//     client.$extends({
//       query: {
//         $allModels: {
//           async update({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const where = args.where as { id: string };
//             if (!where.id) {
//               throw Error(
//                 "update operation with this extension requires the 'where' clause to have the 'id' property",
//               );
//             }
//             const existing = await (
//               basePrisma[camelCase(model) as keyof PrismaClient] as any
//             ).findFirst({ where });
//             if (!existing) {
//               throw new HttpError(404, "data to be updated is not found", true);
//             }
//             const audit_metadata = existing.audit_metadata as AuditMetadata[];
//             const new_audit_metadata = {
//               userId: userId,
//               actionDate: new Date(),
//               action: "update",
//               data: args.data,
//             } as AuditMetadata;
//             audit_metadata.push(new_audit_metadata);
//             await query(args);
//             const updatedResult = await (
//               basePrisma[camelCase(model) as keyof PrismaClient] as any
//             ).update({
//               where: {
//                 id: where.id,
//               },
//               data: {
//                 audit_metadata: audit_metadata,
//               },
//             });
//             return updatedResult;
//           },
//           async createMany({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const result = (await query(args)) as any;
//             return result;
//           },
//           async upsert({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const result = (await query(args)) as any;
//             return result;
//           },
//           async create({
//             model,
//             args,
//             query,
//           }: {
//             model: any;
//             args: any;
//             query: any;
//           }) {
//             const audit_metadata = {
//               userId: userId,
//               actionDate: new Date(),
//               action: "create",
//               data: args.data,
//             };
//             const result = (await query(args)) as any;
//             const updatedResult = await (
//               basePrisma[camelCase(model) as keyof PrismaClient] as any
//             ).update({
//               where: {
//                 id: result?.id,
//               },
//               data: {
//                 audit_metadata: [audit_metadata],
//               },
//             });
//             return updatedResult;
//           },
//         },
//       },
//     });
// }
