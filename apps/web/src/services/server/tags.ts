import "server-only";
import { guardedPrisma, prisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

/**
 * Returns all the tags related to the organization
 * @param orgId - organization id
 * @param userId - user id
 */

export async function getAllTags(orgId: string, userId: string) {
  return await guardedPrisma({ orgId, userId })
    .tag.findMany({
      where: {
        organization_id: orgId,
      },
      select: {
        id: true,
        created_by: true,
        updated_by: true,
        created_at: true,
        name: true,
        count: true,
      },
    })
    .then((res) =>
      res.map(({ id, name, count, created_by, created_at }) => ({
        _id: id,
        name,
        count,
        created_by_image: created_by.image,
        created_by_name: created_by.name,
        created_by_email: created_by.email,
        created_at,
      })),
    );
}

interface CreateTag {
  tags: { id: string; name: string }[];
  org: string;
  user: string;
  fileId?: string;
  kbId?: string;
  db: PrismaClient;
}

/**
 * Tag creation for knowledge base
 * @param org - org id
 * @param user - user id
 * @param kbId - Knowledge base id
 * @param tags - incoming tags which is an array
 * @param db - prisma client
 */
export async function createTagKb({ org, user, kbId, tags, db }: CreateTag) {
  const kb = await db.knowledgeBase.findFirst({
    where: {
      id: kbId,
    },
  });

  if (!kb) throw Error("File does not exists");

  // Remove tags that are associated with the knowledge base but not in the incoming list
  const tagsToRemove = kb.tags.filter(
    (kbId) => !tags.some((tag) => tag.id === kbId),
  );

  if (tagsToRemove.length > 0) {
    // We want to check here if the tags associated to the kb that we are removing has count value of 0 or less
    // if it is then we simply delete it else we decrement.
    await Promise.all(
      tagsToRemove.map(async (tag) => {
        const t = await db.tag.findFirst({
          where: {
            id: tag,
            organization_id: org,
          },
        });

        if (!t) throw Error("Tag does not exists");

        await db.tag.update({
          where: {
            id: tag,
            organization_id: org,
          },
          data: {
            count: {
              decrement: 1,
            },
          },
        });
      }),
    );

    await db.knowledgeBase.update({
      where: {
        id: kbId,
        organization_id: org,
      },
      data: {
        tags: {
          set: kb.tags.filter((tagId) => !tagsToRemove.includes(tagId)),
        },
      },
    });
  }

  const updatedKb = await Promise.all(
    tags.map(async (tag) => {
      // Checks if the tag already exists within the organization
      const tagExists = await db.tag.findFirst({
        where: {
          id: tag.id,
          organization_id: org,
        },
      });

      // If tag doesn't exists we create the tag and append the id to the file tags
      if (!tagExists) {
        await db.tag.create({
          data: {
            id: tag.id,
            name: tag.name,
            count: 1,
            created_by_id: user,
            updated_by_id: user,
            organization_id: org,
            created_at: new Date(),
          },
        });

        const updateKb = await db.knowledgeBase.update({
          where: {
            id: kbId,
            organization_id: org,
          },
          data: {
            tags: {
              push: tag.id,
            },
          },
        });
        return updateKb;
      }

      // else if it exists and its not associated with the file we update and append the tag on the file tags.
      if (tagExists && !kb.tags.includes(tag.id)) {
        await db.tag.update({
          where: {
            id: tag.id,
          },
          data: {
            count: {
              increment: 1,
            },
          },
        });

        const updateKb = await db.knowledgeBase.update({
          where: {
            id: kbId,
            organization_id: org,
          },
          data: {
            tags: {
              push: tag.id,
            },
          },
        });

        return updateKb;
      }
    }),
  );

  return updatedKb;
}

/**
 * Tag creation for knowledge base
 * @param org - org id
 * @param user - user id
 * @param kbId - Knowledge base id
 * @param tags - incoming tags which is an array
 * @param db - prisma client
 */

export async function createTagFile({
  org,
  user,
  fileId,
  tags,
  db,
}: CreateTag) {
  const file = await db.file.findFirst({
    where: {
      id: fileId,
    },
  });

  if (!file) throw Error("File does not exists");

  // Remove tags that are associated with the file but not in the incoming list
  const tagsToRemove = file.tags.filter(
    (tagId) => !tags.some((tag) => tag.id === tagId),
  );

  if (tagsToRemove.length > 0) {
    // We want to check here if the tags associated to the file that we are removing has count value of 0 or less
    // if it is then we simply delete it else we decrement.
    await Promise.all(
      tagsToRemove.map(async (tag) => {
        const t = await db.tag.findFirst({
          where: {
            id: tag,
            organization_id: org,
          },
        });

        if (!t) throw Error("Tag does not exists");

        await db.tag.update({
          where: {
            id: tag,
            organization_id: org,
          },
          data: {
            count: {
              decrement: 1,
            },
          },
        });
      }),
    );

    await db.file.update({
      where: {
        id: fileId,
        organization_id: org,
      },
      data: {
        tags: {
          set: file.tags.filter((tagId) => !tagsToRemove.includes(tagId)),
        },
      },
    });
  }

  const updatedFiles = await Promise.all(
    tags.map(async (tag) => {
      // Checks if the tag already exists within the organization
      const tagExists = await db.tag.findFirst({
        where: {
          id: tag.id,
          organization_id: org,
        },
      });

      // If tag doesn't exists we create the tag and append the id to the file tags
      if (!tagExists) {
        await db.tag.create({
          data: {
            id: tag.id,
            name: tag.name,
            count: 1,
            created_by_id: user,
            updated_by_id: user,
            organization_id: org,
            created_at: new Date(),
          },
        });

        const file = await db.file.update({
          where: {
            id: fileId,
            organization_id: org,
          },
          data: {
            tags: {
              push: tag.id,
            },
          },
        });
        return file;
      }

      // else if it exists and its not associated with the file we update and append the tag on the file tags.
      if (tagExists && !file.tags.includes(tag.id)) {
        await db.tag.update({
          where: {
            id: tag.id,
          },
          data: {
            count: {
              increment: 1,
            },
          },
        });

        const file = await db.file.update({
          where: {
            id: fileId,
            organization_id: org,
          },
          data: {
            tags: {
              push: tag.id,
            },
          },
        });

        return file;
      }
    }),
  );

  return updatedFiles;
}

/**
 * Tag deletion
 * @param id - tag id
 * @param org - org id
 * @param user - user id
 * @param db - prisma client
 * Assuming that the count is 0 and there is no associated file or kb then we can safely delete the tag
 */
export async function deleteTag({
  id,
  org,
  db,
  user,
}: {
  user: string;
  id: string;
  org: string;
  db: PrismaClient;
}) {
  const userExists = await prisma.user.findFirst({
    where: {
      id: user,
    },
  });

  if (!userExists) throw Error("User does not exists");

  // Checks if user exists in the organization & has perms.
  const userIsMember = await db.member.findFirst({
    where: {
      organization_id: org,
      user_id: user,
    },
  });

  if (!userIsMember) throw Error("User is not a member of the organization");

  const userHasPermission =
    userIsMember.role_id === "owner" || userIsMember.role_id === "admin";

  if (userHasPermission) {
    const tagExists = db.tag.findFirst({
      where: {
        id: id,
        organization_id: org,
      },
    });

    // This could either means that the tag have been deleted OR it doesnt exists.
    if (!tagExists) throw Error("Tag does not exists");

    const deleted = await db.tag.delete({
      where: {
        id: id,
        organization_id: org,
      },
    });

    return deleted;
  } else {
    throw Error("User does not have permission");
  }
}

/**
 * Tag creation for tag manager in settings
 * @param org - org id
 * @param user - user id
 * @param tags - incoming tags which is an array
 * @param db - prisma client
 */

export async function createTagSettings({ org, user, tags, db }: CreateTag) {
  const createdTags = await Promise.all(
    tags.map(async (tag) => {
      // check if the tag exists within the org
      const tagExists = await db.tag.findFirst({
        where: {
          id: tag.id,
          organization_id: org,
        },
      });

      if (tagExists) throw Error("Tag already exists");

      // if tag doesn't exist, create tag
      if (!tagExists) {
        await db.tag.create({
          data: {
            id: tag.id,
            name: tag.name,
            count: 0,
            created_by_id: user,
            updated_by_id: user,
            organization_id: org,
            created_at: new Date(),
          },
        });
        return { id: tag.id, name: tag.name, count: 0 };
      }
    }),
  );
  return createdTags;
}
