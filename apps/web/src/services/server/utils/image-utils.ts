import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;
import {
  ALLOWED_IMAGE_EXTENSIONS,
  MIME_TYPES,
  type AllowedExtension,
} from "../common-server-actions/file-image-constants";

/**
 * Utility to validate and fetch image files
 * This doesn't use 'use server' directive so it can be used in API routes
 */
export async function validateAndFetchImage(fileId: string, orgId: string) {
  // Retrieve file information with minimal fields
  const file = await prisma.file.findFirst({
    where: {
      id: fileId,
      organization_id: orgId,
    },
    select: {
      buffer_file: true,
      path: true,
      name: true,
    },
  });

  // Check if file exists and has content
  if (!file || !file.buffer_file) {
    return {
      success: false as const,
      error: "File not found",
      status: 404,
    };
  }

  // Get the file extension and check if it's an image
  const path = file.path || file.name || "";
  const extension = path.substring(path.lastIndexOf(".")).toLowerCase();

  // Check if extension is in allowed list
  if (!ALLOWED_IMAGE_EXTENSIONS.includes(extension as AllowedExtension)) {
    return {
      success: false as const,
      error: "File is not an image",
      status: 400,
      extension,
    };
  }

  // Get the buffer and content type
  const buffer = Buffer.from(file.buffer_file);
  const contentType = MIME_TYPES[extension as AllowedExtension];

  return {
    success: true as const,
    buffer,
    contentType,
    size: buffer.length,
    filename: file.name,
    path: file.path,
  };
}
