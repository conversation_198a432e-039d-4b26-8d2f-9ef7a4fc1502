import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Service class to handle retrieving modules for organizations.
 *
 * This class follows the new approach for retrieving modules:
 * organization → organization_subscription → subscription → product → product_modules → modules
 *
 * It maintains backward compatibility by still supporting the direct modules field
 * on the organization table which will be deprecated.
 *
 * @example
 * // Getting modules for an organization
 * const modules = await OrganizationModuleService.getModulesForOrganization("org-123");
 * console.log(modules); // ["policy-management", "risk-assessment", ...]
 *
 * // Updating modules on an organization (for backward compatibility)
 * await OrganizationModuleService.updateOrganizationModules("org-123", ["policy-management", "risk-assessment"]);
 */
export class OrganizationModuleService {
  /**
   * Retrieves all modules for an organization.
   *
   * This function has been optimized to reduce database calls by:
   * 1. Allowing callers to skip the organization existence check when they already know the organization exists
   * 2. Accepting existing modules directly to avoid fetching them from the database again
   *
   * These optimizations significantly reduce database load during organization loading.
   *
   * @param organizationId - The ID of the organization
   * @param options - Optional parameters
   * @param options.skipExistenceCheck - If true, skips checking if the organization exists (default: false)
   * @param options.existingModules - If provided, uses these modules instead of fetching them from the database
   * @returns An array of module names
   */
  public static async getModulesForOrganization(
    organizationId: string,
    options?: {
      skipExistenceCheck?: boolean;
      existingModules?: string[];
    },
  ): Promise<string[]> {
    try {
      let organization: { id: string; modules: string[] | null } | null = null;

      // Use existing modules if provided
      if (options?.existingModules !== undefined) {
        organization = {
          id: organizationId,
          modules: options.existingModules,
        };
      }
      // Skip existence check if requested
      else if (options?.skipExistenceCheck) {
        organization = {
          id: organizationId,
          modules: null, // We'll fetch modules from subscriptions
        };
      }
      // Otherwise, check if the organization exists
      else {
        organization = await prisma.organization.findUnique({
          where: { id: organizationId },
          select: {
            id: true,
            modules: true,
          },
        });

        if (!organization) {
          logger.warn(
            { organizationId },
            "Organization not found when retrieving modules",
          );
          return [];
        }
      }

      // Get organization's subscriptions and associated products and modules
      const organizationSubscriptions =
        await prisma.organizationSubscription.findMany({
          where: {
            organization_id: organizationId,
            canceled_at: null,
          },
          include: {
            subscription_tier: {
              include: {
                subscription: {
                  include: {
                    product: {
                      include: {
                        product_modules: {
                          include: {
                            module: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        });

      // Check if there are no active subscriptions
      if (organizationSubscriptions.length === 0) {
        logger.info(
          { organizationId },
          "No active subscriptions found for organization",
        );
        return organization.modules || [];
      }

      // Check if organization has an enterprise subscription
      const hasEnterpriseSubscription = organizationSubscriptions.some(
        (sub) =>
          sub.is_enterprise === true ||
          sub.subscription_tier?.subscription?.product?.name ===
            "Enterprise Suite",
      );

      let moduleNames: string[] = [];

      // If enterprise subscription exists, get all available modules
      if (hasEnterpriseSubscription) {
        logger.info(
          { organizationId },
          "Organization has enterprise subscription, retrieving all modules",
        );

        const allModules = await prisma.module.findMany({
          select: {
            name: true,
          },
        });

        moduleNames = allModules.map((mod) => mod.name);
      } else {
        // Otherwise, collect modules from all active subscriptions
        logger.info(
          { organizationId },
          "Retrieving modules from active subscriptions",
        );

        // Set to track unique module names
        const uniqueModuleNames = new Set<string>();

        // Process each subscription
        for (const subscription of organizationSubscriptions) {
          const productModules =
            subscription.subscription_tier?.subscription?.product
              ?.product_modules || [];

          // Add module names to the set
          for (const productModule of productModules) {
            if (productModule.module?.name) {
              uniqueModuleNames.add(productModule.module.name);
            }
          }
        }

        moduleNames = Array.from(uniqueModuleNames);
      }

      // Check for organization module overrides
      const moduleOverrides = await prisma.organizationModuleOverride.findMany({
        where: {
          organization_id: organizationId,
        },
        include: {
          organization: true,
        },
      });

      // Apply module overrides
      if (moduleOverrides.length > 0) {
        logger.info({ organizationId }, "Applying module overrides");

        // Process each override
        for (const override of moduleOverrides) {
          // Get the module name from the module ID
          const moduleRecord = await prisma.module.findUnique({
            where: { id: override.module_id },
            select: { name: true },
          });

          if (moduleRecord) {
            if (
              override.is_enabled &&
              !moduleNames.includes(moduleRecord.name)
            ) {
              // Add module if it's enabled but not in the list
              moduleNames.push(moduleRecord.name);
            } else if (!override.is_enabled) {
              // Remove module if it's disabled
              moduleNames = moduleNames.filter(
                (name) => name !== moduleRecord.name,
              );
            }
          }
        }
      }

      // Merge with existing modules for backward compatibility
      if (organization.modules && organization.modules.length > 0) {
        logger.info(
          { organizationId },
          "Merging with existing modules for backward compatibility",
        );

        // Add any modules from the legacy field that aren't already included
        for (const moduleName of organization.modules) {
          if (!moduleNames.includes(moduleName)) {
            moduleNames.push(moduleName);
          }
        }
      }

      // After moduleNames is calculated (before overrides/merging)
      // --- Add dependency resolution here ---
      const allModulesSet = await getAllRequiredModulesFromDb(moduleNames);
      moduleNames = Array.from(allModulesSet);

      return moduleNames;
    } catch (error) {
      logger.error(
        { organizationId, error },
        "Error retrieving modules for organization",
      );
      // In case of an error, try to use the direct modules field as fallback
      try {
        const org = await prisma.organization.findUnique({
          where: { id: organizationId },
          select: { modules: true },
        });
        return org?.modules || [];
      } catch (fallbackError) {
        logger.error(
          { organizationId, fallbackError },
          "Fallback error when retrieving modules",
        );
        return [];
      }
    }
  }

  /**
   * Updates the modules field on an organization.
   * This method is for backward compatibility and should be used
   * when modules are retrieved via the new approach but need to be
   * persisted back to the organization.modules field.
   *
   * @param organizationId - The ID of the organization
   * @param moduleNames - Array of module names to set
   */
  public static async updateOrganizationModules(
    organizationId: string,
    moduleNames: string[],
  ): Promise<void> {
    try {
      await prisma.organization.update({
        where: { id: organizationId },
        data: {
          modules: moduleNames,
        },
      });

      logger.info(
        { organizationId },
        "Successfully updated organization modules",
      );
    } catch (error) {
      logger.error(
        { organizationId, error },
        "Error updating organization modules",
      );
      throw error;
    }
  }
}

// Export a simple function for convenience
export async function getModulesForOrganization(
  organizationId: string,
  options?: {
    skipExistenceCheck?: boolean;
    existingModules?: string[];
  },
): Promise<string[]> {
  return OrganizationModuleService.getModulesForOrganization(
    organizationId,
    options,
  );
}

// Helper function to recursively resolve all required modules from the database
type ModuleDependencyMap = Record<string, string[]>;

async function getAllRequiredModulesFromDb(
  moduleNames: string[],
): Promise<Set<string>> {
  const seen = new Set<string>(moduleNames);
  const toProcess = [...moduleNames];
  const prisma = bypassedPrisma as unknown as PrismaClient;

  // Build a dependency map for all modules in one go for efficiency
  const allDependencies = await prisma.moduleDependency.findMany({
    include: {
      dependent_module: { select: { name: true } },
      required_module: { select: { name: true } },
    },
  });
  const depMap: ModuleDependencyMap = {};
  for (const dep of allDependencies) {
    const from = dep.dependent_module.name;
    const to = dep.required_module.name;
    if (!depMap[from]) depMap[from] = [];
    depMap[from].push(to);
  }

  while (toProcess.length > 0) {
    const current = toProcess.pop();
    if (!current) continue;
    const deps = depMap[current] || [];
    for (const dep of deps) {
      if (!seen.has(dep)) {
        seen.add(dep);
        toProcess.push(dep);
      }
    }
  }
  return seen;
}
