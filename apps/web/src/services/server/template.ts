import { bypassedPrisma, prisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { createOrg } from "./organization";

/**
 * Get all the templates
 */
export async function getAllTemplates({ org }: { org: string }) {
  // Default templates
  const defaultTemplates = await prisma.template.findMany({
    where: {
      organization_id: null,
    },
  });

  const defaultTemplateAllQuestions = await Promise.all(
    defaultTemplates.map(async (temp) => {
      const questions = await prisma.templateQuestions.findMany({
        where: {
          template_id: temp.id,
        },
      });

      return { ...temp, questions: questions };
    }),
  );

  const ownedTemplates = await prisma.template.findMany({
    where: {
      organization_id: org,
    },
  });

  const ownedTemplatesAllQuestions = await Promise.all(
    ownedTemplates.map(async (temp) => {
      const { created_by_id, ...rest } = temp;
      const questions = await prisma.templateQuestions.findMany({
        where: {
          template_id: temp.id,
        },
      });
      const user = await prisma.user.findFirst({
        where: {
          id: created_by_id as string,
        },
      });

      return { ...rest, user: user, questions: questions };
    }),
  );

  return [...defaultTemplateAllQuestions, ...ownedTemplatesAllQuestions];
}

export async function copyTemplateAndQuestions({
  id,
  user,
  org,
  email,
}: {
  email: string;
  id: string;
  org: string;
  user: string;
}) {
  const template = await prisma.template.findFirst({
    where: {
      id: id,
    },
  });

  if (!template) {
    throw Error("Template does not exists");
  }

  const questions = await prisma.templateQuestions.findMany({
    where: {
      template_id: template.id,
    },
  });

  if (!questions) {
    throw Error(`Questions on ${template.name} does not exists.`);
  }

  // Get the default Organization of the user;
  const defaultOrg = await extendedPrisma.userProfile.findFirst({
    where: {
      user_id: user,
    },
    select: {
      settings: true,
    },
  });

  let orgId: string | undefined = undefined;

  // If there is no default organization we create a new one, note that this applies to new users only
  if (defaultOrg && defaultOrg.settings === null) {
    const org = await createOrg({
      userId: user,
      orgName: email?.split("@")[0] || "",
    });
    orgId = org.id;
  } else if (defaultOrg && defaultOrg.settings) {
    const { defaultOrganizationId } = defaultOrg.settings as {
      defaultOrganizationId: string;
    };
    orgId = defaultOrganizationId;
  }
}
