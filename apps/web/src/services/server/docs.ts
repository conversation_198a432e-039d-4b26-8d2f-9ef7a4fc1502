"use server";
import { tenantGuardedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Shared function to transform file data
async function transformFileData(file: any, org: string, db: PrismaClient) {
  // Process OpenAI file tags - check for both agent and copilot tags
  const isAgentFile = file.tags.some((tag: string) =>
    tag.startsWith("openai_files:agent:"),
  );
  const isCopilotFile = file.tags.some((tag: string) =>
    tag.startsWith("openai_files:copilot:"),
  );

  // Transform tags - exclude all OpenAI file tags
  const transformedTags = await Promise.all(
    file.tags
      .filter((tag: string) => !tag.startsWith("openai_files:"))
      .map(async (tag: string) => {
        const t = await db.tag.findFirst({
          where: {
            id: tag,
            organization_id: org,
          },
        });

        if (!t) throw Error("Tag does not exists");
        return { value: tag, label: t.name };
      }),
  );

  // Return transformed data
  return {
    id: file.id,
    email: file.created_by.email,
    name: file.name,
    path: file.path,
    document_type: file.document_type,
    category_id: file.category_id,
    created_by: {
      image: file.created_by.image,
      name: file.created_by.name || null,
      email: file.created_by.email || null,
      id: file.created_by.id || null,
    },
    created_by_id: file.created_by_id || null,
    uploaded: file.created_at,
    last_trained: file.trained_at,
    tags: file.tags.length >= 1 ? transformedTags : [],
    scope_id: file.scope_id,
    scope: { label: file.scope?.name, value: file.scope_id },
    is_openai_file: isAgentFile,
    is_copilot_file: isCopilotFile,
    is_uploaded: file.path !== null,
    content: file.content || "",
    content_change_history: file.content_change_history || null,
    access_level: file.access_level || null,
  };
}

// Shared Prisma select object
const fileSelect = {
  id: true,
  name: true,
  path: true,
  document_type: true,
  created_at: true,
  trained_at: true,
  created_by_id: true,
  created_by: {
    select: {
      email: true,
      image: true,
      name: true,
      id: true,
    },
  },
  category_id: true,
  tags: true,
  scope_id: true,
  scope: {
    select: {
      id: true,
      name: true,
    },
  },
  content: true,
  content_change_history: true,
  access_level: true,
} as const;

async function getSingleDocFile(id: string, org: string) {
  const db = tenantGuardedPrisma(org);
  const file = await db.file.findUnique({
    where: { id, organization_id: org },
    select: fileSelect,
  });

  if (!file) return null;
  return transformFileData(file, org, db);
}

/**
 *
 * @param org Organization Id
 * @returns All documents / files under specific organization
 */

async function getDocs(org: string, db: PrismaClient | null = null) {
  let dbPrisma = db;
  if (db === null) {
    dbPrisma = tenantGuardedPrisma(org);
  }

  if (!dbPrisma) throw Error("Database is null");

  const files = await dbPrisma.file.findMany({
    where: {
      organization_id: org,
      OR: [
        {
          document_type: {
            in: ["policy", "procedure", "snippet", "other"],
          },
        },
        {
          document_type: {
            equals: null,
          },
        },
      ],
    },
    select: fileSelect,
    orderBy: {
      name: "asc",
    },
  });

  return Promise.all(
    files.map((file) => transformFileData(file, org, dbPrisma)),
  );
}

/**
 *
 * @param id - File Id
 * @param org Organization Id
 * @returns All documents / files under specific organization
 */
async function getDocContents(id: string, org: string) {
  const res = await tenantGuardedPrisma(org).file.findUnique({
    where: { id, organization_id: org },
    select: { documents: true },
  });
  return res?.documents;
}

/**
 *
 * @param id - File Id
 * @param org - Organization Id
 * @param categoryId - Category Id
 * @param userId - User Id
 * @param documentType - Type id
 *
 * @returns Updated document / file
 */

interface DocsParams {
  org: string;
  file: string;
  train?: boolean;
  category?: string;
  type?: string;
  user?: string;
  db?: PrismaClient | null;
}

async function updateDoc({
  org,
  file,
  category,
  type,
  user,
  train,
  scope_id,
  db: dbParam,
}: DocsParams & { scope_id: string }) {
  const db = dbParam || tenantGuardedPrisma(org);
  const existingFile = await db.file.findFirst({
    where: {
      id: file,
      organization_id: org,
    },
  });

  if (!existingFile) throw Error("File does not exists");

  const updateFile = await db.file.update({
    where: {
      id: file,
      organization_id: org,
    },
    data: {
      category_id: category,
      document_type: type,
      created_by_id: user,
      trained_at: train ? new Date() : existingFile.trained_at,
      updated_at: new Date(),
      scope_id: scope_id,
    },
  });

  return updateFile;
}

/**
 *
 * @param org - organization id
 * @param file - file id
 * @returns deleted file
 */
async function deleteDoc({ org, file, db: dbParam }: DocsParams) {
  const db = dbParam || tenantGuardedPrisma(org);
  const fileExists = await db.file.findFirst({
    where: {
      id: file,
      organization_id: org,
    },
    select: {
      id: true,
      document_type: true,
    },
  });

  if (!fileExists) throw Error("File does not exists");

  // If it's a policy document, handle training group relationships
  if (fileExists.document_type === "policy") {
    // Find all training groups containing this policy
    const trainingGroups = await db.trainingGroup.findMany({
      where: {
        organization_id: org,
        policy_ids: {
          hasSome: [file],
        },
      },
    });

    // Handle in a transaction to ensure consistency
    await db.$transaction(async (tx) => {
      // 1. Update training groups to remove this policy ID
      for (const group of trainingGroups) {
        await tx.trainingGroup.update({
          where: { id: group.id },
          data: {
            policy_ids: {
              set: group.policy_ids.filter((id) => id !== file),
            },
          },
        });
      }

      // 2. Delete related user policy acceptance records
      await tx.userPolicyAcceptanceTracking.deleteMany({
        where: {
          policy_id: file,
          training_group_id: {
            in: trainingGroups.map((g) => g.id),
          },
        },
      });
    });
  }

  // Finally delete the file
  const deletedFile = await db.file.delete({
    where: {
      id: file,
      organization_id: org,
    },
  });

  return deletedFile;
}

export { getDocs, getDocContents, updateDoc, deleteDoc, getSingleDocFile };
