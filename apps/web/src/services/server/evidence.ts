import { PrismaClient } from "@askinfosec/database";
import { ParsedPath } from "path";
import { saveFileContent } from "./file";
import { tenantGuardedPrisma } from "@/lib/prisma";

interface GetAllEvidenceParams {
  id: string;
  org: string;
  type: string;
}

export async function getAllEvidenceFiles({
  org,
  id,
  type,
}: GetAllEvidenceParams) {
  if (type === "kb") {
    const queryAllEvidence = await tenantGuardedPrisma(org).evidence.findMany({
      where: {
        knowledgeBaseId: id,
      },
      select: {
        file: {
          select: {
            id: true,
            name: true,
            path: true,
            // buffer_file: true,
            created_at: true,
            created_by_id: true,
            created_by: { select: { image: true, name: true, email: true } },
            document_type: true,
          },
        },
        note: true,
        id: true,
      },
    });

    // Filters the files based on their type which is expected to be evidence.
    const evidencesFiltered = queryAllEvidence
      .filter((evidence) => evidence.file.document_type === "evidence")
      .map((e) => {
        const file = e.file;
        return {
          id: e.id,
          name: file.name,
          path: file.path,
          // buffer_file: file.buffer_file,
          created_at: file.created_at,
          created_by: {
            name: file.created_by?.name,
            email: file.created_by?.email,
            image: file.created_by?.image,
          },
          note: e.note,
          fileId: file.id,
        };
      });

    return evidencesFiltered;
  }

  if (type === "questionnaire") {
    const queryAllEvidence = await tenantGuardedPrisma(org).evidence.findMany({
      where: {
        questionId: id,
      },
      select: {
        file: {
          select: {
            id: true,
            name: true,
            path: true,
            // buffer_file: true,
            created_at: true,
            created_by_id: true,
            created_by: { select: { image: true, name: true, email: true } },
            document_type: true,
          },
        },
        note: true,
        id: true,
      },
    });

    // Filters the files based on their type which is expected to be evidence.
    const evidencesFiltered = queryAllEvidence
      .filter((evidence) => evidence.file.document_type === "evidence")
      .map((e) => {
        const file = e.file;
        return {
          name: file.name,
          path: file.path,
          // buffer_file: file.buffer_file,
          created_at: file.created_at,
          created_by: {
            name: file.created_by?.name,
            email: file.created_by?.email,
            image: file.created_by?.image,
          },
          note: e.note,
          id: e.id,
          fileId: file.id,
        };
      });

    return evidencesFiltered;
  }

  return [];
}

interface CreateEvidenceParams {
  org: string;
  user: string;
  db: PrismaClient;
  file: File | Blob;
  filePath: ParsedPath;
  id: string;
  note: string;
  type: string;
}

export async function saveEvidenceFile({
  org,
  user,
  db,
  file,
  filePath,
  id,
  note,
  type,
}: CreateEvidenceParams) {
  const filename = filePath.name.substring(0, 90);

  const queryFile = await db.file.findFirst({
    where: {
      organization_id: org,
      path: `${filename}${filePath.ext}`,
    },
  });

  if (queryFile && type === "kb") {
    const queryLinkedEvidenceDoc = await db.evidence.findFirst({
      where: {
        knowledgeBaseId: id,
        fileId: queryFile.id,
      },
    });
    // If the document is already linked we just return it directly.
    if (queryLinkedEvidenceDoc) return queryLinkedEvidenceDoc;

    // If the file already exists, then we update the file type to evidence
    // and  link that file to the knowledge base id.
    await db.file.update({
      where: {
        id: queryFile.id,
        organization_id: org,
      },
      data: {
        document_type: "evidence",
      },
    });
    const linkEvidenceDoc = await db.evidence.create({
      data: {
        knowledgeBaseId: id,
        fileId: queryFile.id,
        note: note ? note : "",
      },
    });

    return linkEvidenceDoc;
  } else if (queryFile && type === "questionnaire") {
    const queryLinkedEvidenceDoc = await db.evidence.findFirst({
      where: {
        questionId: id,
        fileId: queryFile.id,
      },
    });
    // If the document is already linked we just return it directly.
    if (queryLinkedEvidenceDoc) return queryLinkedEvidenceDoc;

    // If the file already exists, then we update the file type to evidence
    // and  link that file to the knowledge base id.
    await db.file.update({
      where: {
        id: queryFile.id,
        organization_id: org,
      },
      data: {
        document_type: "evidence",
      },
    });
    const linkEvidenceDoc = await db.evidence.create({
      data: {
        questionId: id,
        fileId: queryFile.id,
        note: note ? note : "",
      },
    });

    return linkEvidenceDoc;
  } else {
    const saveFile = await saveFileContent(
      file,
      db,
      org,
      user,
      filePath,
      "evidence",
    );
    if (saveFile && type === "kb") {
      const saveFileToEvidence = await db.evidence.create({
        data: {
          knowledgeBaseId: id,
          fileId: saveFile.file as string,
          note: note ? note : "",
        },
      });

      return saveFileToEvidence;
    }

    if (saveFile && type === "questionnaire") {
      const saveFileToEvidence = await db.evidence.create({
        data: {
          questionId: id,
          fileId: saveFile.file as string,
          note: note ? note : "",
        },
      });

      return saveFileToEvidence;
    }

    throw Error("File is not supported");
  }
}
