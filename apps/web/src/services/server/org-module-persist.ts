import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Persist a computed array of module names back into the organization.modules column.
 *
 * @param orgId - The ID of the organization
 * @param modules - Array of module names to persist
 */
export async function updateOrganizationModulesField(
  orgId: string,
  modules: string[],
): Promise<void> {
  try {
    await prisma.organization.update({
      where: { id: orgId },
      data: { modules },
    });
    logger.info(
      `Persisted organization.modules field: orgId=${orgId}, moduleCount=${modules.length}`,
    );
  } catch (error) {
    logger.error(
      `Error persisting organization.modules field: ${error instanceof Error ? error.message : String(error)}, orgId=${orgId}`,
    );
    throw error;
  }
}
