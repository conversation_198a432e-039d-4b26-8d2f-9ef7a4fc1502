// TypeScript enabled: removed @ts-nocheck. Please ensure all Prisma and DB usages are typed.

import { bypassedPrisma, tenantGuardedPrisma } from "@/lib/prisma";
import { ChallengeType, MfaMethod } from "@/types/mfa-types";
import type { UserAuthenticator, Timestamp } from "@/types/prisma-kysely";
import type { ColumnType } from "kysely";
import { randomBytes } from "crypto";
import { PrismaClient } from "@askinfosec/database";
import {
  getWebAuthnConfig,
  getEffectiveRPID,
  calculateRPIDHash,
  logVerificationParams,
} from "@/lib/webauthn-utils";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();

/**
 * WebAuthn configuration options
 */
interface WebAuthnConfig {
  rpName: string;
  rpID: string;
  origin: string;
  challengeExpirationMinutes: number;
}

/**
 * Default WebAuthn configuration
 */
const defaultConfig: WebAuthnConfig = getWebAuthnConfig();

/**
 * Service for handling WebAuthn (PassKey) operations
 */
export class WebAuthnService {
  private config: WebAuthnConfig;
  private prisma: PrismaClient = bypassedPrisma as unknown as PrismaClient; // TODO: Replace 'any' with proper Prisma type if available

  constructor(config: Partial<WebAuthnConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Generate a random challenge for WebAuthn operations
   * @returns Random challenge string (base64url encoded)
   */
  private generateChallenge(): string {
    return randomBytes(32).toString("base64url");
  }

  /**
   * Create an authentication challenge for a user
   * @param userId User ID
   * @param type Challenge type (registration or authentication)
   * @returns Challenge string
   */
  async createChallenge(userId: string, type: ChallengeType): Promise<string> {
    const challenge = this.generateChallenge();
    const expiresAt = new Date();
    expiresAt.setMinutes(
      expiresAt.getMinutes() + this.config.challengeExpirationMinutes,
    );

    // Use $executeRaw to avoid TypeScript errors
    await this.prisma.$executeRaw`
      INSERT INTO "auth_challenge" (
        "id", "user_id", "challenge", "expires", "used", "type", "created_at"
      ) VALUES (
        ${randomBytes(16).toString("hex")}, ${userId}, ${challenge},
        ${expiresAt}, false, ${type}, NOW()
      )
    `;

    return challenge;
  }

  /**
   * Verifies a challenge for a user
   * @param userId The user ID
   * @param challenge The challenge to verify
   * @param type The type of challenge
   * @returns True if the challenge is valid, false otherwise
   */
  async verifyChallenge(
    userId: string,
    challenge: string,
    type: ChallengeType,
  ): Promise<boolean> {
    try {
      // Find and verify the challenge using $queryRaw
      const challenges = await this.prisma.$queryRaw`
        SELECT * FROM "auth_challenge"
        WHERE "user_id" = ${userId}
        AND "challenge" = ${challenge}
        AND "type" = ${type}
        AND "used" = false
        AND "expires" > NOW()
        LIMIT 1
      `;

      const challengeRecord =
        Array.isArray(challenges) && challenges.length > 0
          ? challenges[0]
          : null;

      if (!challengeRecord) {
        logger.error({ userId, challenge, type }, "No valid challenge found");
        return false;
      }

      // Mark the challenge as used
      try {
        await this.prisma.$executeRaw`
          UPDATE "auth_challenge"
          SET "used" = true
          WHERE "id" = ${challengeRecord.id}
        `;
      } catch (error) {
        logger.error(
          { error, challengeId: challengeRecord.id },
          "Failed to mark challenge as used",
        );
        // Continue even if marking as used fails
      }

      return true;
    } catch (error) {
      logger.error(
        { error, userId, challenge, type },
        "Error verifying challenge",
      );
      return false;
    }
  }

  /**
   * Gets authentication options for MFA verification
   * @param userId The user ID
   * @returns Authentication options for the WebAuthn API
   */
  async getMfaAuthenticationOptions(userId: string) {
    try {
      // Get user's authenticators using $queryRaw
      const authenticators = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM "user_authenticator" WHERE "user_id" = ${userId}
      `;

      // If no authenticators are found, return null
      if (!Array.isArray(authenticators) || authenticators.length === 0) {
        return null;
      }

      // Create a challenge for authentication
      const challenge = await this.createChallenge(
        userId,
        ChallengeType.AUTHENTICATION,
      );

      // Format authenticators for WebAuthn API
      const allowCredentials = authenticators.map((auth) => ({
        id: auth.credentialId,
        type: "public-key",
        transports: auth.transports || undefined,
      }));

      // Generate authentication options
      const options = {
        challenge,
        timeout: 60000, // 1 minute
        userVerification: "preferred",
        allowCredentials,
      };

      return options;
    } catch (error) {
      logger.error(
        { error, userId },
        "Error generating MFA authentication options",
      );
      return null;
    }
  }

  /**
   * Marks MFA as verified for a user's session
   * @param userId The user ID
   * @returns True if successful, false otherwise
   */
  async markMfaVerified(userId: string): Promise<boolean> {
    try {
      // Update the user's session to mark MFA as verified
      // In a real implementation, this would update the session in the database
      // For this example, we'll just return true
      return true;
    } catch (error) {
      logger.error({ error, userId }, "Error marking MFA as verified");
      return false;
    }
  }

  /**
   * Get WebAuthn registration options
   * @param userId User ID
   * @returns Registration options for the WebAuthn client
   */
  async getRegistrationOptions(userId: string) {
    function toBase64Url(buf: Buffer): string {
      return buf
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=+$/, "");
    }
    // Get user information using $queryRaw
    const users = await this.prisma.$queryRaw`
      SELECT * FROM "user" WHERE "id" = ${userId} LIMIT 1
    `;

    const user = Array.isArray(users) && users.length > 0 ? users[0] : null;

    if (!user) {
      throw new Error("User not found");
    }

    // Get existing authenticators for this user using $queryRaw
    const authenticators = await this.prisma.$queryRaw`
      SELECT * FROM "user_authenticator" WHERE "user_id" = ${userId}
    `;

    const challenge = await this.createChallenge(
      userId,
      ChallengeType.REGISTRATION,
    );

    // Log RP ID configuration for debugging
    logVerificationParams({
      operation: "getRegistrationOptions",
      userId,
      expectedRPID: this.config.rpID,
      expectedOrigin: this.config.origin,
      expectedChallenge: challenge,
    });

    // Format existing authenticators for exclusion
    const excludeCredentials = Array.isArray(authenticators)
      ? authenticators.map((authenticator) => ({
          id: toBase64Url(Buffer.from(authenticator.credentialId, "base64url")),
          type: "public-key",
        }))
      : [];

    // IMPORTANT: Do not double-encode the challenge. Return as-is to match DB and verification.
    return {
      rp: {
        name: this.config.rpName,
        id: this.config.rpID,
      },
      user: {
        id: toBase64Url(Buffer.from(userId)), // Convert to base64url string to avoid ArrayBuffer issues
        name: user.email || userId, // Use email if available
        displayName: user.name || user.email || userId,
      },
      challenge,
      pubKeyCredParams: [
        { type: "public-key", alg: -7 }, // ES256 (WebAuthn's default)
        { type: "public-key", alg: -257 }, // RS256 (for wider compatibility)
      ],
      timeout: 60000,
      attestation: "none", // 'none' to avoid security key requirement
      excludeCredentials, // Include existing credentials to prevent duplicate registration
      authenticatorSelection: {
        authenticatorAttachment: "platform", // Force platform authenticator (Touch ID, Windows Hello)
        requireResidentKey: true, // Enable discoverable credentials
        residentKey: "required", // Ensure discoverable credentials are used
        userVerification: "preferred",
      },
      // Add support for discoverable credentials and better UX
      extensions: {
        credProps: true,
        hmacCreateSecret: true,
      },
    };
  }

  /**
   * Get WebAuthn authentication options. This fetch the user_authenticator table from the database.
   * @param userId User ID
   * @returns Authentication options for the WebAuthn client
   */
  async getAuthenticationOptions(email: string) {
    // Get user information using $queryRaw
    const users = await this.prisma.$queryRaw`
      SELECT * FROM "user" WHERE "email" = ${email} LIMIT 1
    `;

    const user = Array.isArray(users) && users.length > 0 ? users[0] : null;

    if (!user) {
      throw new Error("User not found");
    }

    // Get existing authenticators for this user using $queryRaw
    const authenticators = await this.prisma.$queryRaw`
      SELECT * FROM "user_authenticator" WHERE "user_id" = ${user.id}
    `;

    if (
      !authenticators ||
      !Array.isArray(authenticators) ||
      authenticators.length === 0
    ) {
      throw new Error("No authenticators registered for this user");
    }

    // Create a new challenge for this authentication attempt
    const challenge = await this.createChallenge(
      user.id,
      ChallengeType.AUTHENTICATION,
    );

    // Format existing authenticators, skip those without credentialId
    const allowCredentials = [];

    for (const authenticator of authenticators) {
      if (!authenticator.credential_id) continue;

      // Handle different credential ID formats
      let credentialId = authenticator.credential_id;

      // If it's a Buffer, convert to base64url
      if (Buffer.isBuffer(credentialId)) {
        credentialId = credentialId
          .toString("base64")
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");
      }
      // If it's a string but not in base64url format, convert it
      else if (
        typeof credentialId === "string" &&
        !/^[A-Za-z0-9_-]+$/.test(credentialId)
      ) {
        credentialId = Buffer.from(credentialId, "base64")
          .toString("base64")
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");
      }

      // Only include the credential ID if it's valid base64url
      if (
        typeof credentialId === "string" &&
        /^[A-Za-z0-9_-]+$/.test(credentialId)
      ) {
        allowCredentials.push({
          id: credentialId,
          type: "public-key",
          transports: authenticator.transports
            ? authenticator.transports.split(",")
            : undefined,
        });
      }
    }

    if (allowCredentials.length === 0) {
      throw new Error("No valid authenticators found for this user");
    }

    return {
      challenge,
      rpId: this.config.rpID,
      allowCredentials,
      timeout: 60000,
      userVerification: "preferred",
      // Force platform authenticator usage (Touch ID, Windows Hello)
      authenticatorAttachment: "platform",
    };
  }

  /**
   * Enable MFA for a user
   * @param userId User ID
   * @param method MFA method
   * @param orgId Optional organization ID
   */
  async enableMfa(
    userId: string,
    method: MfaMethod,
    orgId?: string,
  ): Promise<void> {
    // Update user MFA settings using $executeRaw
    if (orgId) {
      // Use tenant-guarded Prisma if orgId is provided
      const prisma = tenantGuardedPrisma(orgId);
      await prisma.user.update({
        where: { id: userId },
        data: {
          mfaEnabled: true,
          defaultMfaMethod: method,
        },
      });
    } else {
      // Use bypassed Prisma if no orgId is provided (for backward compatibility)
      await this.prisma.$executeRaw`
        UPDATE "user"
        SET "mfa_enabled" = true, "default_mfa_method" = ${method}
        WHERE "id" = ${userId}
      `;
    }
  }

  /**
   * Disable MFA for a user
   * @param userId User ID
   * @param orgId Optional organization ID
   */
  async disableMfa(userId: string, orgId?: string): Promise<void> {
    // Update user MFA settings using $executeRaw
    if (orgId) {
      // Use tenant-guarded Prisma if orgId is provided
      const prisma = tenantGuardedPrisma(orgId);
      await prisma.user.update({
        where: { id: userId },
        data: {
          mfaEnabled: false,
          defaultMfaMethod: null,
        },
      });
    } else {
      // Use bypassed Prisma if no orgId is provided (for backward compatibility)
      await this.prisma.$executeRaw`
        UPDATE "user"
        SET "mfa_enabled" = false, "default_mfa_method" = NULL
        WHERE "id" = ${userId}
      `;
    }
  }

  /**
   * Set the default MFA method for a user
   * @param userId User ID
   * @param method MFA method
   * @param orgId Optional organization ID
   */
  async setDefaultMfaMethod(
    userId: string,
    method: MfaMethod,
    orgId?: string,
  ): Promise<void> {
    // Update user MFA settings
    if (orgId) {
      // Use tenant-guarded Prisma if orgId is provided
      const prisma = tenantGuardedPrisma(orgId);
      await prisma.user.update({
        where: { id: userId },
        data: { defaultMfaMethod: method },
      });
    } else {
      // Use bypassed Prisma if no orgId is provided (for backward compatibility)
      await this.prisma.$executeRaw`
        UPDATE "user"
        SET "default_mfa_method" = ${method}
        WHERE "id" = ${userId}
      `;
    }
  }

  /**
   * Get MFA status for a user
   * @param userId User ID
   * @returns MFA status
   */
  async getMfaStatus(userId: string) {
    // Get user information using $queryRaw
    const users = await this.prisma.$queryRaw`
      SELECT * FROM "user" WHERE "id" = ${userId} LIMIT 1
    `;

    const user = Array.isArray(users) && users.length > 0 ? users[0] : null;

    if (!user) {
      throw new Error("User not found");
    }

    // Log the raw user data for debugging
    console.log("Raw user data from database:", {
      userId,
      mfaEnabled: user.mfa_enabled, // Note the snake_case column name
      defaultMfaMethod: user.default_mfa_method,
    });

    // Get user's authenticators using $queryRaw
    const authenticators = await this.prisma.$queryRaw`
      SELECT * FROM "user_authenticator" WHERE "user_id" = ${userId}
    `;

    return {
      enabled: user.mfa_enabled, // Use the correct column name from the database
      method: user.default_mfa_method,
      authenticators: Array.isArray(authenticators)
        ? authenticators.map((auth) => ({
            id: auth.id,
            name: auth.name || "Unnamed authenticator",
            deviceType: auth.credential_device_type, // Use the correct column name
            backedUp: auth.credential_backed_up, // Use the correct column name
            createdAt: auth.created_at, // Use the correct column name
            lastUsed: auth.last_used, // Use the correct column name
          }))
        : [],
    };
  }
  /**
   * Generate authentication options for WebAuthn
   * @param email User's email address
   * @returns Authentication options for the WebAuthn client
   */
  async generateAuthenticationOptions(email: string) {
    return this.getAuthenticationOptions(email);
  }

  /**
   * Verify a WebAuthn authentication response
   * @param email User's email address
   * @param credential The credential from the WebAuthn response
   * @returns User ID if verification is successful, null otherwise
   */
  async verifyAuthenticationCredential(email: string, credential: any) {
    try {
      // Get user by email
      const users = await this.prisma.$queryRaw`
        SELECT * FROM "user" WHERE "email" = ${email} LIMIT 1
      `;

      const user = Array.isArray(users) && users.length > 0 ? users[0] : null;

      if (!user) {
        throw new Error("User not found");
      }

      // Get the challenge from the credential
      const clientDataJSON = credential.response.clientDataJSON;
      const challenge = credential.challenge;

      if (!challenge) {
        throw new Error("No challenge provided in credential");
      }

      // Verify the challenge
      const challengeValid = await this.verifyChallenge(
        user.id,
        challenge,
        ChallengeType.AUTHENTICATION,
      );

      if (!challengeValid) {
        throw new Error("Invalid challenge");
      }

      // Get the authenticator
      // Convert credential ID to base64url for comparison with stored value
      let credentialId = credential.id;
      if (typeof credentialId === "string") {
        // If it's already base64url encoded, use as is
        if (!/^[A-Za-z0-9_-]+$/.test(credentialId)) {
          // If not base64url encoded, convert from base64 to base64url
          credentialId = Buffer.from(credentialId, "base64")
            .toString("base64")
            .replace(/\+/g, "-")
            .replace(/\//g, "_")
            .replace(/=+$/, "");
        }
      } else if (Buffer.isBuffer(credentialId)) {
        credentialId = credentialId
          .toString("base64")
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");
      }

      // Get all authenticators for this user
      const authenticators = await this.prisma.$queryRaw`
        SELECT * FROM "user_authenticator"
        WHERE "user_id" = ${user.id}
      `;

      // Find the matching authenticator by comparing credential IDs
      const authenticator = Array.isArray(authenticators)
        ? authenticators.find((auth) => {
            if (!auth.credential_id) return false;

            let storedId = auth.credential_id;

            // Convert stored ID to base64url if needed
            if (Buffer.isBuffer(storedId)) {
              storedId = storedId
                .toString("base64")
                .replace(/\+/g, "-")
                .replace(/\//g, "_")
                .replace(/=+$/, "");
            } else if (
              typeof storedId === "string" &&
              !/^[A-Za-z0-9_-]+$/.test(storedId)
            ) {
              storedId = Buffer.from(storedId, "base64")
                .toString("base64")
                .replace(/\+/g, "-")
                .replace(/\//g, "_")
                .replace(/=+$/, "");
            }

            return storedId === credentialId;
          })
        : null;

      if (!authenticator) {
        throw new Error("Authenticator not found");
      }

      // Update last used timestamp
      await this.prisma.$executeRaw`
        UPDATE "user_authenticator"
        SET "last_used" = NOW()
        WHERE "id" = ${authenticator.id}
      `;

      return user.id;
    } catch (error) {
      console.error("Error verifying authentication:", error);
      return null;
    }
  }

  /**
   * Get discoverable WebAuthn authentication options (email-less)
   * Returns options for resident keys with no allowCredentials and userVerification: 'required'.
   */
  async getDiscoverableAuthenticationOptions() {
    // Generate a random challenge
    const challenge = this.generateChallenge();
    // Store the challenge in the DB for later verification (no userId, so type is 'discoverable')
    await this.prisma.$executeRaw`
      INSERT INTO "auth_challenge" (
        "id", "challenge", "expires", "used", "type", "created_at"
      ) VALUES (
        ${randomBytes(16).toString("hex")}, ${challenge},
        ${(() => {
          const expiresAt = new Date();
          expiresAt.setMinutes(
            expiresAt.getMinutes() + this.config.challengeExpirationMinutes,
          );
          return expiresAt;
        })()}, false, 'discoverable', NOW()
      )
    `;

    // Log RP ID configuration for debugging
    logVerificationParams({
      operation: "getDiscoverableAuthenticationOptions",
      expectedRPID: this.config.rpID,
      expectedOrigin: this.config.origin,
      expectedChallenge: challenge,
    });

    return {
      challenge,
      rpId: this.config.rpID,
      timeout: 60000,
      userVerification: "required",
      // No allowCredentials means any resident key can be used
    };
  }

  /**
   * Verify a WebAuthn registration response
   * @param email User's email address
   * @param credential The credential from the WebAuthn registration response
   * @returns Verification result with success status and optional error message
   */
  async verifyRegistration(email: string, credential: any) {
    try {
      // Get user by email
      const users = await this.prisma.$queryRaw`
        SELECT * FROM "user" WHERE "email" = ${email} LIMIT 1
      `;

      const user = Array.isArray(users) && users.length > 0 ? users[0] : null;

      if (!user) {
        return { verified: false, error: "User not found" };
      }

      // Verify the challenge
      const challengeValid = await this.verifyChallenge(
        user.id,
        credential.response.clientDataJSON,
        ChallengeType.REGISTRATION,
      );

      if (!challengeValid) {
        return { verified: false, error: "Invalid challenge" };
      }

      // Store the new authenticator
      await this.prisma.$executeRaw`
        INSERT INTO "user_authenticator" (
          "id", "user_id", "credential_id", "public_key", "counter",
          "transports", "device_type", "backup_state", "created_at", "last_used"
        ) VALUES (
          ${randomBytes(16).toString("hex")},
          ${user.id},
          ${credential.id},
          ${credential.response.attestationObject},
          ${credential.response.authenticatorData?.counter || 0},
          ${credential.transports ? credential.transports.join(",") : null},
          ${credential.authenticatorAttachment || "platform"},
          ${credential.response.attestationObject?.backupEligible || false},
          NOW(),
          NOW()
        )
      `;

      return { verified: true };
    } catch (error) {
      console.error("Error verifying registration:", error);
      return {
        verified: false,
        error:
          error instanceof Error
            ? error.message
            : "Unknown error during registration verification",
      };
    }
  }
  /**
   * Register a new PassKey for a user
   * @param orgId Organization ID
   * @param userId User ID
   * @param credentialId Credential ID from WebAuthn
   * @param credentialPublicKey Public key from WebAuthn
   * @param counter Initial counter value
   * @param deviceType Device type (platform, cross-platform)
   * @param backedUp Whether the credential is backed up
   * @param transports Transport methods (usb, nfc, ble, internal)
   * @param name Optional name for the authenticator
   * @param challenge Optional challenge used for verification
   * @returns The created authenticator
   */
  async registerPassKey(
    orgId: string,
    userId: string,
    credentialId: string,
    credentialPublicKey: Buffer,
    counter: number,
    deviceType: string,
    backedUp: boolean,
    transports?: string[],
    name?: string,
    challenge?: string,
  ) {
    // Add detailed logging for debugging
    console.log("registerPassKey called with:", {
      orgId,
      userId,
      credentialId,
      credentialPublicKeyLength: credentialPublicKey?.length,
      counter,
      deviceType,
      backedUp,
      transports,
      name,
      challenge,
      rpID: this.config.rpID,
      origin: this.config.origin,
    });

    // Create the authenticator using proper Prisma methods
    const authenticatorId = randomBytes(16).toString("hex");
    const transportsValue = transports ? transports.join(",") : null;
    const nameValue = name || null;

    // Get the appropriate prisma client based on context
    const prisma = tenantGuardedPrisma(orgId);

    try {
      await prisma.userAuthenticator.create({
        data: {
          id: authenticatorId,
          userId: userId,
          credentialId: credentialId,
          credentialPublicKey: credentialPublicKey, // Keep as Buffer/Uint8Array
          counter: counter,
          credentialDeviceType: deviceType,
          credentialBackedUp: backedUp,
          transports: transportsValue,
          name: nameValue,
          created_at: new Date(),
          last_used: null,
        },
      });

      console.log("Successfully created user authenticator:", {
        authenticatorId,
        userId,
        credentialId,
      });
    } catch (error) {
      console.error("Error creating user authenticator:", error);
      throw error;
    }

    // Fetch the created authenticator
    const authenticator = await prisma.userAuthenticator.findUnique({
      where: { id: authenticatorId },
    });

    // Map to Kysely type
    // Use the same helpers as getUserAuthenticators for type safety
    function toTimestamp(date: Date | null): Timestamp | null {
      return date ? (date as unknown as Timestamp) : null;
    }
    function toGeneratedNumber(
      n: number,
    ): ColumnType<number, number | undefined, number> {
      return n as unknown as ColumnType<number, number | undefined, number>;
    }
    function toKyselyGeneratedBoolean(
      b: boolean,
    ):
      | ColumnType<true, true | undefined, true>
      | ColumnType<false, false | undefined, false> {
      return b
        ? (true as unknown as ColumnType<true, true | undefined, true>)
        : (false as unknown as ColumnType<false, false | undefined, false>);
    }
    const kyselyAuthenticator: UserAuthenticator | null = authenticator
      ? {
          id: authenticator.id,
          userId: authenticator.userId,
          credentialId: authenticator.credentialId,
          credentialPublicKey: Buffer.from(authenticator.credentialPublicKey),
          counter: toGeneratedNumber(authenticator.counter),
          credentialDeviceType: authenticator.credentialDeviceType,
          credentialBackedUp: toKyselyGeneratedBoolean(
            authenticator.credentialBackedUp,
          ),
          transports: authenticator.transports,
          name: authenticator.name,
          createdAt: authenticator.created_at as unknown as Timestamp,
          lastUsed: toTimestamp(authenticator.last_used),
        }
      : null;

    // Enable MFA for the user if this is their first authenticator
    // Get user authenticators using proper Prisma methods
    const userAuthenticators = await prisma.userAuthenticator.findMany({
      where: { userId: userId },
    });

    // Check if this is the first authenticator
    if (userAuthenticators.length === 1) {
      await this.enableMfa(userId, MfaMethod.PASSKEY);
    }

    return kyselyAuthenticator;
  }

  /**
   * Verify a PassKey authentication
   * @param userId User ID
   * @param credentialId Credential ID from WebAuthn
   * @param counter New counter value
   * @param orgId Organization ID
   * @returns Whether the authentication was successful
   */
  async verifyPassKeyAuthentication(
    userId: string,
    credentialId: string,
    counter: number,
    orgId: string,
  ): Promise<boolean> {
    // Verify that the challenge is valid
    const challengeValid = await this.verifyChallenge(
      userId,
      credentialId,
      ChallengeType.AUTHENTICATION,
    );

    if (!challengeValid) {
      return false;
    }

    // Find the authenticator using $queryRaw
    const prisma = tenantGuardedPrisma(orgId);
    const authenticators = await prisma.$queryRaw`
      SELECT * FROM "user_authenticator"
      WHERE "user_id" = ${userId} AND "credential_id" = ${credentialId}
      LIMIT 1
    `;

    const authenticator =
      Array.isArray(authenticators) && authenticators.length > 0
        ? authenticators[0]
        : null;

    if (!authenticator) {
      return false;
    }

    // Verify that the counter is greater than the stored counter
    if (counter <= authenticator.counter) {
      // This could indicate a replay attack
      return false;
    }

    // Update the counter and last used timestamp using $executeRaw
    await prisma.userAuthenticator.update({
      where: { id: authenticator.id },
      data: {
        counter: authenticator.counter + 1,
        last_used: new Date(),
      },
    });

    return true;
  }

  /**
   * Delete a PassKey
   * @param userId User ID
   * @param authenticatorId Authenticator ID
   * @param orgId Organization ID
   */
  async deletePassKey(
    userId: string,
    authenticatorId: string,
    orgId: string,
  ): Promise<void> {
    // Verify that the authenticator belongs to the user using $queryRaw
    const prisma = tenantGuardedPrisma(orgId);
    const authenticators = await prisma.$queryRaw`
      SELECT * FROM "user_authenticator"
      WHERE "id" = ${authenticatorId} AND "user_id" = ${userId}
      LIMIT 1
    `;

    const authenticator =
      Array.isArray(authenticators) && authenticators.length > 0
        ? authenticators[0]
        : null;

    if (!authenticator) {
      throw new Error("Authenticator not found");
    }

    // Delete the authenticator using $executeRaw
    await prisma.userAuthenticator.delete({
      where: { id: authenticatorId },
    });
  }

  /**
   * Rename a PassKey
   * @param userId User ID
   * @param authenticatorId Authenticator ID
   * @param name New name
   * @param orgId Organization ID
   */
  async renamePassKey(
    userId: string,
    authenticatorId: string,
    name: string,
    orgId: string,
  ): Promise<void> {
    // Verify that the authenticator belongs to the user using $queryRaw
    const prisma = tenantGuardedPrisma(orgId);
    const authenticators = await prisma.$queryRaw`
      SELECT * FROM "user_authenticator"
      WHERE "id" = ${authenticatorId} AND "user_id" = ${userId}
      LIMIT 1
    `;

    const authenticator =
      Array.isArray(authenticators) && authenticators.length > 0
        ? authenticators[0]
        : null;

    if (!authenticator) {
      throw new Error("Authenticator not found");
    }

    // Update the name using $executeRaw
    await prisma.userAuthenticator.update({
      where: { id: authenticatorId },
      data: { name: name },
    });
  }

  /**
   * Get all PassKeys for a user
   * @param userId User ID
   * @param orgId Organization ID
   * @returns List of authenticators
   */
  async getPassKeys(userId: string, orgId: string) {
    // Get all authenticators for the user using $queryRaw
    const prisma = tenantGuardedPrisma(orgId);
    const authenticators = await prisma.$queryRaw`
      SELECT * FROM "user_authenticator" WHERE "user_id" = ${userId}
    `;

    return Array.isArray(authenticators) ? authenticators : [];
  }

  /**
   * Get all authenticators for a user
   * @param userId User ID
   * @param orgId Organization ID
   * @returns List of authenticators
   */
  async getUserAuthenticators(userId: string, orgId: string) {
    // Get all authenticators for the user using Prisma findMany
    const prisma = tenantGuardedPrisma(orgId);
    const authenticators = await prisma.userAuthenticator.findMany({
      where: { userId },
    });

    // Map to Kysely UserAuthenticator DTOs for type safety
    // Helper to cast Date to Kysely Timestamp
    function toTimestamp(date: Date | null): Timestamp | null {
      // Kysely's ColumnType is a nominal type, so we cast for compatibility
      return date ? (date as unknown as Timestamp) : null;
    }
    // Helper to cast number to Kysely Generated<number>
    function toGeneratedNumber(
      n: number,
    ): ColumnType<number, number | undefined, number> {
      return n as unknown as ColumnType<number, number | undefined, number>;
    }
    // Helper to cast boolean to Kysely Generated<boolean>
    function toKyselyGeneratedBoolean(
      b: boolean,
    ):
      | ColumnType<true, true | undefined, true>
      | ColumnType<false, false | undefined, false> {
      return b
        ? (true as unknown as ColumnType<true, true | undefined, true>)
        : (false as unknown as ColumnType<false, false | undefined, false>);
    }
    return authenticators.map(
      (auth): UserAuthenticator => ({
        id: auth.id,
        userId: auth.userId,
        credentialId: auth.credentialId,
        credentialPublicKey: Buffer.from(auth.credentialPublicKey),
        counter: toGeneratedNumber(auth.counter),
        credentialDeviceType: auth.credentialDeviceType,
        credentialBackedUp: toKyselyGeneratedBoolean(auth.credentialBackedUp),
        transports: auth.transports,
        name: auth.name,
        createdAt: auth.created_at as unknown as Timestamp,
        lastUsed: toTimestamp(auth.last_used),
      }),
    );
  }

  /**
   * Verify authentication with PassKey
   * @param email User's email
   * @param credentialId Credential ID from WebAuthn
   * @param authenticatorData Authenticator data from WebAuthn
   * @param clientDataJSON Client data from WebAuthn
   * @param signature Signature from WebAuthn
   * @param challenge Challenge used for authentication
   * @param orgId Organization ID
   * @returns User object if authentication was successful, null otherwise
   */
  async verifyAuthentication(
    email: string,
    credentialId: string,
    authenticatorData: Buffer,
    clientDataJSON: Buffer,
    signature: Buffer,
    challenge: string,
    orgId: string,
  ): Promise<{ id: string; email: string; name: string } | null> {
    try {
      const prisma = tenantGuardedPrisma(orgId);
      // TODO: Replace with type-safe Prisma query and Kysely DTO mapping
      const users = await prisma.$queryRaw`
        SELECT * FROM "user" WHERE "email" = ${email} LIMIT 1
      `;
      const user = Array.isArray(users) && users.length > 0 ? users[0] : null;
      if (!user) return null;
      // TODO: Add authenticator checks and updates if needed
      return {
        id: user.id,
        email: user.email,
        name: user.name,
      };
    } catch (error) {
      logger.error(
        { error, email, credentialId, orgId },
        "Error verifying authentication",
      );
      return null;
    }
  }

  /**
   * Verify MFA authentication with PassKey
   * @param userId User ID
   * @param credentialId Credential ID from WebAuthn
   * @param authenticatorData Authenticator data from WebAuthn
   * @param clientDataJSON Client data from WebAuthn
   * @param signature Signature from WebAuthn
   * @param challenge Challenge used for authentication
   * @param orgId Organization ID
   * @returns Whether the authentication was successful
   */
  async verifyMfaAuthentication(
    userId: string,
    credentialId: string,
    authenticatorData: Buffer,
    clientDataJSON: Buffer,
    signature: Buffer,
    challenge: string,
    orgId: string,
  ): Promise<boolean> {
    try {
      // Verify that the challenge is valid
      const challengeValid = await this.verifyChallenge(
        userId,
        challenge,
        ChallengeType.AUTHENTICATION,
      );

      if (!challengeValid) {
        return false;
      }

      // Find the authenticator using $queryRaw
      const prisma = tenantGuardedPrisma(orgId);
      const authenticator = await prisma.userAuthenticator.findFirst({
        where: {
          userId: userId,
          credentialId: credentialId,
        },
      });

      if (!authenticator) {
        return false;
      }

      // Verify the signature (simplified for this implementation)
      // In a real implementation, you would use the SimpleWebAuthn library to verify the signature
      // This is a placeholder for the actual verification logic
      const signatureValid = true;

      if (!signatureValid) {
        return false;
      }

      // Update the counter and last used timestamp using $executeRaw
      await tenantGuardedPrisma(orgId).userAuthenticator.update({
        where: { id: authenticator.id },
        data: {
          counter: authenticator.counter + 1,
          last_used: new Date(),
        },
      });

      return true;
    } catch (error) {
      logger.error(
        { error, userId, credentialId, orgId },
        "Error verifying MFA authentication",
      );
      return false;
    }
  }

  /**
   * Delete an authenticator
   * @param authenticatorId Authenticator ID
   * @param userId User ID
   * @param orgId Organization ID
   * @returns Whether the operation was successful
   */
  async deleteAuthenticator(
    authenticatorId: string,
    userId: string,
    orgId: string,
  ): Promise<boolean> {
    try {
      // Verify that the authenticator belongs to the user
      const prisma = tenantGuardedPrisma(orgId);
      const authenticator = await prisma.userAuthenticator.findFirst({
        where: {
          id: authenticatorId,
          userId: userId,
        },
      });

      if (!authenticator) {
        return false;
      }

      // Delete the authenticator
      await prisma.userAuthenticator.delete({
        where: { id: authenticatorId },
      });

      return true;
    } catch (error) {
      logger.error(
        { error, authenticatorId, userId, orgId },
        "Error deleting authenticator",
      );
      return false;
    }
  }

  /**
   * Authenticate using a passkey (both email-based and discoverable flows)
   * @param email Optional email for email-based flow
   * @param credentialId Credential ID from WebAuthn
   * @param authenticatorData Authenticator data from WebAuthn
   * @param clientDataJSON Client data from WebAuthn
   * @param signature Signature from WebAuthn
   * @param challenge Challenge used for authentication
   * @param orgId Organization ID
   * @returns User object if authentication was successful, null otherwise
   */
  async authenticateWithPasskey(
    email: string | null,
    credentialId: string,
    authenticatorData: Buffer,
    clientDataJSON: Buffer,
    signature: Buffer,
    challenge: string,
    orgId: string,
  ) {
    try {
      if (email) {
        // Email-based authentication flow
        return await this.verifyAuthentication(
          email,
          credentialId,
          authenticatorData,
          clientDataJSON,
          signature,
          challenge,
          orgId,
        );
      } else {
        // Discoverable (email-less) authentication flow
        return await this.verifyDiscoverableAuthentication(
          credentialId,
          authenticatorData,
          clientDataJSON,
          signature,
          challenge,
          orgId,
        );
      }
    } catch (error) {
      logger.error(
        { error, email, credentialId, orgId },
        "Error in authenticateWithPasskey",
      );
      return null;
    }
  }

  /**
   * Verify discoverable authentication (email-less, by credentialId)
   * @param credentialId Credential ID from WebAuthn
   * @param authenticatorData Authenticator data from WebAuthn
   * @param clientDataJSON Client data from WebAuthn
   * @param signature Signature from WebAuthn
   * @param challenge Challenge used for authentication
   * @param orgId Organization ID
   * @returns User object if authentication is successful, null otherwise
   */
  async verifyDiscoverableAuthentication(
    credentialId: string,
    authenticatorData: Buffer,
    clientDataJSON: Buffer,
    signature: Buffer,
    challenge: string,
    orgId: string,
  ) {
    try {
      // Find the authenticator by credentialId
      const prisma = tenantGuardedPrisma(orgId);
      const authenticator = await prisma.userAuthenticator.findFirst({
        where: { credentialId },
        include: { user: true },
      });
      if (!authenticator || !authenticator.userId) {
        logger.error(
          { credentialId, orgId },
          "verifyDiscoverableAuthentication: Authenticator not found or missing userId",
        );
        return null;
      }
      // Validate the challenge as discoverable
      // Fix: pass empty string instead of null for userId
      // Use ChallengeType.AUTHENTICATION for discoverable authentication (see enum)
      const challengeValid = await this.verifyChallenge(
        "",
        challenge,
        ChallengeType.AUTHENTICATION,
      );
      if (!challengeValid) {
        console.error("verifyDiscoverableAuthentication: Challenge invalid", {
          credentialId,
          challenge,
          orgId,
        });
        return null;
      }
      // In a real implementation, verify the signature here
      // Placeholder:
      const signatureValid = true;
      if (!signatureValid) {
        console.error("verifyDiscoverableAuthentication: Signature invalid", {
          credentialId,
          orgId,
        });
        return null;
      }
      // Update the counter and last used timestamp
      await prisma.userAuthenticator.update({
        where: { id: authenticator.id },
        data: {
          counter: authenticator.counter + 1,
          last_used: new Date(),
        },
      });
      // Return user object (minimal)
      const user = await prisma.user.findUnique({
        where: { id: authenticator.userId },
      });
      if (!user) {
        console.error(
          "verifyDiscoverableAuthentication: User not found after authenticator",
          {
            authenticatorUserId: authenticator.userId,
            credentialId,
            orgId,
          },
        );
        return null;
      }
      return {
        id: user.id,
        email: user.email,
        name: user.name,
      };
    } catch (error) {
      console.error("Error verifying discoverable authentication:", error);
      return null;
    }
  }
}

// Export singleton instance
export const webAuthnService = new WebAuthnService();

// Export passKeyService as an alias to webAuthnService for backward compatibility
export const passKeyService = webAuthnService;
