"use server";

import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  ALLOWED_IMAGE_EXTENSIONS,
  MIME_TYPES,
  type AllowedExtension,
} from "./file-image-constants";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Get a file record by ID and organization ID
 */
export async function getFileImage(fileId: string, orgId: string) {
  const file = await prisma.file.findUnique({
    where: { id: fileId, organization_id: orgId },
  });
  return file;
}

/**
 * Get all file records for an organization
 */
export async function getFileImageByOrgId(orgId: string) {
  const files = await prisma.file.findMany({
    where: { organization_id: orgId },
  });
  return files;
}

/**
 * Get only image files for an organization
 * @param orgId Organization ID
 * @param options.page Page number (1-based)
 * @param options.pageSize Number of items per page
 * @param options.search Optional search string for file name
 */
export async function getImageFilesByOrgId(
  orgId: string,
  options?: {
    page?: number;
    pageSize?: number;
    search?: string;
  },
) {
  const { page = 1, pageSize = 10, search = "" } = options || {};

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Generate extension filter for image types
  const extensionFilter = ALLOWED_IMAGE_EXTENSIONS.map((ext) => ({
    path: { endsWith: ext },
  }));

  // Get the total count for pagination
  const totalCount = await prisma.file.count({
    where: {
      organization_id: orgId,
      OR: extensionFilter,
      ...(search ? { name: { contains: search, mode: "insensitive" } } : {}),
    },
  });

  // Get the files
  const files = await prisma.file.findMany({
    where: {
      organization_id: orgId,
      OR: extensionFilter,
      ...(search ? { name: { contains: search, mode: "insensitive" } } : {}),
    },
    select: {
      id: true,
      name: true,
      path: true,
      buffer_file: false, // Don't fetch the buffer for the listing
      created_at: true,
      updated_at: true,
    },
    orderBy: {
      created_at: "desc", // Most recent first
    },
    skip,
    take: pageSize,
  });

  // Return the files and pagination info
  return {
    files,
    pagination: {
      total: totalCount,
      page,
      pageSize,
      pageCount: Math.ceil(totalCount / pageSize),
      hasMore: skip + files.length < totalCount,
    },
  };
}

/**
 * Get an image file with validation
 * Returns the buffer, content type, and other needed metadata
 */
export async function getValidatedImageFile(fileId: string, orgId: string) {
  // Retrieve file information with minimal fields
  const file = await prisma.file.findFirst({
    where: {
      id: fileId,
      organization_id: orgId,
    },
    select: {
      buffer_file: true,
      path: true,
      name: true,
    },
  });

  // Check if file exists and has content
  if (!file || !file.buffer_file) {
    return {
      success: false as const,
      error: "File not found",
      status: 404,
    };
  }

  // Get the file extension and check if it's an image
  const path = file.path || file.name || "";
  const extension = path.substring(path.lastIndexOf(".")).toLowerCase();

  // Check if extension is in allowed list
  if (!ALLOWED_IMAGE_EXTENSIONS.includes(extension as AllowedExtension)) {
    return {
      success: false as const,
      error: "File is not an image",
      status: 400,
      extension,
    };
  }

  // Get the buffer and content type
  const buffer = Buffer.from(file.buffer_file);
  const contentType = MIME_TYPES[extension as AllowedExtension];

  return {
    success: true as const,
    buffer,
    contentType,
    size: buffer.length,
    filename: file.name,
    path: file.path,
  };
}
