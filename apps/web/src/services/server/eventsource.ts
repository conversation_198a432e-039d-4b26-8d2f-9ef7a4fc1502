import { bypassedPrisma, guardedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  CustomEventSourceStatus,
  CustomEventSourceType,
  CustomEventSource,
} from "@/models/eventsource";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

interface CreateEventSourceParams {
  eventType: string;
  userId: string;
  orgId?: string;
  eventId: string;
}

async function createEventSource({
  eventType,
  userId,
  orgId,
  eventId,
}: CreateEventSourceParams) {
  const e = await prisma.eventSource.create({
    data: {
      id: eventId,
      type: eventType,
      status: "pending",
      user_id: userId,
      organization_id: orgId,
    },
  });
  return {
    ...e,
    status: e.status as unknown as CustomEventSourceStatus,
    type: e.type as unknown as CustomEventSourceType,
  } as CustomEventSource;
}

async function getEventSource(
  { userId, orgId }: { userId: string; orgId: string },
  { eventId }: { eventId: string },
) {
  const e = await guardedPrisma({
    userId,
    orgId,
  }).eventSource.findFirstOrThrow({
    where: { id: eventId },
  });
  return {
    ...e,
    status: e.status as unknown as CustomEventSourceStatus,
    type: e.type as unknown as CustomEventSourceType,
  } as CustomEventSource;
}

async function updateEventSource(
  { userId, orgId }: { userId: string; orgId: string },
  { id, status, error }: { id: string; status: string; error?: string },
) {
  const e = await guardedPrisma({
    userId,
    orgId,
  }).eventSource.update({
    where: { id: id },
    data: {
      status: status,
      error: error === null ? null : error,
    },
  });
  return e;
}

export { createEventSource, getEventSource, updateEventSource };
