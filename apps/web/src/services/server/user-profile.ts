"use server";

import { bypassedPrisma, guardedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { UserProfile } from "@/models/user-profile";

interface GetUserProfileParams {
  userId: string;
}

async function getUserProfile({
  userId,
}: GetUserProfileParams): Promise<UserProfile | null> {
  if (!userId) return null;
  const result = await extendedPrisma.userProfile.findUnique({
    where: { user_id: userId },
    include: {
      user: true,
    },
  });
  if (!result) {
    const error = { message: "User not found", errorCode: 4040 };
    return null;
  }
  const { user_id, first_name, last_name, settings, user } = result;
  return {
    userId: user_id,
    firstName: first_name,
    lastName: last_name,
    settings: settings as UserProfile["settings"],
    email: user.email || "",
  };
}

export { getUserProfile };
