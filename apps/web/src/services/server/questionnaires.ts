"use server";

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "type-fest";
import { SourceId } from "@/lib/langchain/genai";
import {
  bypassRLS,
  bypassedPrisma,
  guardedPrisma,
  otherDataAuditLog,
  prisma,
  tenantGuardedPrisma,
  with<PERSON><PERSON><PERSON><PERSON>og<PERSON>,
} from "@/lib/prisma";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { CellDivider, isJSON } from "@/lib/utils";
import { Scope } from "@/models/scope";
import { IDocument } from "@/types/org/docs";
import { PrismaClient, ShortAnswer } from "@askinfosec/database";
import { getAllScopes } from "./scope";
import { createOrg } from "./organization";
import { Step1FormSchemaValues } from "@/app/[lang]/(private)/[org]/questionnaires/upload/model";

/**
 * Gets all Questionnaire
 */
export async function getAllQuestionnaires(org: string, user: string) {
  const member = await guardedPrisma({
    orgId: org,
    userId: user,
  }).member.findFirst({
    where: {
      organization_id: org,
      user_id: user,
    },
    include: {
      role: true,
    },
  });

  if (!member) throw Error("User does not exist within the organization");

  const db = tenantGuardedPrisma(org);

  // If vendor role, only show questionnaires where they are assigned
  if (member.role_id === "vendor") {
    const vendor = await db.vendor.findFirst({
      where: {
        id: member.id,
        organization_id: org,
      },
    });

    if (!vendor) {
      return []; // Return empty if no vendor record found
    }

    // Get questionnaires assigned to this vendor
    // We want both:
    // 1. Questionnaires directly assigned to the vendor (is_assessor=false)
    // 2. Questionnaires created by the vendor with a reference (vendor copies)
    return await db.questionnaire
      .findMany({
        where: {
          organization_id: org,
          OR: [
            {
              // Original condition: Directly assigned questionnaires
              vendor_id: vendor.id,
              is_assessor: false,
            },
            {
              // Add condition for vendor copies: Has reference and is assigned to this user
              reference: { not: "" },
              assigned: { hasSome: [user] },
              is_assessor: false,
            },
          ],
        },
        select: {
          id: true,
          name: true,
          vendor: true,
          questions: true,
          created_at: true,
          status: true,
          created: true,
          assigned: true,
          scope: true,
          reference: true,
          is_assessor: true,
        },
        orderBy: {
          created_at: "asc",
        },
      })
      .then((res) =>
        res.map(
          ({
            id,
            name,
            vendor,
            questions,
            status,
            created_at,
            created,
            assigned,
            scope,
            reference,
            is_assessor,
          }) => ({
            id,
            name,
            status,
            questionnaire_id: id,
            vendor: vendor?.vendor_name,
            count: questions.length,
            uploader: created,
            uploaded: created_at,
            assigned,
            scope: scope,
            reference,
            is_assessor,
          }),
        ),
      );
  }

  // Checks if the current user is an owner or other else. If they are the owner then
  // we return all of the questionnaires else we just return the assigned.

  if (member.role_id === "owner") {
    return await tenantGuardedPrisma(org)
      .questionnaire.findMany({
        where: {
          organization_id: org,
        },
        select: {
          id: true,
          name: true,
          vendor: true,
          questions: true,
          created_at: true,
          status: true,
          created: true,
          assigned: true,
          scope: true,
          reference: true,
          is_assessor: true,
        },
        orderBy: {
          created_at: "asc",
        },
      })
      .then((res) =>
        res.map(
          ({
            id,
            name,
            vendor,
            questions,
            status,
            created_at,
            created,
            assigned,
            scope,
            reference,
            is_assessor,
          }) => ({
            id,
            name,
            status,
            questionnaire_id: id,
            vendor: vendor?.vendor_name,
            count: questions.length,
            uploader: created,
            uploaded: created_at,
            assigned,
            scope: scope,
            reference,
            is_assessor,
          }),
        ),
      );
  } else {
    return await tenantGuardedPrisma(org)
      .questionnaire.findMany({
        where: {
          organization_id: org,
          assigned: {
            hasSome: [user],
          },
        },
        select: {
          id: true,
          name: true,
          vendor: true,
          questions: true,
          created_at: true,
          status: true,
          created: true,
          assigned: true,
          scope: true,
          reference: true,
          is_assessor: true,
        },
        orderBy: {
          created_at: "asc",
        },
      })
      .then((res) =>
        res.map(
          ({
            id,
            name,
            vendor,
            questions,
            status,
            created_at,
            created,
            assigned,
            scope,
            reference,
            is_assessor,
          }) => ({
            id,
            name,
            status,
            questionnaire_id: id,
            vendor: vendor?.vendor_name,
            count: questions.length,
            uploader: created,
            uploaded: created_at,
            assigned,
            scope: scope,
            reference,
            is_assessor,
          }),
        ),
      );
  }
}
/**
 * Gets all the assigned of the question.
 * @param id - questionId
 * @param org - organization id
 */
export async function getAllassigned(id: string, org: string) {
  const q = await tenantGuardedPrisma(org).question.findFirst({
    where: {
      id,
    },
    select: {
      questionnaire: true,
    },
  });

  if (!q) throw Error("Question does not exists");
  const assigned = await Promise.all(
    q.questionnaire.assigned.map(async (u) => {
      const user = await prisma.user.findFirst({
        where: {
          id: u,
        },
      });
      return user;
    }),
  );
  const filteredassigned = assigned.filter((user) => user !== null);

  return filteredassigned;
}
/**
 * Gets specific questionnaire
 */
export async function getQuestionnaire(org: string, questionnaire: string) {
  const q = await tenantGuardedPrisma(org).questionnaire.findUnique({
    where: { id: questionnaire },
    select: {
      id: true,
      name: true,
      created_at: true,
      created_by: true,
      due_date: true,
      status: true,
      vendor: { select: { id: true, vendor_name: true, email: true } },
      assigned: true,
      organization_id: true,
      original_file: true,
      download_file: true,
      reference: true,
      is_assessor: true,
      questions: {
        include: {
          evidences: true,
        },
        orderBy: {
          ref_num: "asc",
        },
      },
      scope: true,
    },
  });

  if (!q) throw Error("Questionnaire does not exists");
  const assigned = await Promise.all(
    q.assigned.map(async (u) => {
      const user = await extendedPrisma.user.findFirst({
        where: {
          id: u,
        },
      });
      return user;
    }),
  );

  return {
    questionnaire: {
      id: q.id,
      name: q.name,
      created_at: q.created_at,
      created_by: q.created_by,
      due_date: q.due_date,
      status: q.status.toString(),
      vendor: q.vendor,
      assigned: assigned.map((c) => {
        return {
          user_d: c?.id,
          email: c?.email,
        };
      }),
      download_file: q.download_file,
      organization_id: q.organization_id,
      original_file: q.original_file,
      scope: q.scope as unknown as Scope,
      reference: q.reference ?? "",
      is_assessor: q.is_assessor,
    },
    questions: q.questions.map((question) => {
      return {
        ...question,
        answer_yes_no_na: question.answer_detail?.toLowerCase(),
        assigned: assigned.find((c) => c?.id === question.assigned_id),
        count_evidence_collected:
          question.evidences && question.evidences.length,
        scope: q.scope as unknown as Scope,
      };
    }),
  };
}

/**
 * Gets specific Question
 * @param questionnaire_id - questionnaire id
 * @param id  - main question id
 */
export async function getQuestion(id: string, org: string, user: string) {
  const q = await tenantGuardedPrisma(org).question.findFirst({
    where: {
      id,
    },
    select: {
      id: true,
      question: true,
      questionnaire: true,
      answer_detail: true,
      answer_yes_no_na: true,
      answer_verified_by_id: true,
      answer_verified_date: true,
      assigned_id: true,
      ref_docs: true,
      ref_kb: true,
      created_by_id: true,
      created_at: true,
      mapping_question: true,
      mapping_answer_detail: true,
      mapping_yes_no_na: true,
      mapping_yes: true,
      mapping_no: true,
      mapping_na: true,
      mapping_question_id: true,
      mapping_worksheet_id: true,
      ai_generated_answer_history: true,
      ai_generated_answer: true,
      ref_num: true,
      updated_at: true,
      evidences: {
        select: {
          id: true,
        },
      },
      answer_verified_by: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (!q) throw Error("Question does not exists.");

  let ai_generated_answer = q.ai_generated_answer;

  // Note: Backwards compatibility isJSON function and s.ref_id
  if (q.ai_generated_answer && isJSON(q.ai_generated_answer)) {
    let sourceIds = (JSON.parse(q.ai_generated_answer) as AILastGeneratedAnswer)
      .sourceId;
    const files = await tenantGuardedPrisma(org).file.findMany({
      where: {
        id: {
          in: sourceIds.map((s) => {
            return s.ref_id || s.refId;
          }),
        },
      },
      select: {
        id: true,
        path: true,
      },
    });
    ai_generated_answer = JSON.stringify({
      ...(JSON.parse(q.ai_generated_answer) as AILastGeneratedAnswer),
      sourceId: sourceIds.map((s) => {
        return {
          ...s,
          fileName: files.find((f) => f.id === (s.ref_id || s.refId))?.path,
        };
      }),
    });
  } else {
    ai_generated_answer = JSON.stringify({
      answer: q.ai_generated_answer,
      sources: "",
      sourceId: [],
    });
  }

  const files = await tenantGuardedPrisma(org).file.findMany({
    where: {
      id: {
        in: q.ref_docs.map((d) => {
          return d;
        }),
      },
    },
    select: {
      id: true,
      path: true,
      name: true,
    },
  });

  return {
    id: q.id,
    answer_detail: q.answer_detail,
    answer_yes_no_na: q.answer_yes_no_na?.toString(),
    answer_verified_by_id: q.answer_verified_by_id !== null ? true : false,
    answer_verified_date: q.answer_verified_date?.toLocaleDateString(),
    assigned_id: q.assigned_id !== null ? q.assigned_id : "",
    created_by: q.created_by_id,
    created_at: q.created_at,
    question: q.question,
    questionnaire_id: q.questionnaire.id,
    ref_docs: q.ref_docs,
    ref_kb: q.ref_kb,
    reference_docs: files.map((f) => {
      return {
        fileId: f.id,
        name: f.name,
        path: f.path,
      } as unknown as IDocument;
    }),
    mapping_question: CellDivider(q.mapping_question),
    mapping_answer_detail: CellDivider(q.mapping_answer_detail),
    mapping_yes_no_na: CellDivider(q.mapping_yes_no_na),
    mapping_yes: CellDivider(q.mapping_yes),
    mapping_no: CellDivider(q.mapping_no),
    mapping_na: CellDivider(q.mapping_na),
    mapping_question_id: CellDivider(q.mapping_question_id),
    mapping_worksheet_id: CellDivider(q.mapping_worksheet_id),
    scope: q.questionnaire.scope_id,
    ai_generated_answer,
    count_evidences_collected: q.evidences && q.evidences.length,
    ref_num: q.ref_num,
    answer_verified_by: q.answer_verified_by,
    updated_at: q.updated_at,
  };
}

/**
 * Update question question verification
 */
export const updateQuestionVerification = async ({
  user,
  id,
  db,
}: {
  user: string;
  id: string;
  db: PrismaClient;
}) => {
  const q = await db.question.findFirst({
    where: {
      id,
    },
  });

  if (!q) throw Error("Question does not exists.");

  // This will check if the current user is the one who's assigned to verify.
  if (q.assigned_id !== user)
    throw Error("Only assigned user can verify this question.");

  // Checks if the question have been verified. If it its then we just unverify else the opposite.
  if (q.answer_verified_by_id !== null && q.answer_verified_date !== null) {
    const updatedQuestion = await db
      .$extends(
        withAuditLogger({
          orgId: q.organization_id,
          userId: user,
          message: "Question updated.",
        }),
      )
      .$extends(otherDataAuditLog())
      .question.update({
        where: {
          id: id,
        },
        data: {
          answer_verified_date: null,
          answer_verified_by_id: null,
          status: "open",
        },
      });
    return updatedQuestion;
  } else {
    const updatedQuestion = await db
      .$extends(
        withAuditLogger({
          orgId: q.organization_id,
          userId: user,
          message: "Question updated.",
        }),
      )
      .$extends(otherDataAuditLog())
      .question.update({
        where: {
          id: id,
        },
        data: {
          answer_verified_date: new Date(),
          answer_verified_by_id: user,
          status: "verified",
        },
      });

    return updatedQuestion;
  }
};

/**
 * Updates Question and Answer
 * @param question - Question new value
 * @param answer - answer new value
 * @param question_id - Question ID
 * @param db - Prisma Client
 */
export const updateQnAQuestion = async (
  user: string,
  question: string,
  answer: string,
  id: string,
  db: PrismaClient,
) => {
  const q = await db.question.findFirst({
    where: {
      id,
    },
  });

  if (!q) throw Error("Question does not exists");

  const updatedQAQuestion = await db
    .$extends(
      withAuditLogger({
        orgId: q.organization_id,
        userId: user,
        message: "Question updated.",
      }),
    )
    .$extends(otherDataAuditLog())
    .question.update({
      where: {
        id,
      },
      data: {
        answer_detail: answer,
        question,
        updated_at: new Date(),
        last_update_by_id: user,
      },
    });

  return updatedQAQuestion;
};

/**
 * Updates the question settings
 * @param id - Question id
 * @param assigned - Assigned user id
 * @param user - Current user
 * @param db - Database Client
 * @param short_answer - Short Answer either Yes/No/NA
 */
export const updateQuestionSetting = async (
  id: string,
  assigned: string,
  user: string,
  db: PrismaClient,
) => {
  const q = await db.question.findFirst({
    where: {
      id,
    },
  });

  if (!q) throw Error("Question does not exists");

  const updateQuestion = await db
    .$extends(
      withAuditLogger({
        orgId: q.organization_id,
        userId: user,
        message: "Question updated.",
      }),
    )
    .$extends(otherDataAuditLog())
    .question.update({
      where: {
        id,
      },
      data: {
        assigned_id: assigned,
        last_update_by_id: user,
        updated_at: new Date(),
      },
    });

  return updateQuestion;
};

export const deleteQuestion = async (id: string, db: PrismaClient) => {
  const q = await db.question.findFirst({
    where: {
      id,
    },
  });

  if (!q) throw Error("Question does not exists");

  const deleteQuestion = await db
    .$extends(
      withAuditLogger({
        orgId: q.organization_id,
        userId: q.created_by_id,
        message: "Question deleted",
      }),
    )
    .question.delete({
      where: {
        id,
      },
    });

  return deleteQuestion;
};

/**
 * @param id - question id
 * @param answer - short answer either yes, no or NA
 * @param db - prisma client.
 */
export async function updateShortAnswer(
  questionId: string,
  shortAnswer: ShortAnswer,
  org: string,
) {
  const db = tenantGuardedPrisma(org);
  const q = await db.question.findFirst({
    where: {
      id: questionId,
    },
  });

  if (!q) throw Error("Question does not exists");

  // Checks if the submitted short answer is either YES, NO , NA
  if (!Object.values(ShortAnswer).includes(shortAnswer as ShortAnswer)) {
    throw new Error(`Invalid answer: ${shortAnswer}`);
  }

  const updateQuestion = await db
    .$extends(otherDataAuditLog())
    .question.update({
      where: {
        id: questionId,
      },
      data: {
        answer_yes_no_na: shortAnswer as ShortAnswer,
      },
    });

  return updateQuestion;
}

export interface AILastGeneratedAnswer {
  answer: string;
  sources: string;
  sourceId: SourceId[];
}
/**
 * @param id - question id
 * @param answer - detailed answer
 * @param sourceHeaders - list of header names source
 * @param source - identifier if the source is from docs or kb
 * @param sourceId - source ids
 * @param db - prisma client.
 */
export const updateAIAnswerAndSource = async (
  id: string,
  answer: string,
  sourceHeaders: string[],
  sourceId: SourceId[],
  db: PrismaClient,
) => {
  const q = await db.question.findFirst({ where: { id } });
  if (!q) throw Error("Question does not exists");

  const aiLastGeneratedAnswer: AILastGeneratedAnswer = {
    answer,
    sources: sourceHeaders.length > 0 ? sourceHeaders.join(", ") : "",
    sourceId,
  };
  // Note: The logic below prevent overwriting the current answer_detail, ref_kb, and ref_docs if these fields have existing values. We put all the details of the last AI answer to the `ai_generated_answer` field so that, we can refer back to this if we need to copy over
  // TODO: Make audit logger work here, it's not working. If we enable it, it's not reaching the return. Alternatively, we can rename the `comment` field to `ai_answer_history` since we are not using it. we can store here a json array of {timestamp, who, answer, source, source_id}
  // .$extends(withAuditLogger({userId: userId, orgId: q.organization_idmessage: "AI generated answer requested",}))
  const updateAnswerAndSource = await db
    .$extends(otherDataAuditLog())
    .question.update({
      where: { id },
      data: {
        answer_detail: q.answer_detail || answer, // this means if q.answer_detail is null or undefined, use answer, otherwise use q.answer_detail
        ai_generated_answer: JSON.stringify(aiLastGeneratedAnswer),
        // ref_kb: q.ref_kb || (source === "kb" && sourceId), // this means if q.ref_kb is null or undefined, use sourceId only if source is "kb", otherwise use existing q.ref_kb
        // ref_docs: q.ref_docs || (source === "document" && sourceId), //this means if q.ref_docs is null or undefined, use sourceId only if source is "document", otherwise use existing q.ref_docs
      },
    });

  return updateAnswerAndSource;
};

/**
 *
 * @param ids - array of  documents
 * @param questionId - question Id
 * @param db - data base client
 * @returns
 */
export async function updateQuestionLinkDocuments({
  ids,
  questionId,
  org,
  user,
}: {
  ids: string[];
  questionId: string;
  org: string;
  user: string;
}) {
  try {
    const db = tenantGuardedPrisma(org);

    // Update the question with new document references
    const updated = await db.$extends(otherDataAuditLog()).question.update({
      where: {
        id: questionId,
      },
      data: {
        ref_docs: ids,
        updated_at: new Date(),
        last_update_by_id: user,
      },
      select: {
        id: true,
        ref_docs: true,
      },
    });

    return {
      success: true,
      data: updated,
    };
  } catch (error) {
    console.error("Update question link documents error:", error);
    throw new Error("Failed to update question link documents");
  }
}

export async function copyQuestionnaireAndQuestions({
  id,
  org,
  user,
  email,
}: {
  id: string;
  org: string;
  user: string;
  email: string;
}) {
  try {
    // Get the questionnaire first
    const questionnaire = await extendedPrisma.questionnaire.findUnique({
      where: { id },
      include: {
        questions: true,
        scope: true, // Include the scope to get its properties
      },
    });

    if (!questionnaire) {
      throw new Error("Questionnaire not found");
    }

    // Get a vendor scope for the organization
    const scopes = await getAllScopes({ org });
    // First try to find a scope specifically marked as vendor type
    const vendorScope = scopes.find((s) => s.scope_type === "vendor");
    let scopeId: string | undefined;

    // If no vendor scope found, create a warning and fall back to common scope
    if (!vendorScope) {
      console.warn(
        "No scope with scope_type='vendor' found - falling back to Common scope",
      );
      const commonScope = scopes.find((s) => s.name === "Common");

      if (!commonScope) {
        console.error(
          "Neither vendor nor common scope found - this may cause issues",
        );
      }

      scopeId = commonScope?.id;
    } else {
      scopeId = vendorScope.id;
    }

    // Create a copy for the vendor
    const copiedQuestionnaire = await extendedPrisma.questionnaire.create({
      data: {
        name: questionnaire.name,
        created_at: new Date(),
        due_date: questionnaire.due_date,
        assigned: [user],
        created_by: user,
        organization_id: org,
        original_file: questionnaire.original_file,
        scope_id: scopeId, // Use vendor scope if available, otherwise common scope
        download_file: questionnaire.download_file,
        reference: `${questionnaire.id},${email}`,
        is_assessor: false, // This is important - vendor questionnaire is not an assessor questionnaire
      },
    });

    // After creating the questionnaire, explicitly update its scope to have scope_type "vendor" if needed
    const scope = await extendedPrisma.scope.findUnique({
      where: { id: scopeId },
      select: { scope_type: true },
    });

    if (scope && scope.scope_type !== "vendor") {
      // If the scope exists but is not of type "vendor", update the questionnaire directly
      console.log(
        `Ensuring vendor questionnaire has scope_type "vendor" even though scope ID ${scopeId} has type "${scope.scope_type}"`,
      );

      // Update the questionnaire's directly associated scope_type field if it exists
      // This assumes your questionnaire model has a direct scope_type field or you have some way to override it
      try {
        const updatedQuestionnaire = await extendedPrisma.questionnaire.update({
          where: { id: copiedQuestionnaire.id },
          data: {
            // If you don't have a direct scope_type field, you might need another approach
            // This could involve creating a special "other_data" JSON field with an override
            other_data: { override_scope_type: "vendor" },
          },
        });
        console.log("Updated questionnaire with vendor scope type override");
      } catch (error) {
        console.warn("Could not set scope_type override:", error);
        // Continue even if this fails - the is_assessor flag is more important
      }
    }

    console.log("Created vendor questionnaire copy:", {
      id: copiedQuestionnaire.id,
      scope_id: scopeId,
      reference: `${questionnaire.id},${email}`,
      is_assessor: false,
    });

    // Copy the questions
    await extendedPrisma.$transaction(
      questionnaire.questions.map((question) =>
        extendedPrisma.question.create({
          data: {
            organization_id: org,
            status: "open", // Set status to open for vendor to fill
            updated_at: new Date(),
            last_update_by_id: user,
            questionnaire_id: copiedQuestionnaire.id,
            created_by_id: user,
            created_at: new Date(),
            question: question.question,
            answer_detail: "", // Clear answer detail for vendor to fill
            assigned_id: user,
            reference: question.id,
            // Copy mapping fields
            mapping_question: question.mapping_question,
            mapping_yes: question.mapping_yes,
            mapping_no: question.mapping_no,
            mapping_na: question.mapping_na,
            mapping_yes_no_na: question.mapping_yes_no_na,
            mapping_answer_detail: question.mapping_answer_detail,
            mapping_question_id: question.mapping_question_id,
            mapping_worksheet_id: question.mapping_worksheet_id,
            mapping_category: question.mapping_category,
          },
        }),
      ),
    );

    return {
      organization: org,
      questionnaireId: copiedQuestionnaire.id,
    };
  } catch (error) {
    console.error("Error in copyQuestionnaireAndQuestions:", error);
    throw error;
  }
}

/**
 * Copy the assessee's answer
 */
export async function copyAsseseeAnswer({
  assesseeQId,
  assessorQId,
  user,
}: {
  user: string;
  assesseeQId: string;
  assessorQId: string;
}) {
  const asseseeQuestionnaire = await extendedPrisma.questionnaire.findFirst({
    where: {
      id: assesseeQId,
    },
  });

  const assessorQuestionnaire = await extendedPrisma.questionnaire.findFirst({
    where: {
      id: assessorQId,
    },
  });

  if (!asseseeQuestionnaire || !assessorQuestionnaire) {
    throw Error("Questionnaire does not exists");
  }

  const assesseeDb = tenantGuardedPrisma(asseseeQuestionnaire.organization_id);
  const assessorDb = tenantGuardedPrisma(assessorQuestionnaire.organization_id);

  const assesseeQuestions = await assesseeDb.question.findMany({
    where: {
      questionnaire_id: asseseeQuestionnaire.id,
    },
  });

  assesseeQuestions.map(async (question) => {
    if (question.reference !== null) {
      const evidences = await assesseeDb.evidence.findMany({
        where: {
          questionId: question.id,
        },
        select: {
          file: true,
          note: true,
        },
      });

      // If there are new evidences attached on the question
      if (evidences.length >= 1) {
        evidences.map(async ({ file, note }) => {
          const queryFile = await assessorDb.file.findFirst({
            where: {
              name: file.name,
              path: file.path,
              checksum: file.checksum,
              organization_id: assessorQuestionnaire.organization_id,
            },
            select: {
              id: true,
            },
          });

          // Check if file  & evidence. If the file exists but there is no evidence create one
          if (queryFile) {
            const queryEvidence = await assessorDb.evidence.findFirst({
              where: {
                fileId: queryFile.id,
              },
            });

            if (!queryEvidence) {
              await assessorDb.evidence.create({
                data: {
                  questionId: question.reference,
                  fileId: queryFile.id,
                  note,
                },
              });
            }
          } else {
            const f = await assessorDb.file.create({
              data: {
                name: file.name,
                path: file.path,
                buffer_file: file.buffer_file,
                checksum: file.checksum,
                organization_id: assessorQuestionnaire.organization_id,
                created_by_id: user,
                created_at: new Date(),
                updated_at: new Date(),
                document_type: file.document_type,
              },
            });

            await assessorDb.evidence.create({
              data: {
                questionId: question.reference,
                fileId: f.id,
                note,
              },
            });
          }
        });
      }

      // If there are new docs on attached to the question
      if (question.ref_docs.length >= 1) {
        const ref_docs = await Promise.all(
          question.ref_docs.map(async (file) => {
            const assesseeFile = await assesseeDb.file.findFirst({
              where: {
                id: file,
              },
            });

            const queryFile = await assessorDb.file.findFirst({
              where: {
                name: assesseeFile!.name,
                path: assesseeFile!.path,
                buffer_file: assesseeFile!.buffer_file,
                checksum: assesseeFile!.checksum,
                organization_id: assessorQuestionnaire.organization_id,
              },
            });

            if (queryFile) {
              return queryFile.id;
            } else {
              const f = await assessorDb.file.create({
                data: {
                  name: assesseeFile!.name,
                  path: assesseeFile!.path,
                  buffer_file: assesseeFile!.buffer_file,
                  checksum: assesseeFile!.checksum,
                  organization_id: assessorQuestionnaire.organization_id,
                  created_at: new Date(),
                  updated_at: new Date(),
                  document_type: assesseeFile!.document_type,
                  category_id: assesseeFile!.category_id,
                  created_by_id: user,
                },
              });
              return f.id;
            }
          }),
        );

        await assessorDb.$extends(otherDataAuditLog()).question.update({
          where: {
            id: question.reference,
          },
          data: {
            ref_docs: ref_docs,
          },
        });
      }

      await assessorDb.$extends(otherDataAuditLog()).question.update({
        where: {
          id: question.reference,
        },
        data: {
          answer_detail: question.answer_detail,
        },
      });
    }
  });
}

/**
 * Update questionnaire details
 */
export async function updateQuestionnaire(
  user: string,
  questionnaireId: string,
  org: string,
  details: Step1FormSchemaValues,
) {
  try {
    const db = tenantGuardedPrisma(org);
    const updated = await db
      .$extends(otherDataAuditLog())
      .questionnaire.update({
        where: {
          id: questionnaireId,
        },
        data: {
          name: details.questionnaire_name,
          vendor_id: details.vendor || null,
          due_date: details.due_date,
          assigned: details.assigned,
          scope_id: details.scope_id,
        },
      });

    return updated;
  } catch (error) {
    console.error("Update questionnaire error:", error);
    throw new Error("Failed to update questionnaire");
  }
}

export async function isQuestionnaireAccessibleByVendor(
  questionnaireId: string,
  vendorId: string,
  org: string,
  db: PrismaClient,
) {
  const questionnaire = await db.questionnaire.findFirst({
    where: {
      id: questionnaireId,
      organization_id: org,
      vendor_id: vendorId,
    },
  });

  return !!questionnaire;
}

// create a function to update the questionnaire
