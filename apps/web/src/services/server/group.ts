import { guardedPrisma, prisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { nanoid } from "nanoid";

interface GetAllGroupsParams {
  org: string;
  user_id?: string;
}

export async function getAllGroups({ org, user_id }: GetAllGroupsParams) {
  const db = guardedPrisma({ orgId: org });

  const groups = await db.group.findMany({
    where: {
      organization_id: org,
    },
  });

  const transformedGroups = await Promise.all(
    groups.map(async (group) => {
      const members = await db.userGroup.findMany({
        where: {
          group_id: group.id,
        },
        select: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      const filteredMember = members.map((mem) => {
        const name = mem.user.name !== null ? mem.user.name : mem.user.email;
        return { id: mem.user.id, name: name };
      });

      return {
        id: group.id,
        name: group.name,
        description: group.description,
        organization_id: group.organization_id,
        created_at: new Date(group.created_at).toLocaleDateString(),
        updated_at: group.updated_at,
        members: filteredMember,
      };
    }),
  );

  return transformedGroups;
}

interface CreateGroupInterface {
  org: string;
  name: string;
  description: string;
  user: string;
  db: PrismaClient;
}

/**
 * Creates Group
 * @param org - organization id
 * @param name - group name
 * @param user - group creator / owner
 * @param description - group description
 * @param db - Prisma client
 */
export async function createGroup({
  org,
  name,
  user,
  description,
  db,
}: CreateGroupInterface) {
  const g = await db.group.findFirst({
    where: {
      name,
      organization_id: org,
    },
  });

  if (g) throw Error(`Organization Group ${name} already exists`);

  const u = await prisma.user.findFirst({
    where: {
      id: user,
    },
  });

  if (!u) throw Error("User does not exists");

  const group = await db.group.create({
    data: {
      id: nanoid(),
      name,
      organization_id: org,
      description,
      created_by_id: user,
      updated_by_id: user,
      created_at: new Date(),
    },
  });

  return group;
}

interface AddGroupMemberInterface {
  org: string;
  ids: string[];
  group: string;
  db: PrismaClient;
  userId: string;
}

export async function addGroupMember({
  org,
  group,
  ids,
  db,
  userId,
}: AddGroupMemberInterface) {
  const g = await db.group.findFirst({
    where: {
      id: group,
    },
  });

  if (!g) throw Error("Group does not exists");

  const m = await db.userGroup.findFirst({
    where: {
      organization_id: org,
      group_id: group,
    },
  });

  if (m) throw Error("Member already exists in the same group.");

  const members = await Promise.all(
    ids.map(async (id) => {
      const u = await prisma.user.findFirst({
        where: {
          id: id,
        },
      });

      if (!u) throw Error("User does not exists");

      const addMember = await db.userGroup.create({
        data: {
          id: nanoid(),
          group_id: group,
          user_id: id,
          organization_id: org,
          created_by_id: userId,
          updated_by_id: userId,
        },
      });

      return addMember;
    }),
  );

  return members;
}

export async function removeGroupMember({
  id,
  group,
  org,
  db,
}: {
  id: string;
  group: string;
  org: string;
  db: PrismaClient;
}) {
  const g = await db.group.findFirst({
    where: {
      id: group,
    },
  });

  if (!g) throw Error("Group does not exists");

  const u = await prisma.user.findFirst({
    where: {
      id,
    },
  });

  if (!u) throw Error("User does not exists");

  const queryMember = await db.userGroup.findFirst({
    where: {
      group_id: group,
      organization_id: org,
    },
  });

  if (!queryMember) throw Error("User is not a member of the said group.");

  const deleteMember = await db.userGroup.delete({
    where: {
      id: queryMember.id,
    },
  });
  return deleteMember;
}
