/**
 * File: src/services/server/file.ts
 *
 * Overview:
 *  This module exports the `saveFileContent` function, which processes an uploaded file
 *  (either an image or a document) by performing validations, loading content via appropriate loaders,
 *  computing a checksum, checking for duplicate uploads, and then either updating an existing file record
 *  (with re-training of vector embeddings) or creating a new record.
 *
 *  The workflow is as follows:
 *    - Validate file extension.
 *    - For images: Immediately store the file buffer and metadata.
 *    - For documents: Load file content, validate non-empty content, compute checksum,
 *      then search for duplicate uploads and either update (retrain) or create a new record.
 *
 *  Note: The request header 'x-askinfosec-force-retrain' is read but not yet used to conditionally override retraining.
 */

import { TextLoader } from "langchain/document_loaders/fs/text";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { CSVLoader } from "@langchain/community/document_loaders/fs/csv";
import { UnstructuredLoader } from "@langchain/community/document_loaders/fs/unstructured";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { Prisma, PrismaClient } from "@askinfosec/database";
import { DefaultArgs } from "@askinfosec/database";
import { ParsedPath } from "path";
import { headers } from "next/headers";

import { createChecksum } from "@/lib/api/utils";
import {
  businessPlan,
  freePlan,
} from "@/lib/subscription/subscription-manager";
import { getOrg } from "@/services/server/organization";
import {
  ACCEPTED_DOCUMENT_EXTENSIONS,
  ACCEPTED_IMAGE_EXTENSIONS,
} from "@/lib/utils";
import { aiSettings } from "./ai-settings";
import { classifyFile } from "./security/file-classification";
import {
  FileAccessLevel,
  DEFAULT_ACCESS_LEVEL,
} from "~/src/types/file-access-level";

export const saveFileContent = async (
  file: File | Blob,
  db: PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
  org: string,
  created_by_id: string,
  filePath: ParsedPath,
  type?: string,
) => {
  // Validate the file extension. If not allowed, return null.
  if (!ACCEPTED_DOCUMENT_EXTENSIONS.includes(filePath.ext)) {
    return null;
  }

  // Retrieve headers to check for a forced retrain flag (currently unused).
  const headersList = await headers();
  const forceRetrain = headersList.get("x-askinfosec-force-retrain") === "true";
  // TODO: Optionally use forceRetrain to control retraining behavior.

  // Create a safe (shortened) filename.
  const filename = filePath.name.substring(0, 90);

  // Compute the file's raw buffer once for reuse.
  const rawBuffer = Buffer.from(await file.arrayBuffer());

  // === IMAGE UPLOAD HANDLING ===
  if (ACCEPTED_IMAGE_EXTENSIONS.includes(filePath.ext)) {
    const uploadImageFile = await db.file.create({
      data: {
        name: filePath.name,
        path: `${filename}${filePath.ext}`,
        buffer_file: rawBuffer,
        organization_id: org,
        document_type: type || null,
        created_by_id: created_by_id,
        checksum: "", // No checksum for images.
      },
    });
    return { message: "File uploaded successfully.", file: uploadImageFile.id };
  }

  // === DOCUMENT FILE HANDLING ===
  // Choose the correct loader based on file extension.
  const loader =
    filePath.ext === ".pdf"
      ? new PDFLoader(file)
      : filePath.ext === ".docx"
        ? new DocxLoader(file)
        : filePath.ext === ".csv"
          ? new CSVLoader(file)
          : filePath.ext === ".md" || filePath.ext === ".txt"
            ? new TextLoader(file)
            : new UnstructuredLoader(filePath.name); // TODO: determine if this is correct and how to really support this

  // Load the file content.
  const contentArray = await loader.load();

  // Ensure that contentArray is not empty and that the first document has content.
  if (
    !Array.isArray(contentArray) ||
    contentArray.length === 0 ||
    !contentArray[0].pageContent
  ) {
    return { message: "empty content" };
  }

  // Compute checksum of the loaded document content.
  const checksum = createChecksum(contentArray[0].pageContent);

  // Check for duplicate content based on checksum.
  const duplicateFile = await db.file.findFirst({
    where: { organization_id: org, checksum: checksum },
  });
  if (duplicateFile) {
    const msg = `This file already exists based on checksum: ${checksum}`;
    console.log(msg);
    throw new Error(msg);
  }

  // Check whether a file with the same path/name exists.
  const existingFileRecord = await db.file.findFirst({
    where: { organization_id: org, path: `${filePath.name}${filePath.ext}` },
  });

  // Helper: Create vector embeddings by splitting content into chunks.
  const createVector = async (fileId: string) => {
    const { embeddingChunkSize, embeddingChunkOverlap } = await aiSettings(org);
    const splitter = RecursiveCharacterTextSplitter.fromLanguage("markdown", {
      chunkSize: embeddingChunkSize,
      chunkOverlap: embeddingChunkOverlap,
    });
    const docs = await splitter.splitDocuments(contentArray);
    // Insert vector embeddings in a transaction.
    await db.$transaction(
      docs.map((doc) =>
        db.documentVector.create({
          data: {
            content: doc.pageContent,
            organization_id: org,
            file_id: fileId,
            metadata: doc.metadata,
          },
        }),
      ),
    );
  };

  try {
    if (existingFileRecord) {
      // File exists: update the buffer, remove old embeddings, and re-create embeddings (i.e. re-train).
      // Also reclassify the file to update its access level
      const accessLevel = await classifyFile(
        contentArray[0].pageContent,
        filename,
        org,
      );
      console.log(`Reclassified file ${filename} as ${accessLevel}`);

      await db.file.update({
        where: { id: existingFileRecord.id },
        data: {
          buffer_file: rawBuffer,
          access_level: accessLevel,
        },
      });
      await db.documentVector.deleteMany({
        where: { file_id: existingFileRecord.id },
      });
      await createVector(existingFileRecord.id);
      return { message: "Done re-training model", file: existingFileRecord.id };
    } else {
      // New file: enforce organization limits (for certain document types).
      const currentOrg = await getOrg({ userId: created_by_id, orgId: org });
      const currentFileCount = await db.file.count({
        where: {
          organization_id: org,
          document_type: {
            in: ["policy", "procedure", "control"],
          },
        },
      });
      if (
        currentFileCount > businessPlan.limits.maxDocuments &&
        !currentOrg?.license
      ) {
        throw new Error(
          `Your organization (${org}) reached maximum number (${businessPlan.limits.maxDocuments}) of documents that can be uploaded!`,
        );
      }

      // Classify the file content to determine access level
      const accessLevel = await classifyFile(
        contentArray[0].pageContent,
        filename,
        org,
      );
      console.log(`Classified file ${filename} as ${accessLevel}`);

      // Create a new file record with the determined access level
      const newFile = await db.file.create({
        data: {
          name: filename,
          path: `${filename}${filePath.ext}`,
          buffer_file: rawBuffer,
          organization_id: org,
          document_type: type || null,
          created_by_id: created_by_id,
          checksum,
          access_level: accessLevel,
        },
      });
      // Create vector embeddings for the new file.
      await createVector(newFile.id);
      return { message: "Done uploading and training model", file: newFile.id };
    }
  } catch (error) {
    console.error("saveFileContent():", error);
    return { message: "Failed to train model" };
  }
};
