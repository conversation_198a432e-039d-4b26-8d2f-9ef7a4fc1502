"use server";

import type { PrismaClient } from "@askinfosec/database";
import { getServerSession } from "@/lib/get-session";
// TODO: Import Kysely organization type instead of using custom model types
// import { Organization as KyselyOrganization } from "@/types/prisma-kysely";

import {
  bypassedPrisma,
  guardedPrisma,
  otherDataAuditLog,
  prisma,
  tenantGuardedPrisma,
  userGuardedPrisma,
  withAuditLogger,
} from "@/lib/prisma";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
// Avoid importing model types from the client re-export to prevent ESM/CJS type issues
import { type Organization } from "@/models/organization";
import { UserProfile } from "@/models/user-profile";
import type { CompatLogger as Logger } from "@/observability/migration-shim";

import { createCustomer } from "./stripe";
import { HttpError } from "@/lib/errors";
import { getStartAndEndDateFromCurrentMonth } from "./ai-usage";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { copyQuestionnaireAndQuestions } from "./questionnaires";
import { transport } from "@/lib/email";
import EmailNotif from "../../../emails/new-org";
import { render } from "@react-email/render";
import { OrganizationModuleService } from "./organization-modules";

// Add this interface before the createOrgCore function
interface OrgCoreResult {
  id: string;
  name: string;
  email?: string | null;
}

// Change the return type of createOrgCore
async function createOrgCore({
  userId,
  orgName,
}: CreateOrgParams): Promise<OrgCoreResult> {
  const initial_current_usage = {
    maxDocuments: 0,
    maxKnowledgeBaseQAs: 0,
    maxChatQuestions: 0,
    maxBulkQuestionUpload: 0,
  };

  // Check for existing organization name before starting transaction
  const existingOrgName = await extendedPrisma.organization.findFirst({
    where: { company_name: orgName },
  });
  if (existingOrgName)
    throw new HttpError(400, "organization name already exists", true);

  try {
    // Execute all critical DB operations in a single transaction
    const orgResult = await prisma.$transaction(
      async (tx: any) => {
        // Set current user context
        await tx.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`;

        // Create organization
        const org = await tx.organization.create({
          data: {
            company_name: orgName,
            main_owner_id: userId,
            current_usage: initial_current_usage as any,
            settings: {
              chatEnabled: true,
            },
            // Note: new field for modules array - initialize empty
            modules: [],
          },
        });

        // Set organization context and create member
        await tx.$executeRaw`SELECT set_config('app.current_organization_id', ${org.id}, TRUE)`;
        const member = await tx.member.create({
          data: { organization_id: org.id, user_id: userId, role_id: "owner" },
          select: { user: { select: { email: true } } },
        });

        // Handle user profile update if first organization
        const count = await tx.member.count({ where: { user_id: userId } });
        if (count === 1) {
          const userProfileSettings = {
            defaultOrganizationId: org.id,
          } satisfies UserProfile["settings"];

          await tx.userProfile.upsert({
            where: { user_id: userId },
            create: {
              user_id: userId,
              settings: userProfileSettings as any,
            },
            update: {
              settings: userProfileSettings as any,
            },
          });
        }

        return {
          id: org.id,
          name: orgName,
          email: member.user.email,
        };
      },
      {
        maxWait: 10000, // ms to wait for transaction
        timeout: 30000, // ms before timing out
      },
    );

    return orgResult;
  } catch (error) {
    logger.error(
      { error, userId, orgName },
      "Error in core organization creation",
    );
    throw error;
  }
}

interface CreateOrgParams {
  userId: string;
  orgName: string;
}

async function createOrg({
  userId,
  orgName,
}: CreateOrgParams): Promise<Organization> {
  try {
    // Step 1: Create core organization data
    const orgCore = await createOrgCore({ userId, orgName });

    // Step 2: Handle post-processing tasks
    const { id, name, email } = orgCore;
    const completeOrg = await createOrgPostProcess(
      { id, name, email: email || undefined },
      userId,
    );

    // Step 3 (optional): Handle core module assignment as a separate operation
    try {
      await assignCoreModulesToOrg(orgCore.id);
    } catch (moduleError) {
      // Log but don't fail the org creation - we've already created the org
      logger.warn(
        { error: moduleError, orgId: orgCore.id },
        "Module assignment failed, but organization was created",
      );
    }

    return completeOrg;
  } catch (error) {
    logger.error(
      `Failed to create organization: ${error instanceof Error ? error.message : String(error)}, orgName: ${orgName}`,
    );
    throw error;
  }
}

// Helper function to assign core modules
async function assignCoreModulesToOrg(orgId: string): Promise<void> {
  try {
    // Verify that the organization exists before proceeding
    const organizationExists = await prisma.organization.findUnique({
      where: { id: orgId },
      select: { id: true },
    });

    if (!organizationExists) {
      logger.warn(
        { orgId },
        "Cannot assign core modules - organization not found",
      );
      return; // Exit early without throwing an error
    }

    // Get core modules from database
    const coreModules = await prisma.module.findMany({
      where: { is_core: true },
    });

    // Update organization with core module IDs
    if (coreModules.length > 0) {
      const coreModuleIds = coreModules.map((module: any) => module.id);

      // Use tenantGuardedPrisma to ensure proper context
      await tenantGuardedPrisma(orgId).organization.update({
        where: {
          id: orgId,
          deleted_at: null, // Only update if not deleted
        },
        data: { modules: coreModuleIds },
      });

      logger.info(
        { orgId, moduleCount: coreModuleIds.length },
        "Successfully assigned core modules to organization",
      );
    } else {
      logger.info({ orgId }, "No core modules found to assign");
    }
  } catch (error) {
    // Log error but don't fail the whole operation
    logger.error(
      { error, orgId },
      "Failed to assign core modules to organization - manual intervention needed",
    );
    // Continue with organization creation even if module assignment fails
  }
}

interface UpdateOrgNameParams {
  userId: string;
  orgId: string;
  orgName: string;
}

async function updateOrgName({
  userId,
  orgId,
  orgName,
}: UpdateOrgNameParams): Promise<Organization> {
  const dataToUpdate = { company_name: orgName };
  const result = await guardedPrisma({
    userId,
    orgId,
    roles: ["owner", "admin"],
  })
    .$extends(
      withAuditLogger({ orgId, userId, message: "update organization name" }),
    )
    .$extends(otherDataAuditLog())
    .organization.update({
      where: { id: orgId, deleted_at: null },
      data: dataToUpdate,
    });
  return await getOrgThrowErrorIfOrgNotFound({ userId, orgId });
}

interface UpdateOrgSettingsParams {
  userId: string;
  orgId: string;
  settings: NonNullable<Organization["settings"]>;
}

async function updateOrgSettings({
  userId,
  orgId,
  settings,
}: UpdateOrgSettingsParams): Promise<Organization> {
  const currentSetting = await guardedPrisma({
    userId,
    orgId,
  }).organization.findFirst({
    where: {
      id: orgId,
    },
    select: {
      settings: true,
    },
  });

  if (currentSetting?.settings !== null) {
    const { chatEnabled } = (currentSetting!.settings as any) || {};
    const mergeSettings = {
      chatEnabled: chatEnabled !== undefined ? chatEnabled : false,
      ...settings,
    };

    const result = await guardedPrisma({
      userId,
      orgId,
      roles: ["owner", "admin"],
    })
      .$extends(
        withAuditLogger({
          orgId,
          userId,
          message: "update organization settings",
        }),
      )
      .$extends(otherDataAuditLog())
      .organization.update({
        where: { id: orgId, deleted_at: null },
        data: { settings: mergeSettings },
      });
  } else {
    const mergeSettings = {
      chatEnabled: false,
      ...settings,
    };
    const result = await guardedPrisma({
      userId,
      orgId,
      roles: ["owner", "admin"],
    })
      .$extends(
        withAuditLogger({
          orgId,
          userId,
          message: "update organization settings",
        }),
      )
      .$extends(otherDataAuditLog())
      .organization.update({
        where: { id: orgId, deleted_at: null },
        data: { settings: mergeSettings },
      });
  }
  return await getOrgThrowErrorIfOrgNotFound({ userId, orgId });
}

interface UpdateOrgCurrentUsageParams {
  userId: string;
  orgId: string;
  resource: string;
  value: number;
}

/**
 * [@deprecated] This function updates organization's platform usage
 *
 * @param userId - user id
 * @param orgId - organization to update
 * @param resource - resource limit to update
 * @param value - resource consumed
 */
async function updateOrgCurrentUsage({
  userId,
  orgId,
  resource,
  value,
}: UpdateOrgCurrentUsageParams): Promise<Organization> {
  const updatedOrg = await guardedPrisma({
    userId,
    orgId,
  }).$transaction(async (tx: any) => {
    const org = (await tx.organization.findUnique({
      where: { id: orgId },
    })) as unknown as Organization;
    const currentUsage = org.current_usage;
    // TODO: As we grow, we might adding more resources to have limits. We can think of a better way to handle the growth.
    let updatedUsage: any = {};
    switch (resource) {
      case "maxDocuments":
        updatedUsage = {
          ...currentUsage,
          maxDocuments: currentUsage?.maxDocuments
            ? currentUsage?.maxDocuments + value
            : value,
        };
        break;
      case "maxKnowledgeBaseQAs":
        updatedUsage = {
          ...currentUsage,
          maxKnowledgeBaseQAs: currentUsage?.maxKnowledgeBaseQAs
            ? currentUsage?.maxKnowledgeBaseQAs + value
            : value,
        };
        break;
      case "maxChatQuestions":
        updatedUsage = {
          ...currentUsage,
          maxChatQuestions: currentUsage?.maxChatQuestions
            ? currentUsage?.maxChatQuestions + value
            : value,
        };
        break;
      case "maxBulkQuestionUpload":
        updatedUsage = {
          ...currentUsage,
          maxBulkQuestionUpload: currentUsage?.maxBulkQuestionUpload
            ? currentUsage?.maxBulkQuestionUpload + value
            : value,
        };
        break;
      case "maxInvitedMembers":
        updatedUsage = {
          ...currentUsage,
          maxInvitedMembers: currentUsage?.maxInvitedMembers
            ? currentUsage?.maxInvitedMembers + value
            : value,
        };
        break;
      case "maxAIDocumentRetraining":
        updatedUsage = {
          ...currentUsage,
          maxAIDocumentRetraining: currentUsage?.maxAIDocumentRetraining
            ? currentUsage?.maxAIDocumentRetraining + value
            : value,
        };
        break;
      default:
        // Nothing changes
        return org;
    }
    return (await tx.organization.update({
      where: { id: orgId },
      data: { current_usage: updatedUsage as any },
    })) as unknown as Organization;
  });

  return updatedOrg;
}

interface UpdateOrgOpenAISettingsParams {
  userId: string;
  orgId: string;
  openAISettings: NonNullable<Organization["openai_settings"]>;
}

async function updateOrgOpenAISettings({
  userId,
  orgId,
  openAISettings,
}: UpdateOrgOpenAISettingsParams): Promise<Organization> {
  // Import the utility function here to avoid circular dependencies
  const { unwrapOpenAISettings } = await import(
    "~/src/lib/utils/openai-settings"
  );

  // Unwrap settings if they have a 'set' property
  const settingsToStore = unwrapOpenAISettings(openAISettings, {
    orgId,
    logIssue: true,
  });

  const result = await guardedPrisma({
    userId,
    orgId,
    roles: ["owner", "admin"],
  })
    .$extends(
      withAuditLogger({
        orgId,
        userId,
        message: "update organization OpenAI settings",
      }),
    )
    .$extends(otherDataAuditLog())
    .organization.update({
      where: { id: orgId, deleted_at: null },
      data: { openai_settings: settingsToStore as any },
    });
  return await getOrgThrowErrorIfOrgNotFound({ userId, orgId });
}

interface AcceptInviteParams {
  invite: { id: string; orgId: string; roleId: string; image: string };
  userId: string;
  email?: string | null;
  type?: string;
}

async function acceptInvite({
  invite,
  userId,
  email,
  type,
}: AcceptInviteParams) {
  // Confirm if user owns at least 1 organization
  // const mainOrg = await bypassedPrisma.organization.findFirst({ where: { main_owner_id: userId } });
  // if (!mainOrg) {
  //   // If user does not own any org, create it first
  //   let mainOrgName = email?.split("@")[0] || nanoid();
  //   const existingOrgName = await bypassedPrisma.organization.findFirst({
  //     where: { company_name: mainOrgName },
  //   });
  //   if (existingOrgName) mainOrgName += `.${nanoid(4)}`;
  //   await createOrg({ userId, orgName: mainOrgName });
  // }
  // This will check if the invited user has an image. If it doesn't then we can append the color set in the invite table.
  /// we want to be consistent with their avatar.
  // const invitedUser = await bypassedPrisma.user.findFirst({ where: { id: userId } });
  // if (!invitedUser?.image) {
  //   await bypassedPrisma.user.update({
  //     where: { id: userId },
  //     data: {
  //       image: invite.image,
  //     },
  //   });
  // }

  await extendedPrisma.$transaction(async (tx: any) => {
    await tx.user
      .findFirst({ where: { id: userId } })
      .then(async (u: any) => {
        if (!u?.image) {
          await tx.user.update({
            where: { id: userId },
            data: {
              image: invite.image,
            },
          });
        }
      })
      .catch((e: any) => {});

    const [_a, _b] = await Promise.all([
      // TODO: Confirm organization is not deleted before adding a member
      tx.member.create({
        data: {
          organization_id: invite.orgId,
          user_id: userId,
          role_id: invite.roleId,
        },
      }),
      tx.invite.delete({ where: { id: invite.id } }),
    ]);
  });
}

interface AcceptInviteVendorParams extends AcceptInviteParams {
  questionnaireId: string;
}
async function acceptInviteVendor({
  invite,
  userId,
  email,
  type,
  questionnaireId,
}: AcceptInviteVendorParams) {
  try {
    // Check if user is already a member with vendor role
    const existingMember = await extendedPrisma.member.findFirst({
      where: {
        organization_id: invite.orgId,
        user_id: userId,
        role_id: invite.roleId,
      },
    });

    if (!existingMember) {
      throw new Error("User is not a member with vendor role");
    }

    // Copy questionnaire and get IDs
    const { organization, questionnaireId: qId } =
      await copyQuestionnaireAndQuestions({
        id: questionnaireId,
        org: invite.orgId,
        user: userId,
        email: email ?? "",
      });

    // Delete the invite after successful processing
    await extendedPrisma.invite.delete({
      where: {
        id: invite.id,
      },
    });

    return { organization, questionnaireId: qId };
  } catch (error) {
    logger.error(
      { error, invite, userId, email, type, questionnaireId },
      "Error in acceptInviteVendor",
    );
    throw error;
  }
}

interface GetOrgParams {
  userId: string;
  orgId: string;
  prisma?: PrismaClient;
}

async function getOrgThrowErrorIfOrgNotFound({
  userId,
  orgId,
  prisma,
}: GetOrgParams): Promise<Organization> {
  const persistedOrg = await getOrg({ userId, orgId, prisma });
  if (persistedOrg) {
    return persistedOrg;
  } else {
    throw new Error("Failed to retrieve organization");
  }
}

/**
 * Returns the organization model. Recommended to call this function after any update on organization to give consistent organization model.
 *
 * This implementation directly fetches the specific organization by ID with membership check,
 * rather than fetching all user organizations and filtering. This optimization reduces database load
 * and improves performance, especially for users with multiple organizations.
 *
 * @param userId - The ID of the user requesting the organization
 * @param orgId - The ID of the organization to retrieve
 * @param prisma - Optional Prisma client instance
 * @returns The organization object or throws an error if not found
 */
async function getOrg({
  userId,
  orgId,
  prisma,
}: {
  userId: string;
  orgId: string;
  prisma?: PrismaClient;
}): Promise<Organization> {
  try {
    // Validate organization ID format
    // Organization IDs should not contain dots or special characters that might be confused with static files
    if (
      !orgId ||
      orgId.includes(".") ||
      orgId.includes("/") ||
      orgId.includes("\\")
    ) {
      const err_msg = `Invalid organization ID format: ${orgId}`;
      logger.error(err_msg);
      throw new Error(err_msg);
    }

    // Log the start of the optimized getOrg function
    logger.debug(
      { userId, orgId },
      "getOrg(): Starting optimized direct organization fetch",
    );

    // Use the provided prisma client or create a guarded one with full RLS context (org + user)
    const prismaClient =
      (prisma as PrismaClient) ??
      (guardedPrisma({ userId, orgId }) as unknown as PrismaClient);

    // Directly fetch the specific organization with membership check
    const org = await prismaClient.organization.findFirst({
      where: {
        id: orgId,
        deleted_at: null,
        members: {
          some: {
            user_id: userId,
          },
        },
      },
      include: {
        organization_subscriptions: {
          include: {
            subscription_tier: {
              include: {
                subscription: {
                  select: { id: true, description: true },
                },
              },
            },
          },
        },
        licenses: {
          select: {
            id: true,
            organization_id: true,
            key: true,
            description: true,
            valid_until: true,
          },
        },
        members: {
          where: { user_id: userId },
          select: {
            role_id: true,
            user_id: true,
            organization_id: true,
            id: true,
          },
        },
      },
    });

    // If organization not found or user is not a member
    if (!org) {
      const err_msg = `User ${userId} is not a member of organization ${orgId}`;
      logger.debug({ userId, orgId }, err_msg);
      throw new Error(err_msg);
    }

    // Create a properly formatted organization object with type assertions
    // This is necessary because the Prisma type doesn't match our expected structure
    const formattedOrg = {
      id: org.id,
      company_name: org.company_name, // Ensure we use company_name for consistency
      deleted_at: org.deleted_at,
      settings: org.settings,
      openai_settings: org.openai_settings,
      current_usage: org.current_usage,
      organization_subscriptions: (org as any).organization_subscriptions,
      licenses: (org as any).licenses,
      members: (org as any).members,
      main_owner_id: org.main_owner_id,
      company_information: org.company_information,
      // Include modules if they exist
      modules: org.modules || [],
    };

    // Use type assertion to handle type compatibility issues
    // This is safe because we're ensuring all required fields are present
    const result = await mapToOrg(formattedOrg as any, userId);

    // Log successful completion
    logger.debug(
      { userId, orgId },
      "getOrg(): Successfully fetched organization with optimized query",
    );

    return result;
  } catch (error) {
    // Log the error and rethrow
    logger.error(
      `Error in getOrg(): ${error instanceof Error ? error.message : String(error)}, userId: ${userId}, orgId: ${orgId}`,
    );
    throw error;
  }
}

async function getUserOrgs({
  userId,
  prisma: _prisma,
}: {
  userId: string;
  prisma?: PrismaClient;
}): Promise<Organization[]> {
  // Step 1: Fetch all organization IDs where the user is a member using user-scoped client
  const userScoped =
    _prisma ?? (userGuardedPrisma(userId) as unknown as PrismaClient);
  const memberships = await userScoped.member.findMany({
    where: { user_id: userId },
    select: { organization_id: true },
  });

  const orgIds = memberships.map((m: any) => m.organization_id);
  if (orgIds.length === 0) return [];

  // Step 2: Hydrate each organization with org-scoped guarded client to satisfy RLS and include members
  const orgs = await Promise.all(
    orgIds.map(
      (orgId: string) => getOrg({ userId, orgId }), // getOrg internally uses guardedPrisma({ orgId, userId })
    ),
  );

  return orgs;
}

interface DeleteOrgParams {
  orgId: string;
  userId: string;
  logger: Logger;
}

async function deleteOrg({ orgId, userId, logger }: DeleteOrgParams) {
  // Check if user is the org's main_owner
  const db = guardedPrisma({ orgId, userId });
  const org = await db.organization.findFirst({ where: { id: orgId } });
  if (!org) {
    throw new HttpError(404, `organization with id '${orgId}' not found`, true);
  }

  logger.debug({ orgId, userId, org }, "attempt delete organization");

  if (org.main_owner_id !== userId) {
    throw new HttpError(
      403,
      "user is not allowed to delete this organization",
      true,
    );
  }
  const updatedOrg = await db
    .$extends(
      withAuditLogger({ orgId, userId, message: "soft delete organization" }),
    )
    .$extends(otherDataAuditLog())
    .organization.update({
      where: { id: orgId },
      data: { deleted_at: new Date() },
    });
  // TODO: Handle org subscription and stripe subscription
  return updatedOrg;
}

const usageCount = async (
  orgId: string,
  aiFeatureType:
    | "document_upload"
    | "knowledge_base"
    | "chat"
    | "questions_bulk_upload",
) => {
  const [startDate, endDate] = getStartAndEndDateFromCurrentMonth();

  const { Prisma: PrismaNS } = require("@prisma/client");
  const sql = PrismaNS.sql`
  SELECT
    COUNT(DISTINCT transaction_id)
  FROM ai_usage
  WHERE organization_id = ${orgId}
  AND ai_feature_type = ${aiFeatureType}
  AND timestamp BETWEEN ${startDate} AND ${endDate};`;

  return await prisma.$transaction(async (tx: any) => {
    await tx.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`;
    const [result] = (await tx.$queryRaw(sql)) as Array<{ count: bigint }>;
    return Number(result.count);
  });
};

async function getDocumentTrainUsage(orgId: string) {
  return await usageCount(orgId, "document_upload");
}

async function getKbTrainUsage(orgId: string) {
  return await usageCount(orgId, "knowledge_base");
}

async function getQuestionnaireUsage(orgId: string) {
  return await usageCount(orgId, "questions_bulk_upload");
}

async function getChatUsage(orgId: string) {
  return await usageCount(orgId, "chat");
}

async function mapToOrg<
  T extends Organization & {
    organization_subscriptions: (any & {
      subscription_tier?: {
        subscription?: {
          id: string;
          description: string;
        };
      };
    })[];
  } & {
    licenses: {
      id: string;
      description: string;
      key: string;
      organization_id: string;
    }[];
  } & {
    members: {
      role_id: string;
      user_id: string;
      organization_id: string;
      id: string;
    }[];
  },
>(o: T, userId?: string) {
  const {
    id,
    // our DB column is company_name; some code paths may pass name instead
    // we'll handle both via safe access below
    main_owner_id,
    deleted_at,
    settings,
    openai_settings,
    current_usage,
    organization_subscriptions,
    members,
  } = o as any;

  // Check if members array exists and has items
  if (!members || members.length === 0) {
    throw Error(`Organization '${id}' has no members`);
  }

  // Handle subscription data - provide fallback if missing
  let subscription = { id: "none", name: "No Subscription" };
  if (
    organization_subscriptions &&
    organization_subscriptions.length > 0 &&
    organization_subscriptions[0].subscription_tier?.subscription
  ) {
    subscription = {
      id: organization_subscriptions[0].subscription_tier.subscription.id,
      name: organization_subscriptions[0].subscription_tier.subscription
        .description,
    };
  }

  // Optimize module fetching - use existing modules if available from the query
  let modulesList: string[] = [];

  // Check if modules were already included in the query (from getOrg optimization)
  if (o.modules && Array.isArray(o.modules)) {
    // Use the modules directly from the database query
    modulesList = o.modules;
  } else {
    // Fallback to the service call with optimizations - but avoid if not needed
    try {
      modulesList = await OrganizationModuleService.getModulesForOrganization(
        id,
        {
          skipExistenceCheck: true,
          existingModules: o.modules,
        },
      );
    } catch (error) {
      // Log error but don't fail the mapping - modules can be empty
      logger.warn(
        { error, orgId: id },
        "Failed to fetch organization modules, using empty array",
      );
      modulesList = [];
    }
  }

  // Log the organization data before mapping
  // console.log("mapToOrg - company_name:", company_name, "id:", id);

  // Enhanced company name fallback logic - minimize database calls
  let finalCompanyName = (o as any).company_name;
  if (!finalCompanyName || finalCompanyName === "") {
    // Try to access company_name from the raw object first (most efficient)
    if ((o as any).company_name) {
      finalCompanyName = (o as any).company_name;
      logger.debug(
        { id, finalCompanyName },
        "Found company_name in raw object",
      );
    } else if ((o as any).name) {
      // Some objects might have 'name' instead of 'company_name'
      finalCompanyName = (o as any).name;
      logger.debug(
        { id, finalCompanyName },
        "Using 'name' property as fallback",
      );
    } else {
      // Final fallback - avoid database call entirely, use a placeholder with the ID
      finalCompanyName = `Organization-${id.substring(0, 8)}`;
      logger.debug(
        { id, finalCompanyName },
        "Using fallback organization name without database call",
      );
    }
  }

  // Import the utility function here to avoid circular dependencies
  const { unwrapOpenAISettings } = require("~/src/lib/utils/openai-settings");

  // Conditionally unwrap openai_settings - optimize to avoid unnecessary calls
  let unwrappedOpenAISettings = openai_settings;
  if (
    openai_settings &&
    typeof openai_settings === "object" &&
    "set" in openai_settings
  ) {
    try {
      unwrappedOpenAISettings = await unwrapOpenAISettings(openai_settings, {
        orgId: id,
        logIssue: true,
      });
    } catch (error) {
      // Log error but don't fail the mapping - use original settings
      logger.warn(
        { error, orgId: id },
        "Failed to unwrap OpenAI settings, using original",
      );
      unwrappedOpenAISettings = openai_settings;
    }
  }

  const mappedOrg: Organization = {
    id,
    name: finalCompanyName || "Organization", // Ensure name is properly set with fallback
    deleted_at,
    settings: settings as any,
    openai_settings: unwrappedOpenAISettings as any,
    current_usage: current_usage as any,
    // Use the subscription data we extracted above
    subscription: subscription,
    license:
      o.licenses.length > 0
        ? o.licenses.find((l: any) => l.organization_id === id)
        : undefined,
    current_user_role: o.members.filter((m: any) => m.organization_id === id)[0]
      .role_id,
    ...(userId && { is_main_owner: main_owner_id === userId }),
    members: o.members,
    main_owner_id,
    company_information: o.company_information,
    modules: modulesList,
  } as any;

  // Log the mapped organization
  // console.log("mapToOrg - mapped organization name:", mappedOrg.name);

  return mappedOrg;
}

// Stage 2: Handle non-critical operations that don't need to be in the same transaction
async function createOrgPostProcess(
  org: { id: string; name: string; email?: string },
  userId: string,
) {
  try {
    // Create Stripe customer - make this non-blocking
    try {
      await createCustomer({
        orgId: org.id,
        orgName: org.name,
        email: org.email!,
        userId,
      });
    } catch (stripeError) {
      // Log the Stripe error but don't fail the organization creation
      logger.warn(
        { error: stripeError, org, userId },
        "Failed to create Stripe customer, but organization was created successfully",
      );
    }

    // Send notification email - make this non-blocking
    try {
      const emailHtml = render(
        EmailNotif({
          userId: userId,
          orgName: org.name,
        }),
      );

      await transport.sendMail({
        to: [process.env.EMAIL_NOTIF, process.env.EMAIL_FROM],
        from: process.env.EMAIL_FROM,
        html: await emailHtml,
        subject: "New organization",
        text: `Hello,

      A new organization has been created.

      Organization Name: ${org.name}
      Created By: ${userId}`,
      });
    } catch (emailError) {
      // Log the email error but don't fail the organization creation
      logger.warn(
        { error: emailError, org, userId },
        "Failed to send organization creation notification email, but organization was created successfully",
      );
    }

    // Return complete organization model with custom subscription info
    // Without requiring a database subscription record
    return {
      id: org.id,
      name: org.name,
      is_main_owner: true,
      subscription: {
        id: "none",
        name: "No Subscription",
      },
      current_user_role: "owner",
      license: undefined,
      members: {
        role_id: "owner",
        user_id: userId,
        organization_id: org.id,
      },
      main_owner_id: userId,
      company_information: {} as unknown as Organization["company_information"],
      modules: [], // New organizations start with empty modules array
      settings: { chatEnabled: true } as unknown as any,
      current_usage: {
        maxDocuments: 0,
        maxKnowledgeBaseQAs: 0,
        maxChatQuestions: 0,
        maxBulkQuestionUpload: 0,
      },
      invites: [], // Initialize with empty array for new organizations
    };
  } catch (error) {
    logger.error(
      { error, org, userId },
      "Error in post-processing organization creation",
    );
    // Consider handling clean-up here if needed
    throw error;
  }
}

/**
 * Get organization modules with enhanced caching consideration
 */
export async function getOrgModules(
  userId: string,
  orgId: string,
): Promise<any[]> {
  try {
    // Validate organization membership first
    const userOrg = await prisma.member.findFirst({
      where: {
        user_id: userId,
        organization_id: orgId,
      },
    });

    if (!userOrg) {
      throw new Error("User not authorized for this organization");
    }

    const modules = await prisma.organizationModuleOverride.findMany({
      where: {
        organization_id: orgId,
      },
      orderBy: {
        created_at: "asc",
      },
    });

    return modules.map((module: any) => ({
      id: module.id,
      organizationId: module.organization_id,
      moduleId: module.module_id,
      isEnabled: module.is_enabled,
      reason: module.reason,
      createdAt: module.created_at,
      createdById: module.created_by_id,
    }));
  } catch (error) {
    console.error("Error fetching organization modules:", error);
    throw error;
  }
}

export {
  createOrg,
  updateOrgName,
  updateOrgSettings,
  updateOrgOpenAISettings,
  getUserOrgs,
  acceptInvite,
  acceptInviteVendor,
  getOrg,
  deleteOrg,
  updateOrgCurrentUsage,
  getDocumentTrainUsage,
  getKbTrainUsage,
  getQuestionnaireUsage,
  getChatUsage,
  usageCount,
};
