import {
  bypassedPrisma,
  guardedPrisma,
  prisma,
  tenantGuardedPrisma,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/lib/prisma";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { MemberSelector } from "@/models/member";
import { Scope as ScopeModel } from "@/models/scope";
import { createId } from "@paralleldrive/cuid2";
import { <PERSON>rismaClient, Scope } from "@askinfosec/database";

interface GetAllScopesParams {
  org: string;
}

const createBuiltInScopes = async (org: string): Promise<ScopeModel[]> => {
  const db = tenantGuardedPrisma(org);

  // avoids duplicate scopes in database
  try {
    const existingScopes = await db.scope.findMany({
      where: {
        organization_id: org,
        scope_type: { in: ["common", "vendor"] },
      },
    });

    // If we already have both built-in scopes, just return them
    if (existingScopes.length >= 2) {
      const scopes = await db.scope.findMany({
        where: { organization_id: org },
        include: { created_by: true, updated_by: true },
      });
      return scopes.map((scope) => ({
        id: scope.id,
        name: scope.name,
        description: scope.description,
        scope_type: scope.scope_type,
        organization_id: org,
        created_by_email: scope.created_by.email,
        created_by: {
          userId: scope.created_by.id,
          email: scope.created_by.email,
          name: scope.created_by.name,
          image: scope.created_by.image,
        },
        updated_by: {
          userId: scope.updated_by.id,
          email: scope.updated_by.email,
          name: scope.updated_by.name,
          image: scope.updated_by.image,
        },
        created_at: scope.created_at,
      })) as ScopeModel[];
    }

    const organization = await extendedPrisma.organization.findUnique({
      where: { id: org },
      select: {
        id: true,
        main_owner_id: true,
        company_name: true,
      },
    });

    if (!organization) {
      throw new Error(`Organization not found: ${org}`);
    }

    if (!organization.main_owner_id) {
      throw new Error(`No main_owner_id for organization: ${org}`);
    }

    // Create missing built-in scopes
    const requiredScopes = [
      {
        name: "Common",
        description: "Common scopes",
        scope_type: "common",
      },
      {
        name: "Outgoing",
        description: "Outgoing",
        scope_type: "vendor",
      },
    ];

    for (const scopeData of requiredScopes) {
      const existingScope = existingScopes.find(
        (s) => s.name === scopeData.name,
      );
      if (!existingScope) {
        await createScope({
          org,
          user: organization.main_owner_id,
          ...scopeData,
          db,
        });
      }
    }

    // Fetch and return the newly created scopes
    const newScopes = await db.scope.findMany({
      where: { organization_id: org },
      include: { created_by: true, updated_by: true },
    });

    return newScopes.map((scope) => ({
      id: scope.id,
      name: scope.name,
      description: scope.description,
      scope_type: scope.scope_type,
      organization_id: org,
      created_by_email: scope.created_by.email,
      created_by: {
        userId: scope.created_by.id,
        email: scope.created_by.email,
        name: scope.created_by.name,
        image: scope.created_by.image,
      },
      updated_by: {
        userId: scope.updated_by.id,
        email: scope.updated_by.email,
        name: scope.updated_by.name,
        image: scope.updated_by.image,
      },
      created_at: scope.created_at,
    })) as ScopeModel[];
  } catch (error) {
    console.error("Error in createBuiltInScopes:", {
      error,
      orgId: org,
      step: "organization lookup",
    });
    throw error;
  }
};

export async function getAllScopes({ org }: GetAllScopesParams) {
  const db = guardedPrisma({ orgId: org });

  const scopes = await db.scope.findMany({
    where: { organization_id: org },
    include: { created_by: true, updated_by: true },
  });

  // Deduplicate by name (case-insensitive, trimmed) and organization_id
  const uniqueScopesMap = new Map();
  for (const scope of scopes) {
    const key = `${scope.name.trim().toLowerCase()}-${scope.organization_id}`;
    if (!uniqueScopesMap.has(key)) {
      uniqueScopesMap.set(key, scope);
    }
  }
  const uniqueScopes = Array.from(uniqueScopesMap.values());

  // If unique scopes exist, return them with proper mapping
  if (uniqueScopes.length > 0) {
    return uniqueScopes.map((scope) => ({
      id: scope.id,
      name: scope.name,
      description: scope.description,
      scope_type: scope.scope_type,
      organization_id: org,
      created_by_email: scope.created_by.email,
      created_by: {
        userId: scope.created_by.id,
        email: scope.created_by.email,
        name: scope.created_by.name,
        image: scope.created_by.image,
      },
      updated_by: {
        userId: scope.updated_by.id,
        email: scope.updated_by.email,
        name: scope.updated_by.name,
        image: scope.updated_by.image,
      },
      created_at: scope.created_at,
    })) as ScopeModel[];
  }

  // If no scopes exist, create and return built-in scopes
  const builtInScopes = await createBuiltInScopes(org);
  return builtInScopes;
}

export interface CreateScopeParams {
  org: string;
  user: string;
  name: string;
  description: string;
  scope_type: string;
  db: PrismaClient;
}
export async function createScope({
  org,
  user,
  name,
  description,
  scope_type,
  db,
}: CreateScopeParams) {
  // Prevent duplicate scope insertion (case-insensitive, trimmed)
  const s = await db.scope.findFirst({
    where: {
      name: { equals: name.trim(), mode: "insensitive" },
      organization_id: org,
    },
  });

  if (s) throw Error("Scope already exists");

  const u = await prisma.user.findFirst({
    where: {
      id: user,
    },
  });

  if (!u) throw Error("User does not exists");

  const scope = await db
    .$extends(
      withAuditLogger({ orgId: org, userId: user, message: "Scope created" }),
    )
    .scope.create({
      data: {
        id: createId(),
        name: name.trim(),
        description,
        scope_type,
        created_by_id: user,
        updated_by_id: user,
        organization_id: org,
      },
    });

  return scope;
}

export async function updateScope({
  id,
  org,
  user,
  name,
  description,
  scope_type,
  db,
}: CreateScopeParams & { id: string }) {
  // TODO: This is acting funky, It doesnt return anything..
  const s = await db.scope.findFirst({
    where: {
      id: id,
    },
  });
  if (!s) throw Error("Scope does not exists.");

  const u = await prisma.user.findFirst({
    where: {
      id: user,
    },
  });

  if (!u) throw Error("User does not exists");

  const scope = await db
    .$extends(
      withAuditLogger({ orgId: org, userId: user, message: "Scope updated." }),
    )
    .scope.update({
      where: {
        id,
      },
      data: {
        name,
        description,
        scope_type,
        updated_by_id: user,
      },
    });

  return scope;
}
