import { AllowedUrlPatternType } from "@/types/enums";
import { PrismaClient, type AllowedUrlPattern } from "@askinfosec/database";
import { bypassedPrisma } from "@/lib/prisma";
import { OpenAISettings } from "~/src/models/organization";

interface UrlCheckResult {
  isAllowed: boolean;
  reason?: string;
  deniedUrl?: string;
}

/**
 * Normalizes a URL for consistent matching.
 * - Converts to lowercase.
 * - Removes trailing slashes.
 * - TODO: Potentially remove www. prefix for domain matching if desired.
 * @param urlString The URL to normalize.
 * @returns Normalized URL string.
 */
function normalizeUrl(urlString: string): string {
  try {
    const url = new URL(urlString.toLowerCase());
    let normalized = `${url.protocol}//${url.host}${url.pathname}`;
    if (normalized.endsWith("/")) {
      normalized = normalized.slice(0, -1);
    }
    // Consider query parameters and fragments? For now, path-based matching.
    return normalized;
  } catch (error) {
    // If URL is invalid, return original string in lowercase for basic matching attempts
    // This might happen for partial patterns like "example.com"
    let simpleNormalized = urlString.toLowerCase();
    if (simpleNormalized.endsWith("/")) {
      simpleNormalized = simpleNormalized.slice(0, -1);
    }
    return simpleNormalized;
  }
}

/**
 * Checks if a given URL matches a specific AllowedUrlPattern.
 * @param targetUrl The URL to check (should be pre-normalized).
 * @param patternEntry The AllowedUrlPattern entry from the database.
 * @returns True if the URL matches the pattern, false otherwise.
 */
function matchPattern(
  targetUrl: string,
  patternEntry: AllowedUrlPattern,
): boolean {
  const normalizedPattern = normalizeUrl(patternEntry.pattern);

  // For robust domain/prefix matching, ensure targetUrl is a full URL
  let targetUrlObj: URL | null = null;
  try {
    targetUrlObj = new URL(targetUrl);
  } catch (e) {
    /* Not a full URL, can only match EXACT or simple PREFIX */
  }

  switch (patternEntry.type) {
    case AllowedUrlPatternType.EXACT:
      return targetUrl === normalizedPattern;

    case AllowedUrlPatternType.PREFIX:
      return targetUrl.startsWith(normalizedPattern);

    case AllowedUrlPatternType.DOMAIN:
      if (!targetUrlObj) return false; // Cannot match domain if target is not a valid URL
      const patternDomain = normalizedPattern.includes("//")
        ? new URL(normalizedPattern).hostname
        : normalizedPattern;
      const targetDomain = targetUrlObj.hostname;

      if (patternEntry.includeSubdomains) {
        return (
          targetDomain === patternDomain ||
          targetDomain.endsWith(`.${patternDomain}`)
        );
      } else {
        return targetDomain === patternDomain;
      }

    default:
      return false;
  }
}

export class UrlValidationService {
  static prisma = bypassedPrisma as unknown as PrismaClient;
  /**
   * Checks if a URL is allowed based on organization and assistant settings.
   * @param url The URL to validate.
   * @param organizationId The ID of the organization.
   * @param assistantId The ID of the assistant making the request.
   * @returns True if the URL is allowed, false otherwise.
   */
  static async isUrlAllowed(
    url: string,
    organizationId: string,
    assistantId: string, // assistantId is used to fetch assistant-specific config if needed in future
  ): Promise<UrlCheckResult> {
    if (!url || !organizationId || !assistantId) {
      console.warn("isUrlAllowed: Missing required parameters.");
      return {
        isAllowed: false,
        reason: "Missing required parameters for URL validation.",
        deniedUrl: url,
      };
    }

    const org = await UrlValidationService.prisma.organization.findFirst({
      where: { id: organizationId },
      select: {
        openai_settings: true,
        allowed_url_pattern: {
          where: { isEnabled: true },
        },
      },
    });

    if (!org || !org.openai_settings) {
      console.warn(
        `isUrlAllowed: Organization or OpenAI settings not found for org ${organizationId}. Default to allowed`,
      );
      // Consider if this case should be a denial if settings are crucial
      return {
        isAllowed: true,
        reason:
          "Organization or OpenAI settings not found, defaulting to allowed.",
      };
    }

    const openAISettings = org.openai_settings as unknown as OpenAISettings; // Cast from JsonValue

    if (openAISettings && !openAISettings.enforceUrlScope) {
      return {
        isAllowed: true,
        reason: "URL scope enforcement is disabled for this assistant.",
      };
    }

    // If enforceUrlScope is true (or undefined and we default to enforcing)
    if (!org.allowed_url_pattern || org.allowed_url_pattern.length === 0) {
      const denialReason = `URL enforcement is active, but no URL patterns are defined for organization ${organizationId}.`;
      console.log(`isUrlAllowed: ${denialReason} Denying URL: ${url}`);
      return { isAllowed: false, reason: denialReason, deniedUrl: url };
    }

    const normalizedTargetUrl = normalizeUrl(url);

    for (const pattern of org.allowed_url_pattern) {
      // Type assertion for pattern if necessary, or ensure Kysely types are used if it's a DTO
      const typedPattern = pattern as unknown as AllowedUrlPattern;
      if (matchPattern(normalizedTargetUrl, typedPattern)) {
        return { isAllowed: true };
      }
    }

    const noMatchReason = `URL did not match any allowed patterns for organization ${organizationId}.`;
    console.log(
      `isUrlAllowed: URL '${url}' (normalized: '${normalizedTargetUrl}') did not match any patterns for org ${organizationId}. Denying.`,
    );
    return { isAllowed: false, reason: noMatchReason, deniedUrl: url };
  }
}
