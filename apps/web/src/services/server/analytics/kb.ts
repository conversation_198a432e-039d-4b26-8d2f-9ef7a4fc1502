import "server-only";
import { prisma } from "@/lib/prisma";
import { Prisma } from "@askinfosec/database";

interface PrismaParams {
  orgId: string;
  userId?: string;
}

interface KbAnalytics {
  FrequencyAndRecencyOfUpdatesAndVerifications: [];
  QuestionsAndAnswersByCategory: [];
  NumberAndTypesOfEvidences: [];
}

async function kbAnalytics({ orgId }: PrismaParams) {
  const data: KbAnalytics = {
    FrequencyAndRecencyOfUpdatesAndVerifications: [],
    QuestionsAndAnswersByCategory: [],
    NumberAndTypesOfEvidences: [],
  };
  const sql1 = Prisma.sql`SELECT question, MAX(updated_at) AS last_update, MAX(last_verification_date) AS last_verification, COUNT(updated_at) AS update_count, COUNT(last_verification_date) AS verification_count FROM knowledge_base WHERE organization_id = ${orgId} GROUP BY question ORDER BY verification_count DESC, update_count DESC`;
  const sql2 = Prisma.sql`SELECT category_id, COUNT(question) AS question_count, category.name FROM knowledge_base JOIN category ON knowledge_base.category_id = category.id WHERE organization_id = ${orgId} GROUP BY category_id, category.name ORDER BY question_count DESC`;
  const sql3 = Prisma.sql`SELECT question, answer, COUNT(evidence.id) AS evidence_count FROM knowledge_base LEFT JOIN evidence ON knowledge_base.id = evidence."knowledgeBaseId" WHERE organization_id = ${orgId} GROUP BY question, answer ORDER BY evidence_count DESC`;
  const finalData = await prisma.$transaction(async (tx) => {
    await tx.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`;
    const sql1Result = await tx.$queryRaw`${sql1}, ${orgId}`;
    const sql2Result = await tx.$queryRaw`${sql2}, ${orgId}`;
    const sql3Result = await tx.$queryRaw`${sql3}, ${orgId}`;
    return {
      ...data,
      FrequencyAndRecencyOfUpdatesAndVerifications: (
        sql1Result as unknown as []
      ).filter((r: any) => r.update_count > 0),
      QuestionsAndAnswersByCategory: sql2Result as unknown as [],
      NumberAndTypesOfEvidences: (sql3Result as unknown as []).filter(
        (r: any) => r.evidence_count > 0,
      ),
    };
  });
  return finalData;
}

// async function getSimilarityAndDiversityOfQuestions({ orgId }: PrismaParams) {
//   // Use Prisma Client to get all the questions and their vectors
//   const data = await guardedPrisma({ orgId }).knowledgeBase.findMany({
//     select: {
//       question: true,
//     },
//   });

//   // Define an array to store the similarity scores. Use langchain to calculate similarity of questions in knowledgebase.
//   const similarityScores: never[] = [];

//   // Return the array
//   return similarityScores;
// }

// The average length and sentiment of the comments. This metric can help you measure the feedback and satisfaction of the users who interact with your knowledge base. You can use the comment field to calculate the length and sentiment of each comment using natural language processing techniques.

export { kbAnalytics };
