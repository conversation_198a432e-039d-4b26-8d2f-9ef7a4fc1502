"use server";
import { guardedPrisma, otherDataAuditLog } from "@/lib/prisma";
import { JsonValue } from "@askinfosec/database";
import { getRisk } from "@/services/server/risk-management/retrieve-actions";

export interface RiskScoreModel {
  impact: number;
  impact_note: string;
  vulnerability: number;
  vulnerability_note: string;
  likelihood: number;
  likelihood_note: string;
  last_update_by: string;
  last_update_on: string;
}

async function getLatestInherentRisk(
  orgId: string,
  userId: string,
  riskId: string,
): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const risk = await db.risk.findUnique({
    where: { id: riskId },
    select: {
      id: true,
      inherent_risk: true,
    },
  });
  if (risk && risk.inherent_risk) {
    const inherent_risk_JSON = risk.inherent_risk as JsonValue[];
    const latest_inherent_risk = (inherent_risk_JSON.length > 0
      ? (inherent_risk_JSON[
          inherent_risk_JSON.length - 1
        ] as unknown as RiskScoreModel)
      : inherent_risk_JSON[0]) as unknown as RiskScoreModel;
    return JSON.stringify({
      status: "success",
      data: latest_inherent_risk,
    });
  } else {
    return JSON.stringify({ status: "success", message: "Risk not found." });
  }
}

async function getLatestResidualRisk(
  orgId: string,
  userId: string,
  riskId: string,
): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const risk = await db.risk.findUnique({
    where: { id: riskId },
    select: {
      id: true,
      residual_risk: true,
    },
  });
  if (risk && risk.residual_risk) {
    const residual_risk_JSON =
      risk.residual_risk as unknown as RiskScoreModel[];
    const latest_residual_risk = (residual_risk_JSON.length > 0
      ? (residual_risk_JSON[
          residual_risk_JSON.length - 1
        ] as unknown as RiskScoreModel)
      : residual_risk_JSON[0]) as unknown as RiskScoreModel;
    return JSON.stringify({
      status: "success",
      data: latest_residual_risk,
    });
  } else {
    return JSON.stringify({ status: "success", message: "Risk not found." });
  }
}

async function saveRiskScoreModel(
  orgId: string,
  userId: string,
  riskId: string,
  riskKind: "inherent" | "residual",
  riskScore: RiskScoreModel,
): Promise<string> {
  try {
    const db = guardedPrisma({ orgId, userId, checkMembership: false });
    const currentRisk = await db.risk.findUnique({
      where: {
        id: riskId,
        organization_id: orgId,
      },
      select: {
        inherent_risk: true,
        residual_risk: true,
      },
    });
    if (!currentRisk) {
      return JSON.stringify({ status: "error", message: "Risk id not found." });
    }
    // TODO: Let's auto save the existing notes
    if (riskKind === "inherent") {
      const updatedRisk = await db.$extends(otherDataAuditLog()).risk.update({
        where: {
          id: riskId,
          organization_id: orgId,
        },
        data: {
          inherent_risk:
            // If it satisfies the condtion, it means that no existing score yet.
            !currentRisk.inherent_risk || currentRisk.inherent_risk === null
              ? ([riskScore] as unknown as JsonValue[]) // first ever score
              : ([
                  ...(currentRisk.inherent_risk as JsonValue[]),
                  riskScore,
                ] as unknown as JsonValue[]), // else, append to the last
        },
      });
      return JSON.stringify({ status: "success", data: updatedRisk });
    } else {
      const updatedRisk = await db.risk.update({
        where: {
          id: riskId,
          organization_id: orgId,
        },
        data: {
          residual_risk:
            !currentRisk.residual_risk || currentRisk.residual_risk === null
              ? ([riskScore] as unknown as JsonValue[])
              : ([
                  ...(currentRisk.residual_risk as JsonValue[]),
                  riskScore,
                ] as unknown as JsonValue[]),
        },
      });
      const risk = await getRisk(orgId, userId, riskId);
      return JSON.stringify({ status: "success", data: risk });
    }
  } catch (error) {
    return JSON.stringify({ status: "error", message: error });
  }
}

async function addNoteToRiskFactor(
  orgId: string,
  userId: string,
  riskId: string,
  riskKind: "inherent" | "residual",
  factor: "impact" | "vulnerability" | "likelihood",
  note: string,
): Promise<string> {
  try {
    const db = guardedPrisma({ orgId, userId, checkMembership: false });
    const risk = await db.risk.findUnique({
      where: {
        id: riskId,
        organization_id: orgId,
      },
      select: {
        inherent_risk: true,
        residual_risk: true,
      },
    });
    if (!risk) {
      return JSON.stringify({ status: "error", message: "Risk not found." });
    }
    const inherentOrResidual =
      riskKind === "inherent" ? risk.inherent_risk : risk.residual_risk;
    const riskInherentOrResidualArray = JSON.parse(
      JSON.stringify(inherentOrResidual),
    );
    const lastItem =
      riskInherentOrResidualArray[riskInherentOrResidualArray.length - 1];
    if (factor === "impact") {
      lastItem.impact_note = note;
    } else if (factor === "vulnerability") {
      lastItem.vulnerability_note = note;
    } else {
      lastItem.likelihood_note = note;
    }
    lastItem.last_updated_by = userId;
    lastItem.last_updated_on = new Date().toLocaleDateString();
    // Replace the last item in the array of inherent_risk or residual_risk
    riskInherentOrResidualArray[riskInherentOrResidualArray.length - 1] =
      lastItem;
    // Update the risk record
    const updatedRisk = await db.$extends(otherDataAuditLog()).risk.update({
      where: {
        id: riskId,
        organization_id: orgId,
      },
      data: {
        [riskKind === "inherent" ? "inherent_risk" : "residual_risk"]:
          riskInherentOrResidualArray,
      },
    });
    const latestRiskScoreModel =
      riskKind === "inherent"
        ? await getLatestInherentRisk(orgId, userId, riskId)
        : await getLatestResidualRisk(orgId, userId, riskId);
    return JSON.stringify({
      status: "success",
      data: JSON.parse(latestRiskScoreModel).data as RiskScoreModel,
    });
  } catch (error) {
    return JSON.stringify({ status: "error", message: error });
  }
}
export {
  getLatestInherentRisk,
  getLatestResidualRisk,
  saveRiskScoreModel,
  addNoteToRiskFactor,
};
