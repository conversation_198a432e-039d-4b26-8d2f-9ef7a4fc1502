"use server";
import {
  bypassedP<PERSON><PERSON>,
  guardedPrisma,
  tenantGuardedPrisma,
} from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { stripe } from "@/lib/stripe";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import crypto from "crypto";
import type Stripe from "stripe";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { syncOrganizationModules } from "./org-module-sync";

interface SaveProductParams {
  id: string;
  name: string;
  description: string;
}

async function saveProduct({ id, name, description }: SaveProductParams) {
  return await extendedPrisma.product.upsert({
    where: { id },
    update: { name, description, stripe_product_id: id },
    create: { id, name, description, stripe_product_id: id },
  });
}

async function deleteProduct({ id }: { id: string }) {
  try {
    return await extendedPrisma.product.delete({ where: { id } });
  } catch (err) {
    return {};
  }
}

interface SavePriceParams {
  id: string;
  productId: string;
  description: string;
}

async function savePrice({ id, productId, description }: SavePriceParams) {
  return await extendedPrisma.subscription.upsert({
    where: { id },
    update: { description, product_id: productId },
    create: { id, description, product_id: productId },
  });
}

async function deletePrice({ id }: { id: string }) {
  try {
    return await extendedPrisma.subscription.delete({ where: { id } });
  } catch (err) {
    return {};
  }
}

interface CreateCustomerParams {
  orgId: string;
  orgName: string;
  email: string;
  userId: string;
}

async function createCustomer({
  orgId,
  orgName,
  email,
  userId,
}: CreateCustomerParams) {
  // Create the Stripe customer
  const customer = await stripe.customers.create({
    email,
    name: orgName,
    metadata: {
      org_id: orgId,
      org_name: orgName,
      user_id: userId,
    },
  });

  // Update the organization with the new Stripe customer ID using raw SQL
  try {
    const prisma = guardedPrisma({ orgId, userId });
    await prisma.organization.update({
      where: { id: orgId },
      data: { stripe_customer_id: customer.id },
    });
    logger.info(
      `Updated organization with Stripe customer ID: orgId=${orgId}, customerId=${customer.id}`,
    );
  } catch (error) {
    logger.error(
      `Failed to update organization with Stripe customer ID: ${error instanceof Error ? error.message : String(error)}, orgId=${orgId}, customerId=${customer.id}`,
    );
  }

  return customer;
}

/**
 * Retrieves the Stripe customer ID for an organization
 * Checks if the organization has a stripe_customer_id field
 * If not, checks if there are any Stripe customers with the organization ID in metadata
 */
async function retrieveCustomerId({ orgId }: { orgId: string }) {
  const requestId = crypto.randomUUID();
  logger.info(
    { requestId, orgId },
    "Starting Stripe customer ID retrieval process",
  );

  // First, check if the organization has a stripe_customer_id
  const organization = await extendedPrisma.organization.findUnique({
    where: { id: orgId },
  });

  // @ts-ignore - The stripe_customer_id field exists in the database but might not be in the Prisma types yet
  if (organization && organization.stripe_customer_id) {
    logger.info(
      { requestId, orgId, customerId: organization.stripe_customer_id },
      "Found existing Stripe customer ID in organization record",
    );
    // @ts-ignore
    return organization.stripe_customer_id;
  }

  logger.info(
    { requestId, orgId },
    "No Stripe customer ID found in organization record, searching in Stripe",
  );

  // If no customer ID is found, try to find a Stripe customer with this org ID
  try {
    // Get all customers and filter manually since Stripe API doesn't support direct metadata search
    const customers = await stripe.customers.list({
      limit: 100,
      expand: ["data.subscriptions"],
    });

    logger.info(
      { requestId, orgId, totalCustomers: customers.data.length },
      "Retrieved customers from Stripe, searching for matching org_id in metadata",
    );

    // Filter customers to find those with matching org_id in metadata
    const matchingCustomers = customers.data.filter(
      (customer) => customer.metadata && customer.metadata.org_id === orgId,
    );

    logger.info(
      { requestId, orgId, matchingCustomersCount: matchingCustomers.length },
      "Found customers with matching org_id in metadata",
    );

    if (matchingCustomers.length > 0) {
      const customerId = matchingCustomers[0].id;

      logger.info(
        {
          requestId,
          orgId,
          customerId,
          customerEmail: matchingCustomers[0].email,
        },
        "Found matching Stripe customer, updating organization record",
      );

      // If we found a customer, update the organization with this customer ID
      // const prisma = tenantGuardedPrisma(orgId);
      await extendedPrisma.organization.update({
        where: { id: orgId },
        data: { stripe_customer_id: customerId },
      });

      logger.info(
        { requestId, orgId, customerId },
        "Successfully updated organization with Stripe customer ID",
      );

      return customerId;
    } else {
      logger.info(
        { requestId, orgId },
        "No matching Stripe customers found with this org_id in metadata",
      );
    }
  } catch (error) {
    logger.error(
      { error, requestId, orgId },
      "Failed to search for Stripe customers",
    );
  }

  // If no customer is found, return null
  logger.info(
    { requestId, orgId },
    "No Stripe customer found for this organization",
  );
  return null;
}

async function manageSubscriptionStatusChange({
  subscriptionId,
  customerId,
  organizationSubscriptionId = null,
}: {
  /** Stripe Subscription ID */
  subscriptionId: string;
  /** Stripe Customer ID */
  customerId: string;
  /** Organization Subscription ID (if available) */
  organizationSubscriptionId?: string | null;
}) {
  const requestId = crypto.randomUUID();
  logger.debug(
    { requestId, subscriptionId, customerId, organizationSubscriptionId },
    "Managing subscription status change",
  );
  logger.info(
    { requestId, subscriptionId, customerId, organizationSubscriptionId },
    "Managing subscription status change",
  );

  // If we have a local subscription ID, try to find it first
  if (organizationSubscriptionId) {
    try {
      const orgSub = await extendedPrisma.organizationSubscription.findUnique({
        where: { id: organizationSubscriptionId },
        include: { organization: true },
      });

      if (orgSub) {
        logger.debug(
          { requestId, organizationSubscriptionId },
          "Found organization subscription with ID",
        );

        // Update the local subscription with the Stripe subscription ID
        await extendedPrisma.organizationSubscription.update({
          where: { id: organizationSubscriptionId },
          data: {
            stripe_sub_id: subscriptionId,
          },
        });

        logger.info(
          {
            requestId,
            subscriptionId,
            organizationSubscriptionId,
            orgId: orgSub.organization_id,
          },
          "UPDATED_ORGANIZATION_SUBSCRIPTION_WITH_STRIPE_ID",
        );

        // Continue with the rest of the function to update other fields
      }
    } catch (error) {
      logger.error(
        { requestId, subscriptionId, organizationSubscriptionId, error },
        "Error finding/updating local subscription",
      );
      logger.error(
        { requestId, subscriptionId, organizationSubscriptionId, error },
        "FAILED_TO_UPDATE_ORGANIZATION_SUBSCRIPTION",
      );
      // Continue with the normal flow
    }
  }

  try {
    // Find the organizationSubscription to which the Stripe Subscription ID belongs
    const orgSub = await extendedPrisma.organizationSubscription.findFirst({
      where: {
        stripe_sub_id: subscriptionId,
      },
      include: {
        organization: true,
      },
    });

    // If we found an organization subscription, make sure the organization has the customer ID
    if (orgSub && orgSub.organization) {
      // Use tenantGuardedPrisma since we have the orgId
      const prismaTenanGuarded = tenantGuardedPrisma(orgSub.organization_id);

      // Update the organization with the Stripe customer ID if needed
      await prismaTenanGuarded.organization.updateMany({
        where: {
          id: orgSub.organization_id,
          OR: [{ stripe_customer_id: null }, { stripe_customer_id: "" }],
        },
        data: {
          stripe_customer_id: customerId,
        },
      });
    }

    // Retrieve the Stripe subscription details
    const stripeSubscriptionResponse = (await stripe.subscriptions.retrieve(
      subscriptionId,
      {
        expand: ["default_payment_method", "items.data.price.product"],
      },
    )) as Stripe.Response<Stripe.Subscription>;

    // Cast to a more complete type that includes current_period_start and current_period_end
    const stripeSubscription =
      stripeSubscriptionResponse as unknown as Stripe.Subscription & {
        current_period_start: number;
        current_period_end: number;
      };

    if (!stripeSubscription || typeof stripeSubscription !== "object") {
      throw new Error("Invalid subscription data received from Stripe");
    }

    // Get the price ID from the subscription
    const priceId = stripeSubscription.items.data[0].price.id;

    // Find the subscription tier (Stripe Price) to which the Stripe Subscription ID belongs
    const subscriptionTier = await extendedPrisma.subscriptionTier.findFirst({
      where: {
        price: { stripe_price_id: priceId },
      },
      include: {
        subscription: true,
      },
    });

    // If we can't find the subscription tier, try to find it by price_id directly
    const subscription =
      subscriptionTier ||
      (await extendedPrisma.subscriptionTier.findFirst({
        where: {
          price_id: priceId,
        },
        include: {
          subscription: true,
        },
      }));

    if (!subscription) {
      logger.error(
        { requestId, subscriptionId, customerId, priceId },
        "STRIPE_PRICE_NOT_FOUND",
      );
      return;
    }

    // Get the product ID
    const productId = subscription.subscription.product_id;

    // If we found an existing organization subscription with this Stripe subscription ID
    if (orgSub) {
      try {
        // Update the existing subscription
        const updated = await extendedPrisma.organizationSubscription.update({
          where: { id: orgSub.id },
          data: {
            stripe_sub_id: subscriptionId,
            subscription_tier_id: subscription.id, // Set the subscription_tier_id
            current_period_start: new Date(
              stripeSubscription.current_period_start * 1000,
            ),
            current_period_end: new Date(
              stripeSubscription.ended_at
                ? stripeSubscription.ended_at * 1000
                : 0,
            ),
            cancel_at_period_end: Boolean(
              stripeSubscription.cancel_at_period_end,
            ),
            canceled_at: stripeSubscription.canceled_at
              ? new Date(stripeSubscription.canceled_at * 1000)
              : null,
          },
        });

        logger.info(
          { requestId, subscriptionId, orgSubId: orgSub.id, productId },
          "ORGANIZATION_SUBSCRIPTION_UPDATED_SUCCESSFULLY",
        );

        // Sync modules after subscription update/create
        try {
          await syncOrganizationModules(updated.organization_id);
        } catch (syncError) {
          logger.error(
            { orgId: updated.organization_id, syncError },
            "Failed to sync org modules after Stripe metadata change",
          );
        }
        return updated;
      } catch (error) {
        logger.error(
          { requestId, subscriptionId, customerId, orgSubId: orgSub.id, error },
          "FAILED_TO_UPDATE_ORGANIZATION_SUBSCRIPTION",
        );
        throw error;
      }
    }

    // If we didn't find an existing subscription with this Stripe subscription ID,
    // we need to find the organization by customer ID
    // Using raw SQL query to find the organization by stripe_customer_id
    const orgWithCustomerResult = await extendedPrisma.$queryRaw`
      SELECT id FROM organization WHERE stripe_customer_id = ${customerId} LIMIT 1
    `;

    // Convert the result to a more usable format
    const orgWithCustomer = orgWithCustomerResult as Array<{ id: string }>;
    const orgId = orgWithCustomer[0]?.id;

    if (!orgId) {
      // Try to find the organization from Stripe customer metadata
      try {
        const customer = await stripe.customers.retrieve(customerId);
        if (customer.deleted) {
          logger.error(
            { requestId, subscriptionId, customerId },
            "STRIPE_CUSTOMER_DELETED",
          );
          return;
        }

        const orgId = customer.metadata?.org_id;
        if (!orgId) {
          logger.error(
            { requestId, subscriptionId, customerId },
            "STRIPE_CUSTOMER_HAS_NO_ORG_ID_METADATA",
          );
          return;
        }

        // Check if there's already an active subscription with the same tier for this organization
        const existingActiveTierSubscription =
          await extendedPrisma.organizationSubscription.findFirst({
            where: {
              organization_id: orgId,
              subscription_tier_id: subscription.id,
              stripe_sub_id: { not: null },
              OR: [{ canceled_at: null }, { canceled_at: { gt: new Date() } }],
            },
          });

        // Also check for any subscription with the same tier (active or inactive)
        let existingTierSubscription = existingActiveTierSubscription;

        if (!existingTierSubscription) {
          existingTierSubscription =
            await extendedPrisma.organizationSubscription.findFirst({
              where: {
                organization_id: orgId,
                subscription_tier_id: subscription.id,
              },
              orderBy: {
                created_at: "desc",
              },
            });
        }

        // Check for any active subscription for this tier (needed in the else branch below)
        const anyActiveSubscription =
          existingActiveTierSubscription ||
          (await extendedPrisma.organizationSubscription.findFirst({
            where: {
              organization_id: orgId,
              subscription_tier_id: subscription.id,
              stripe_sub_id: { not: null },
              OR: [{ canceled_at: null }, { canceled_at: { gt: new Date() } }],
            },
          }));

        let updatedOrNewSubscription;

        if (existingTierSubscription) {
          // If there's an active subscription and it's not the one we're updating, deactivate it first
          if (
            anyActiveSubscription &&
            anyActiveSubscription.id !== existingTierSubscription.id &&
            anyActiveSubscription.stripe_sub_id !== subscriptionId
          ) {
            logger.info(
              {
                requestId,
                subscriptionId,
                customerId,
                orgId,
                existingActiveSubId: anyActiveSubscription.id,
              },
              "DEACTIVATING_PREVIOUS_ACTIVE_SUBSCRIPTION_BEFORE_UPDATING",
            );

            await extendedPrisma.organizationSubscription.update({
              where: { id: anyActiveSubscription.id },
              data: {
                end_date: new Date(),
              },
            });
          }

          // Update the existing subscription instead of creating a new one
          logger.info(
            {
              requestId,
              subscriptionId,
              customerId,
              orgId,
              existingSubId: existingTierSubscription.id,
            },
            "FOUND_EXISTING_SUBSCRIPTION_WITH_SAME_TIER_UPDATING_FROM_METADATA",
          );

          updatedOrNewSubscription =
            await extendedPrisma.organizationSubscription.update({
              where: { id: existingTierSubscription.id },
              data: {
                stripe_sub_id: subscriptionId,
                current_period_start: new Date(
                  stripeSubscription.current_period_start * 1000,
                ),
                current_period_end: new Date(
                  stripeSubscription.current_period_end * 1000,
                ),
                cancel_at_period_end: Boolean(
                  stripeSubscription.cancel_at_period_end,
                ),
                canceled_at: stripeSubscription.canceled_at
                  ? new Date(stripeSubscription.canceled_at * 1000)
                  : null,
              },
            });
        } else {
          // Check if there's an existing subscription record without a stripe_sub_id
          // This could happen if a record was created during checkout but not updated by the webhook
          const pendingSubscription =
            await extendedPrisma.organizationSubscription.findFirst({
              where: {
                organization_id: orgId,
                subscription_tier_id: subscription.id,
                stripe_sub_id: null,
              },
              orderBy: {
                created_at: "desc",
              },
            });

          if (pendingSubscription) {
            // Update the pending subscription with the Stripe subscription ID
            logger.info(
              {
                requestId,
                subscriptionId,
                customerId,
                orgId,
                pendingSubId: pendingSubscription.id,
              },
              "FOUND_PENDING_SUBSCRIPTION_UPDATING_WITH_STRIPE_ID_FROM_METADATA",
            );

            updatedOrNewSubscription =
              await extendedPrisma.organizationSubscription.update({
                where: { id: pendingSubscription.id },
                data: {
                  stripe_sub_id: subscriptionId,
                  start_date: new Date(stripeSubscription.start_date * 1000),
                  current_period_start: new Date(
                    stripeSubscription.current_period_start * 1000,
                  ),
                  current_period_end: new Date(
                    stripeSubscription.current_period_end * 1000,
                  ),
                  cancel_at_period_end: Boolean(
                    stripeSubscription.cancel_at_period_end,
                  ),
                  canceled_at: stripeSubscription.canceled_at
                    ? new Date(stripeSubscription.canceled_at * 1000)
                    : null,
                },
              });
          } else {
            // Create a new organization subscription
            logger.info(
              {
                requestId,
                subscriptionId,
                customerId,
                orgId,
              },
              "CREATING_NEW_SUBSCRIPTION_RECORD_FROM_METADATA",
            );

            // First check if there's any subscription for this tier, regardless of stripe_sub_id
            // This is a fallback in case the unique constraint was removed
            console.log(
              `[DEBUG] Double-checking for ANY subscription with this tier (metadata) - OrgID: ${orgId}, TierID: ${subscription.id}`,
            );
            const anyTierSubscription =
              await extendedPrisma.organizationSubscription.findFirst({
                where: {
                  organization_id: orgId,
                  subscription_tier_id: subscription.id,
                },
                orderBy: {
                  created_at: "desc",
                },
              });

            if (anyTierSubscription) {
              // If we found any subscription with this tier, update it instead of creating a new one
              console.log(
                `[DEBUG] Found existing tier subscription to update (metadata): ${anyTierSubscription.id}`,
              );
              updatedOrNewSubscription =
                await extendedPrisma.organizationSubscription.update({
                  where: { id: anyTierSubscription.id },
                  data: {
                    stripe_sub_id: subscriptionId,
                    start_date: new Date(stripeSubscription.start_date * 1000),
                    current_period_start: new Date(
                      stripeSubscription.current_period_start * 1000,
                    ),
                    current_period_end: new Date(
                      stripeSubscription.current_period_end * 1000,
                    ),
                    cancel_at_period_end: Boolean(
                      stripeSubscription.cancel_at_period_end,
                    ),
                    canceled_at: stripeSubscription.canceled_at
                      ? new Date(stripeSubscription.canceled_at * 1000)
                      : null,
                  },
                });
            } else {
              // If no subscription exists for this tier at all, create a new one
              console.log(
                `[DEBUG] Creating brand new subscription record (metadata)`,
              );
              updatedOrNewSubscription =
                await extendedPrisma.organizationSubscription.create({
                  data: {
                    organization_id: orgId,
                    subscription_tier_id: subscription.id, // Set the subscription_tier_id
                    stripe_sub_id: subscriptionId,
                    start_date: new Date(stripeSubscription.start_date * 1000),
                    current_period_start: new Date(
                      stripeSubscription.current_period_start * 1000,
                    ),
                    current_period_end: new Date(
                      stripeSubscription.current_period_end * 1000,
                    ),
                    cancel_at_period_end: Boolean(
                      stripeSubscription.cancel_at_period_end,
                    ),
                    canceled_at: stripeSubscription.canceled_at
                      ? new Date(stripeSubscription.canceled_at * 1000)
                      : null,
                  },
                });
            }
          }
        }

        logger.info(
          {
            requestId,
            stripeSubscriptionId: subscriptionId,
            customerId,
            orgId,
            orgSubscriptionId: updatedOrNewSubscription.id,
          },
          existingTierSubscription
            ? "EXISTING_ORGANIZATION_SUBSCRIPTION_UPDATED_FROM_METADATA"
            : "NEW_ORGANIZATION_SUBSCRIPTION_CREATED_FROM_CUSTOMER_METADATA",
        );

        // Sync modules after subscription update/create
        try {
          await syncOrganizationModules(
            updatedOrNewSubscription.organization_id,
          );
        } catch (syncError) {
          logger.error(
            { orgId: updatedOrNewSubscription.organization_id, syncError },
            "Failed to sync org modules after Stripe metadata change",
          );
        }
        return updatedOrNewSubscription;
      } catch (error) {
        logger.error(
          { requestId, subscriptionId, customerId, error },
          "FAILED_TO_CREATE_ORGANIZATION_SUBSCRIPTION_FROM_CUSTOMER_METADATA",
        );
        throw error;
      }
    }

    // Use tenantGuardedPrisma since we have the orgId
    const prismaTenanGuarded = tenantGuardedPrisma(orgId);

    // Check if there's already an active subscription with the same tier for this organization
    const existingActiveTierSubscription =
      await prismaTenanGuarded.organizationSubscription.findFirst({
        where: {
          subscription_tier_id: subscription.id,
          canceled_at: null,
        },
      });

    // Check for any non-canceled subscription with the same tier
    let existingTierSubscription = existingActiveTierSubscription;

    if (!existingTierSubscription) {
      // Look for a non-canceled subscription with the same tier
      existingTierSubscription =
        await prismaTenanGuarded.organizationSubscription.findFirst({
          where: {
            subscription_tier_id: subscription.id,
            canceled_at: null,
          },
          orderBy: {
            created_at: "desc",
          },
        });
    }

    // If we still don't have a subscription, check if there's any subscription with this tier
    // that we can reuse (even if it's canceled)
    let anyTierSubscription = existingTierSubscription;
    if (!anyTierSubscription) {
      anyTierSubscription =
        await prismaTenanGuarded.organizationSubscription.findFirst({
          where: {
            subscription_tier_id: subscription.id,
          },
          orderBy: {
            created_at: "desc",
          },
        });

      // If the subscription is canceled, we'll create a new one instead of reusing it
      if (anyTierSubscription && anyTierSubscription.canceled_at) {
        console.log(
          `[DEBUG] Found canceled subscription (${anyTierSubscription.id}), will create a new one instead`,
        );
        anyTierSubscription = null;
      }
    }

    // Check for any active subscription for this tier (needed in the else branch below)
    const anyActiveSubscription =
      existingActiveTierSubscription ||
      (await prismaTenanGuarded.organizationSubscription.findFirst({
        where: {
          subscription_tier_id: subscription.id,
          canceled_at: null,
        },
      }));

    try {
      let updatedOrNewSubscription;

      if (existingTierSubscription) {
        // If there's an active subscription and it's not the one we're updating, deactivate it first
        if (
          existingActiveTierSubscription &&
          existingActiveTierSubscription.id !== existingTierSubscription.id &&
          existingActiveTierSubscription.stripe_sub_id !== subscriptionId
        ) {
          logger.info(
            {
              requestId,
              subscriptionId,
              customerId,
              orgId,
              existingActiveSubId: existingActiveTierSubscription.id,
            },
            "DEACTIVATING_PREVIOUS_ACTIVE_SUBSCRIPTION_BEFORE_UPDATING",
          );

          await prismaTenanGuarded.organizationSubscription.update({
            where: { id: existingActiveTierSubscription.id },
            data: {
              end_date: new Date(),
            },
          });
        }

        // Update the existing subscription instead of creating a new one
        logger.info(
          {
            requestId,
            subscriptionId,
            customerId,
            orgId,
            existingSubId: existingTierSubscription.id,
          },
          "FOUND_EXISTING_SUBSCRIPTION_WITH_SAME_TIER_UPDATING",
        );

        updatedOrNewSubscription =
          await prismaTenanGuarded.organizationSubscription.update({
            where: { id: existingTierSubscription.id },
            data: {
              stripe_sub_id: subscriptionId,
              current_period_start: new Date(
                stripeSubscription.current_period_start * 1000,
              ),
              current_period_end: new Date(
                stripeSubscription.current_period_end * 1000,
              ),
              cancel_at_period_end: Boolean(
                stripeSubscription.cancel_at_period_end,
              ),
              canceled_at: stripeSubscription.canceled_at
                ? new Date(stripeSubscription.canceled_at * 1000)
                : null,
            },
          });
      } else {
        // Check if there's an active subscription for this tier that needs to be deactivated
        if (anyActiveSubscription) {
          logger.info(
            {
              requestId,
              subscriptionId,
              customerId,
              orgId,
              existingActiveSubId: anyActiveSubscription.id,
            },
            "DEACTIVATING_EXISTING_ACTIVE_SUBSCRIPTION_BEFORE_CREATING_NEW",
          );

          await prismaTenanGuarded.organizationSubscription.update({
            where: { id: anyActiveSubscription.id },
            data: {
              end_date: new Date(),
            },
          });
        }

        // Check if there's an existing subscription record without a stripe_sub_id
        // This could happen if a record was created during checkout but not updated by the webhook
        console.log(
          `[DEBUG] Checking for pending subscription - OrgID: ${orgId}, TierID: ${subscription.id}`,
        );
        const pendingSubscription =
          await prismaTenanGuarded.organizationSubscription.findFirst({
            where: {
              subscription_tier_id: subscription.id,
              stripe_sub_id: null,
            },
            orderBy: {
              created_at: "desc",
            },
          });
        console.log(
          `[DEBUG] Pending subscription found: ${!!pendingSubscription}`,
        );
        if (pendingSubscription) {
          console.log(
            `[DEBUG] Pending subscription details: ${JSON.stringify(pendingSubscription)}`,
          );
        }

        if (pendingSubscription) {
          // Update the pending subscription with the Stripe subscription ID
          logger.info(
            {
              requestId,
              subscriptionId,
              customerId,
              orgId,
              pendingSubId: pendingSubscription.id,
            },
            "FOUND_PENDING_SUBSCRIPTION_UPDATING_WITH_STRIPE_ID",
          );

          updatedOrNewSubscription =
            await prismaTenanGuarded.organizationSubscription.update({
              where: { id: pendingSubscription.id },
              data: {
                stripe_sub_id: subscriptionId,
                start_date: new Date(stripeSubscription.start_date * 1000),
                current_period_start: new Date(
                  stripeSubscription.current_period_start * 1000,
                ),
                current_period_end: new Date(
                  stripeSubscription.current_period_end * 1000,
                ),
                cancel_at_period_end: Boolean(
                  stripeSubscription.cancel_at_period_end,
                ),
                canceled_at: stripeSubscription.canceled_at
                  ? new Date(stripeSubscription.canceled_at * 1000)
                  : null,
              },
            });
        } else {
          // Create a new subscription if no existing one with the same tier
          logger.info(
            {
              requestId,
              subscriptionId,
              customerId,
              orgId,
            },
            "CREATING_NEW_SUBSCRIPTION_RECORD",
          );

          try {
            // First check if there's any subscription for this tier, regardless of stripe_sub_id
            // This is a fallback in case the unique constraint was removed
            console.log(
              `[DEBUG] Double-checking for ANY subscription with this tier - OrgID: ${orgId}, TierID: ${subscription.id}`,
            );
            // We already did this check earlier, but we'll do it again here as a fallback
            // This time we'll specifically look for non-canceled subscriptions
            const anyTierSubscription =
              await prismaTenanGuarded.organizationSubscription.findFirst({
                where: {
                  subscription_tier_id: subscription.id,
                  canceled_at: null,
                },
                orderBy: {
                  created_at: "desc",
                },
              });

            // If we don't find a non-canceled subscription, we'll create a new one

            if (anyTierSubscription) {
              // If we found any subscription with this tier, update it instead of creating a new one
              console.log(
                `[DEBUG] Found existing tier subscription to update: ${anyTierSubscription.id}`,
              );
              updatedOrNewSubscription =
                await prismaTenanGuarded.organizationSubscription.update({
                  where: { id: anyTierSubscription.id },
                  data: {
                    stripe_sub_id: subscriptionId,
                    start_date: new Date(stripeSubscription.start_date * 1000),
                    current_period_start: new Date(
                      stripeSubscription.current_period_start * 1000,
                    ),
                    current_period_end: new Date(
                      stripeSubscription.current_period_end * 1000,
                    ),
                    cancel_at_period_end: Boolean(
                      stripeSubscription.cancel_at_period_end,
                    ),
                    canceled_at: stripeSubscription.canceled_at
                      ? new Date(stripeSubscription.canceled_at * 1000)
                      : null,
                  },
                });
            } else {
              // If no subscription exists for this tier at all, create a new one
              console.log(`[DEBUG] Creating brand new subscription record`);
              // Always create a new subscription if we don't find a non-canceled one
              // This is the key change to allow creating new subscriptions when existing ones are canceled
              updatedOrNewSubscription =
                await prismaTenanGuarded.organizationSubscription.create({
                  data: {
                    organization_id: orgId, // Still need to specify organization_id even with tenantGuardedPrisma
                    subscription_tier_id: subscription.id, // Set the subscription_tier_id
                    stripe_sub_id: subscriptionId,
                    start_date: new Date(stripeSubscription.start_date * 1000),
                    current_period_start: new Date(
                      stripeSubscription.current_period_start * 1000,
                    ),
                    current_period_end: new Date(
                      stripeSubscription.current_period_end * 1000,
                    ),
                    cancel_at_period_end: Boolean(
                      stripeSubscription.cancel_at_period_end,
                    ),
                    canceled_at: stripeSubscription.canceled_at
                      ? new Date(stripeSubscription.canceled_at * 1000)
                      : null,
                  },
                });
            }
          } catch (createError) {
            console.error(
              `[DEBUG] Error in subscription creation/update: ${createError instanceof Error ? createError.message : "Unknown error"}`,
            );
            logger.error(
              {
                requestId,
                subscriptionId,
                customerId,
                orgId,
                error: createError,
              },
              "ERROR_CREATING_OR_UPDATING_SUBSCRIPTION",
            );
            throw createError;
          }
        }
      }

      logger.info(
        {
          requestId,
          stripeSubscriptionId: subscriptionId,
          customerId,
          orgId,
          orgSubscriptionId: updatedOrNewSubscription.id,
        },
        existingTierSubscription
          ? "EXISTING_ORGANIZATION_SUBSCRIPTION_UPDATED"
          : "NEW_ORGANIZATION_SUBSCRIPTION_CREATED",
      );

      // Sync modules after subscription event
      try {
        await syncOrganizationModules(updatedOrNewSubscription.organization_id);
      } catch (syncError) {
        logger.error(
          { orgId: updatedOrNewSubscription.organization_id, syncError },
          "Failed to sync org modules after Stripe subscription event",
        );
      }
      return updatedOrNewSubscription;
    } catch (error) {
      logger.error(
        { requestId, subscriptionId, customerId, orgId, error },
        "FAILED_TO_CREATE_ORGANIZATION_SUBSCRIPTION",
      );
      throw error;
    }
  } catch (error) {
    logger.error(
      { requestId, subscriptionId, customerId, error },
      "UNEXPECTED_ERROR_MANAGING_SUBSCRIPTION_STATUS_CHANGE",
    );
    throw error;
  }
}

/**
 * Creates a Payment Intent for one-time payments
 */
async function createPaymentIntent({
  amount,
  currency = "usd",
  customerId,
  metadata = {},
}: {
  amount: number;
  currency?: string;
  customerId: string;
  metadata?: Record<string, string>;
}) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer: customerId,
      metadata,
      payment_method_types: ["card"],
      // Enable automatic payment methods for better conversion
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return paymentIntent;
  } catch (error) {
    logger.error(
      { error, amount, customerId },
      "Failed to create payment intent",
    );
    throw error;
  }
}

export {
  saveProduct,
  deleteProduct,
  savePrice,
  deletePrice,
  createCustomer,
  retrieveCustomerId,
  manageSubscriptionStatusChange,
  createPaymentIntent,
};
