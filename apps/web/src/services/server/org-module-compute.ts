import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { isSubscriptionActive } from "./utils/get-active-org-subs";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Compute the feature-module list for an organization based on its
 * active subscriptions and any per-module overrides.
 *
 * This function does NOT write back to the DB.
 *
 * @param orgId - the organization ID
 * @returns array of module names the org should have
 */
export async function getModulesFromSubscriptions(
  orgId: string,
): Promise<string[]> {
  // Fetch active organization subscriptions with nested product_modules
  const organizationSubscriptions =
    await prisma.organizationSubscription.findMany({
      where: { organization_id: orgId },
      include: {
        subscription_tier: {
          include: {
            subscription: {
              include: {
                product: {
                  include: {
                    product_modules: { include: { module: true } },
                  },
                },
              },
            },
          },
        },
      },
    });

  // No subscriptions → return empty
  if (organizationSubscriptions.length === 0) {
    return [];
  }

  // No active subscriptions → return empty
  // const activeSubscriptions = organizationSubscriptions.filter((sub) => isSubscriptionActive(sub));
  let count = 0;
  organizationSubscriptions.forEach((s) => {
    // Pass the relevant properties, converting null cancel_at_period_end to undefined
    if (
      isSubscriptionActive({
        canceled_at: s.canceled_at,
        cancel_at_period_end: s.cancel_at_period_end ?? undefined,
        current_period_end: s.current_period_end,
      })
    ) {
      console.log("activeSubscriptions", s);
      count++;
    }
  });
  console.log("activeSubscriptions", count);
  if (count === 0) {
    return [];
  }

  // Check for enterprise subscription
  const hasEnterprise = organizationSubscriptions.some(
    (sub) =>
      sub.is_enterprise === true ||
      sub.subscription_tier?.subscription?.product?.name === "Enterprise Suite",
  );

  let moduleNames: string[];

  if (hasEnterprise) {
    // Grant all modules
    const allModules = await prisma.module.findMany({ select: { name: true } });
    moduleNames = allModules.map((m) => m.name);
  } else {
    // Collect unique modules from subscriptions
    const unique = new Set<string>();
    for (const sub of organizationSubscriptions) {
      const mods =
        sub.subscription_tier?.subscription?.product?.product_modules || [];
      for (const pm of mods) {
        pm.module?.name && unique.add(pm.module.name);
      }
    }
    moduleNames = Array.from(unique);
  }

  // Apply module overrides (enable/disable)
  const overrides = await prisma.organizationModuleOverride.findMany({
    where: { organization_id: orgId },
  });
  for (const ov of overrides) {
    const rec = await prisma.module.findUnique({
      where: { id: ov.module_id },
      select: { name: true },
    });
    if (!rec) continue;
    if (ov.is_enabled && !moduleNames.includes(rec.name))
      moduleNames.push(rec.name);
  }

  return moduleNames;
}
