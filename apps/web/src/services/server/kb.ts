"use server";
import { guardedPrisma, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/prisma";
import { Prisma, PrismaClient, ShortAnswer } from "@askinfosec/database";
import { tenantGuardedPrisma, userGuardedPrisma } from "@/lib/prisma";
import { IDocument } from "@/types/org/docs";
import _ from "lodash";
import { prisma } from "@/lib/prisma";
import { getAllScopes } from "./scope";

/**
 * fetch data from knowledge base table
 */
export async function getKb(orgId: string, userId: string) {
  const db = guardedPrisma({ orgId, userId });
  const kb = await db.knowledgeBase.findMany({
    where: {
      organization_id: orgId,
    },
    select: {
      id: true,
      question: true,
      assigned_id: true,
      answer: true,
      category_id: true,
      status: true,
      owned_by: true,
      last_verification_date: true,
      created_at: true,
      updated_at: true,
      last_update_by_id: true,
      tags: true,
      scope_id: true,
      scope: true,
      next_verification: true,
      ref_num: true,
    },
    orderBy: {
      ref_num: "asc",
    },
  });

  const allKB = await Promise.all(
    kb.map(async (kb: any) => {
      const t = await Promise.all(
        kb.tags.map(async (tag: any) => {
          const t = await tenantGuardedPrisma(orgId).tag.findFirst({
            where: {
              id: tag,
              organization_id: orgId,
            },
          });

          if (!t) throw Error("Tag does not exists");
          return { value: tag, label: t.name };
        }),
      );

      return {
        id: kb.id,
        question: kb.question,
        answer: kb.answer,
        category_id: kb.category_id,
        assigned_id: kb.assigned_id,
        status: kb.status,
        owned_by: kb.owned_by,
        last_verification_date: kb.last_verification_date,
        created_at: kb.created_at,
        updated_at: kb.updated_at,
        last_update_by_id: kb.last_update_by_id,
        tags: kb.tags.length >= 1 ? t : [],
        scope_id: kb.scope_id,
        scope: { label: kb.scope?.name, value: kb.scope_id },
        next_verification: kb.next_verification,
        ref_num: kb.ref_num,
      };
    }),
  );

  return allKB;
}

interface DeleteKbParams {
  org: string;
  userId: string;
  idsToDelete: any;
}

export async function deleteKbs({ org, userId, idsToDelete }: DeleteKbParams) {
  try {
    const knowledgeBaseDeleted = await guardedPrisma({
      orgId: org,
      userId,
      checkMembership: false,
    }).knowledgeBase.deleteMany({
      where: {
        id: {
          in: idsToDelete,
        },
        organization_id: org,
      },
    });
    return knowledgeBaseDeleted;
  } catch (err) {
    return err;
  }
}

interface createKbParams {
  org: string;
  question: string;
  assigned_id: string;
  answer: string;
  db: PrismaClient;
  userId: string;
  category_id?: string;
}

export async function uploadKb({
  org,
  question,
  assigned_id,
  answer,
  db,
  userId,
  category_id,
}: createKbParams) {
  const commonScope = (await getAllScopes({ org })).find(
    (scope) => scope.name === "Common",
  )?.id;
  const existingQuestion = await db.knowledgeBase.findFirst({
    where: {
      organization_id: org,
      question: question,
    },
  });

  if (existingQuestion) throw Error("Question already exists");

  // create kb in db table
  const createQA = await tenantGuardedPrisma(org)
    .$extends(
      withAuditLogger({
        orgId: org,
        userId,
        message: "KB create from file upload",
      }),
    )
    .knowledgeBase.create({
      data: {
        category_id: category_id,
        question: question,
        answer: answer!,
        status: "in_review",
        organization_id: org,
        created_by_id: userId,
        last_update_by_id: userId,
        assigned_id,
        owned_by_id: assigned_id,
        last_verification_date: null,
        scope_id: commonScope,
      },
    });
  return createQA;
}

/**
 * Gets Knowledge base by Id
 * @param orgId - organization id
 * @param userId  - user id
 * @param id - kb id
 * @returns knowledge base
 */
export async function getKbById(orgId: string, userId: string, id: string) {
  const kb = await guardedPrisma({ orgId, userId }).knowledgeBase.findUnique({
    where: {
      organization_id: orgId,
      id: id,
    },
    select: {
      id: true,
      question: true,
      assigned_id: true,
      answer: true,
      category_id: true,
      status: true,
      owned_by: true,
      last_verification_date: true,
      created_at: true,
      updated_at: true,
      last_update_by_id: true,
      next_verification: true,
      answer_yes_no_na: true,
      scope_id: true,
      ref_num: true,
    },
  });

  const kbVector: any = await prisma.$transaction(async (tx) => {
    const sql1 = Prisma.sql`SELECT vector::text FROM knowledge_base WHERE id = ${id}`;
    await tx.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`;
    return await tx.$queryRaw`${sql1}`;
  });

  const ref_docs = await guardedPrisma({ orgId, userId }).evidence.findMany({
    where: {
      knowledgeBaseId: id,
    },
    include: {
      file: true,
    },
  });

  if (kb === null)
    throw Error(`Knowlegde base with Id: '${id}' does not exist`);

  const kb_resp = {
    id: kb.id,
    question: kb.question,
    answer: kb.answer,
    answer_yes_no_na: kb.answer_yes_no_na as ShortAnswer,
    category_id: kb.category_id,
    assigned_id: kb.assigned_id,
    status: kb.status,
    owned_by: kb.owned_by,
    last_verification_date: kb.last_verification_date,
    created_at: kb.created_at,
    updated_at: kb.updated_at,
    last_update_by_id: kb.last_update_by_id,
    next_verification: kb.next_verification,
    reference_docs: ref_docs
      ?.filter((f) => f.file.document_type !== "evidence")
      .map((f) => {
        return {
          fileId: f.fileId,
          name: f.file?.name,
          path: f.file?.path,
          buffer_file: f.file?.buffer_file,
        } as unknown as IDocument;
      }),
    hasVector: kbVector.length > 0 ? kbVector[0].vector !== null : false,
    scope_id: kb.scope_id,
    ref_num: kb.ref_num,
  };

  return kb_resp;
}

interface AssignToParams {
  name: string | null;
  email: string | null;
  image: string | null;
}

export async function getAssignTo(userId: string) {
  const row: AssignToParams = await userGuardedPrisma(
    userId,
  ).user.findFirstOrThrow({
    where: { id: userId },
    select: { name: true, email: true, image: true },
  });

  return row;
}
/**
 * Gets all Linked Documents of knowledge base
 * @param id - knowledgebase id
 * @param db - prisma client
 * @returns filtered data
 */
export async function getKBLinkDocuments(id: string, db: PrismaClient) {
  const data = await db.evidence.findMany({
    where: {
      knowledgeBaseId: id,
    },
    include: {
      file: {
        select: {
          id: true,
          name: true,
          buffer_file: true,
          path: true,
        },
      },
    },
  });
  const filter_data = data.map((e) => {
    return {
      id: e.id,
      knowledgeBaseId: e.knowledgeBaseId,
      name: e.file?.name,
      fileId: e.fileId,
      buffer_file: e.file?.buffer_file,
      path: e.file?.path,
    };
  });

  return filter_data;
}

/**
 * Updates the KB Linked documents.
 * @param ids - Array of fileId
 * @param kbId - Knowledge base id
 * @param db - Prisma Client
 */
export async function updateKBLinkDocuments({
  ids,
  kbId,
  db,
}: {
  ids: string[];
  kbId: string;
  db: PrismaClient;
}) {
  const existingRefDocs = await db.evidence.findMany({
    where: {
      knowledgeBaseId: kbId,
    },
    select: {
      id: true,
      fileId: true,
      file: {
        select: {
          document_type: true,
        },
      },
    },
  });

  existingRefDocs.map(async (e) => {
    // delete the evidence if its not included in list of ids
    if (!ids.includes(e.fileId)) {
      // If the file type is "evidence", also delete the file. We should not delete other than evidence type as that means the file can be policy, proceude, or control which maybe used by other objects
      if (e.file.document_type && e.file.document_type === "evidence") {
        // this delete will cascade to evidence table
        await db.file.delete({
          where: {
            id: e.fileId,
          },
        });
      } else {
        // delete the row in evidence table
        await db.evidence.delete({
          where: {
            id: e.id,
          },
        });
      }
    }
  });

  const linkDocs = await Promise.all(
    // loop through list of ids and insert the file in evidence if not yet exist
    ids.map(async (id: string) => {
      let queryEvidence = await db.evidence.findFirst({
        where: {
          fileId: id,
          knowledgeBaseId: kbId,
        },
      });
      if (!queryEvidence) {
        queryEvidence = await db.evidence.create({
          data: {
            knowledgeBaseId: kbId,
            fileId: id,
          },
        });
      }
      return queryEvidence;
    }),
  );

  return linkDocs;
}

interface CreateKBParams {
  org: string;
  question: string;
  category_id: string;
  answer: string;
  assigned_id: string;
  scope_id: string;
  questionId?: string;
  userId: string;
}

export async function createKnowledgeBase({
  org,
  question,
  category_id,
  answer,
  assigned_id,
  scope_id,
  questionId,
  userId,
}: CreateKBParams) {
  const db = tenantGuardedPrisma(org);
  const commonScope = (await getAllScopes({ org })).find(
    (scope) => scope.name === "Common",
  )?.id;
  if (!commonScope) throw new Error("Common scope not found");

  const existingQuestion = await db.knowledgeBase.findFirst({
    where: {
      organization_id: org,
      question: question,
    },
  });

  if (existingQuestion) throw Error("Question already exists");

  const createQA = await db
    .$extends(withAuditLogger({ orgId: org, userId, message: "KB create" }))
    .knowledgeBase.create({
      data: {
        category_id: category_id || "default",
        question,
        answer,
        status: "in_review",
        organization_id: org,
        created_by_id: assigned_id,
        last_update_by_id: assigned_id,
        assigned_id,
        owned_by_id: assigned_id,
        last_verification_date: null,
        scope_id: commonScope,
        ref_num: questionId ? parseInt(questionId) : 0,
      },
    });

  return createQA;
}
