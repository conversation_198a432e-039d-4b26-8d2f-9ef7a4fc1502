"use server";

import { Invite, User, type PrismaClient } from "@askinfosec/database";
import { authAdapter } from "@/lib/auth-helpers";
import { createHash, emailRegex, getRandomColor } from "@/lib/utils";
import { transport } from "@/lib/email";
import { jwtService } from "@/lib/jwt";
import { getOrg } from "../organization";
import { bypassedPrisma, guardedPrisma, prisma } from "@/lib/prisma";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const extendedPrisma = bypassedPrisma as unknown as PrismaClient;
import { InviteMemberEmail } from "../../../../emails/invite-member";
import { render } from "@react-email/render";
import InviteVendorEmail from "../../../../emails/invite-vendor";
import AcceptAssesseeAnswer from "../../../../emails/accept-answer";
import { addNotificationForUser } from "../notification";
import { NextResponse } from "next/server";
import { createId } from "@paralleldrive/cuid2";
import { NotificationStatus, NotificationType } from "@/models/notification";
import { add } from "date-fns";

const stripTrailingSlash = (str: string) => {
  return str.endsWith("/") ? str.slice(0, -1) : str;
};

interface CreateInviteParams {
  origin: string;
  db: PrismaClient;
  org: string;
  userId: string;
  role: string;
}

export async function orgPlan(db: PrismaClient, orgId: string) {
  const organization = await db.organizationSubscription.findFirst({
    where: { organization_id: orgId },
  });

  if (!organization) {
    throw new Error("Organization not found");
  }

  // if (organization.subscription_id === "free") {
  //   const org = await db.organization.findFirst({
  //     where: { id: orgId },
  //     select: { licenses: true },
  //   });

  //   if (!org) {
  //     throw new Error("Organization not found");
  //   }

  //   if (org.licenses.length > 0) {
  //     return { allowed: true };
  //   }
  //   return {
  //     allowed: false,
  //     message: "Free plan only allows one member. Upgrade to add more.",
  //   };
  // }

  return { allowed: true };
}

export async function createInviteByLink(params: CreateInviteParams) {
  const { origin, db, org, userId, role } = params;
  console.debug(`[INVITE] Creating link invite started`, { org, userId, role });

  try {
    if (!role || typeof role !== "string") {
      console.error("[INVITE] Invalid role specified", { role });
      throw new Error("Invalid role specified");
    }

    // console.debug("[INVITE] Checking organization plan", { org });
    // const planCheck = await orgPlan(db, org);
    // if (!planCheck.allowed) {
    //   console.warn("[INVITE] Organization plan limit exceeded", { org, planCheck });
    //   throw new Error(planCheck.message || "Organization plan limit exceeded");
    // }

    const baseurl = stripTrailingSlash(
      process.env.NEXTAUTH_URL ?? "https://localhost:3000",
    );
    console.debug("[INVITE] Checking for existing active invites", {
      org,
      userId,
      role,
    });

    const activeInvite = await db.invite.findFirst({
      where: {
        created_by: userId,
        organization_id: org,
        role_id: role,
        email: null,
        expired_at: { gt: new Date() },
      },
    });

    if (activeInvite) {
      console.debug("[INVITE] Returning existing active invite", {
        inviteId: activeInvite.id,
      });
      return `${baseurl}/accept-invite-vendor?token=${activeInvite.token}&org=${org}&user=${userId}`;
    }

    console.debug("[INVITE] Creating new invite token", { org, userId });
    const maxAge = 24 * 60 * 60;
    const expires = add(new Date(), { seconds: maxAge });
    const inviteId = createId();

    const jwt = await jwtService.encode({
      token: { id: inviteId },
      maxAge,
    });

    if (!jwt) {
      console.error("[INVITE] Failed to generate JWT token");
      throw new Error("Failed to generate invitation token");
    }

    console.debug("[INVITE] Creating new invite record", {
      inviteId,
      org,
      userId,
    });
    await db.invite.create({
      data: {
        id: inviteId,
        token: jwt,
        organization_id: org,
        created_by: userId,
        expired_at: expires,
        role_id: role,
        image: getRandomColor(),
      },
    });

    console.debug("[INVITE] Invite created successfully", { inviteId });
    return `${baseurl}/accept-invite-vendor?token=${jwt}&org=${org}&user=${userId}`;
  } catch (error) {
    console.error("[INVITE] Error in createInviteByLink", {
      org,
      userId,
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

export async function createInviteByEmail(
  params: CreateInviteParams & { email: string },
) {
  const { origin, db, org, userId, role, email } = params;
  console.debug(`[INVITE] Creating email invite started`, {
    org,
    userId,
    role,
    email,
  });

  try {
    if (!email || !emailRegex.test(email)) {
      console.error("[INVITE] Invalid email address", { email });
      throw new Error("Invalid email address");
    }

    // console.debug("[INVITE] Checking organization plan", { org });
    // const planCheck = await orgPlan(db, org);
    // if (!planCheck.allowed) {
    //   console.warn("[INVITE] Organization plan limit exceeded", { org, planCheck });
    //   throw new Error(planCheck.message || "Organization plan limit exceeded");
    // }

    const baseurl = stripTrailingSlash(
      process.env.NEXTAUTH_URL ?? "https://localhost:3000",
    );

    console.debug("[INVITE] Checking existing membership", { email, org });
    const existingMember = await db.member.findFirst({
      where: {
        organization_id: org,
        user: { email },
      },
    });

    if (existingMember) {
      console.warn("[INVITE] User already a member", { email, org });
      throw new Error("User is already a member of the organization");
    }

    console.debug("[INVITE] Fetching inviter and organization details");
    const [inviter, organization] = await Promise.all([
      db.user.findFirst({ where: { id: userId } }),
      getOrg({ userId, orgId: org }),
    ]);

    if (!inviter?.email) {
      console.error("[INVITE] Inviter not found", { userId });
      throw new Error("Inviter not found");
    }

    if (!organization) {
      console.error("[INVITE] Organization not found", { org });
      throw new Error("Organization not found");
    }

    console.debug("[INVITE] Checking existing invites", { email, org });
    const existingInvite = await db.invite.findFirst({
      where: {
        email,
        organization_id: org,
        expired_at: { gt: new Date() },
      },
    });

    if (existingInvite) {
      console.debug("[INVITE] Resending existing invite", {
        inviteId: existingInvite.id,
      });
      return resendExistingInvite(existingInvite, inviter, organization, email);
    }

    console.debug("[INVITE] Creating new invite record", { email, org });
    const maxAge = 24 * 60 * 60;
    const expires = add(new Date(), { seconds: maxAge });
    const inviteId = createId();
    const authToken = createId();

    const jwt = await jwtService.encode({ token: { id: inviteId }, maxAge });
    if (!jwt) {
      console.error("[INVITE] Failed to generate JWT token");
      throw new Error("Failed to generate invitation token");
    }

    await db.$transaction(async (tx) => {
      console.debug("[INVITE] Creating invite transaction", { inviteId });
      await tx.invite.create({
        data: {
          id: inviteId,
          token: jwt,
          organization_id: org,
          created_by: userId,
          expired_at: expires,
          role_id: role,
          email,
          image: getRandomColor(),
        },
      });
    });

    console.debug("[INVITE] Sending invitation email", { email });
    await sendEmailInvite({
      email,
      link: `${baseurl}/api/invites/accepted?token=${jwt}`,
      org: organization.name,
      inviterName: inviter.name || inviter.email.split("@")[0],
      inviterEmail: inviter.email,
    });

    console.debug("[INVITE] Email invite completed successfully", {
      email,
      org,
    });
    return { success: true };
  } catch (error) {
    console.error("[INVITE] Error in createInviteByEmail", {
      org,
      email,
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

// Updated helper functions with logging
async function resendExistingInvite(
  existingInvite: Invite,
  inviter: User,
  organization: { id: string; name: string },
  email: string,
) {
  console.debug("[INVITE] Resending existing invite", {
    inviteId: existingInvite.id,
    email,
  });

  try {
    if (!inviter.email) {
      console.error("[INVITE] Missing inviter email", {
        inviterId: inviter.id,
      });
      throw new Error("Inviter email is required");
    }

    const baseurl = stripTrailingSlash(
      process.env.NEXTAUTH_URL ?? "https://localhost:3000",
    );
    const authToken = createId();

    console.debug("[INVITE] Creating verification token", { email });
    if (authAdapter?.createVerificationToken) {
      await authAdapter.createVerificationToken({
        identifier: email,
        token: await createHash(`${authToken}${process.env.NEXTAUTH_SECRET}`),
        expires: existingInvite.expired_at,
      });
    }

    console.debug("[INVITE] Sending resend email", { email });
    await sendEmailInvite({
      email,
      link: `${baseurl}/accept-invite?token=${existingInvite.token}`,
      org: organization.name,
      inviterName: inviter.name || inviter.email.split("@")[0],
      inviterEmail: inviter.email,
    });

    console.debug("[INVITE] Resent invite successfully", { email });
    return { success: true, message: "Invitation resent" };
  } catch (error) {
    console.error("[INVITE] Error resending invite", {
      email,
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

async function sendEmailInvite(params: {
  email: string;
  link: string;
  org: string;
  inviterName: string;
  inviterEmail: string;
}) {
  console.debug("[EMAIL] Preparing invitation email", { email: params.email });

  try {
    if (!process.env.EMAIL_FROM) {
      console.error("[EMAIL] Missing EMAIL_FROM environment variable");
      throw new Error("Missing EMAIL_FROM environment variable");
    }

    const emailHtml = await render(
      InviteMemberEmail({
        to: params.email,
        link: params.link,
        org: params.org,
        username: params.email.split("@")[0],
        invitedByUsername: params.inviterName,
        invitedByEmail: params.inviterEmail,
      }),
    );

    console.debug("[EMAIL] Sending invitation email", { email: params.email });

    if (!emailHtml) {
      throw new Error("Failed to generate email template");
    }

    const result = await transport.sendMail({
      to: params.email,
      from: process.env.EMAIL_FROM,
      subject: `You're invited to join ${params.org} on AskInfosec`,
      text: inviteLinkText({
        username: params.email.split("@")[0],
        link: params.link,
        org: params.org,
        invitedByUsername: params.inviterName,
        invitedByEmail: params.inviterEmail,
      }),
      html: emailHtml,
    });

    if (!result) {
      throw new Error("Email transport returned undefined result");
    }

    const failed = result.rejected?.concat(result.pending).filter(Boolean);
    if (failed?.length) {
      console.error("[EMAIL] Email delivery failed", { failed });
      throw new Error(`Email delivery failed for: ${failed.join(", ")}`);
    }

    console.debug("[EMAIL] Invitation email sent successfully", {
      email: params.email,
    });
    return true;
  } catch (error) {
    console.error("[EMAIL] Error sending invitation email", {
      email: params.email,
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

export async function sendEmailInviteLink({
  to,
  link,
  org,
  invitee,
  invitedByUsername,
  invitedByEmail,
}: {
  to: string;
  link: string;
  org: string;
  invitee: string;
  invitedByUsername: string;
  invitedByEmail: string;
}) {
  const emailHtml = await render(
    InviteMemberEmail({
      to,
      link,
      org,
      username: invitee,
      invitedByUsername: invitedByUsername,
      invitedByEmail,
    }),
  );
  const result = await transport.sendMail({
    to,
    from: process.env.EMAIL_FROM,
    subject: `You are invited to join ${org} to AskInfosec`,
    text: inviteLinkText({
      link,
      org,
      username: invitee,
      invitedByUsername,
      invitedByEmail,
    }),
    html: emailHtml,
  });
  if (!result) {
    throw new Error("Email transport returned undefined result");
  }
  const failed = result.rejected?.concat(result.pending).filter(Boolean);
  if (failed.length) {
    console.error("[EMAIL] Email delivery failed", { failed });
    throw new Error(`Email delivery failed for: ${failed.join(", ")}`);
  }
  console.debug("[EMAIL] Invitation email sent successfully", { email: to });
  return true;
}

function inviteLinkText({
  link,
  org,
  username,
  invitedByUsername,
  invitedByEmail,
}: {
  username: string;
  link: string;
  org: string;
  invitedByUsername: string;
  invitedByEmail: string;
}) {
  return `
  JOIN ${org} ON ASKINFOSEC

  Hello ${username},

  ${invitedByUsername} (${invitedByEmail} ${invitedByEmail}) has invited you to the
  ${org} organization on AskInfosec.

  Join the team ${link}

  or copy and paste this URL into your browser: ${link} ${link}

  --------------------------------------------------------------------------------

  This invitation was intended for ${username}. If you were not expecting this
  invitation, you can ignore this email. If you are concerned about your account's
  safety, don't hesitate to contact us.
  `;
}

export async function sendInviteLinkNotification({
  url,
  email,
}: {
  url: string;
  email: string;
}) {
  const cb = new URL(url).searchParams?.get("callbackUrl") as string;
  const org = new URL(cb).searchParams?.get("org") || "";
  const user = new URL(cb).searchParams?.get("user") || "";

  const db = guardedPrisma({
    orgId: org,
    userId: user,
    roles: ["owner", "admin"],
  });

  const inviter = await db.user.findFirst({ where: { id: user } });
  const organization = await getOrg({ userId: user, orgId: org });

  if (!inviter || !organization) {
    throw new Error("User or organization does not exists.");
  }

  const inviterName = inviter.name
    ? inviter.name
    : inviter.email!.replace(/@.*$/, "");
  const invitee = await db.user.findFirst({ where: { email } });
  const inviteeName =
    invitee && invitee.name
      ? invitee.name.split(" ")[0]
      : email.replace(/@.*$/, "");

  return await sendEmailInviteLink({
    to: email,
    link: url,
    org: organization.name,
    invitee: inviteeName,
    invitedByUsername: inviterName,
    invitedByEmail: inviter.email!,
  });
}

export async function deleteInvite({ id, org }: { id: string; org: string }) {
  const invitee = await extendedPrisma.invite.findFirst({
    where: {
      id,
      organization_id: org,
    },
  });

  if (!invitee) throw Error("Invitation does not exists");
  return await extendedPrisma.invite.delete({
    where: {
      id,
      organization_id: org,
    },
  });
}

function inviteVendorLinkText({
  link,
  email,
}: {
  email: string;
  link: string;
}) {
  return `
  Hello ${email},

  We appreciate your cooperation in our security assessment process. As part of our due
  diligence, we kindly request your responses to the following assessment questions to
  evaluate your security posture:

  Accept Invite ${link}

  Best regards,
  The AskInfosec Team
  `;
}

function acceptVendorAnswerLinkText({
  link,
  name,
}: {
  name: string;
  link: string;
}) {
  return `
  Hello ${name},

  Thank you for providing us with the opportunity to participate in the security
  assessment. We are pleased to inform you that we have completed the questionnaire.

  Below is the link for your review:

  View Answers
  ${link}

  Should you have any further inquiries or require additional information, please feel
  free to reach out to us.


  Best regards,
  The AskInfosec Team
  `;
}

/**
 * Send Email notification to the assesee.
 */
export async function sendEmailVendorInviteLink({
  email,
  link,
}: {
  email: string;
  link: string;
}) {
  const emailHtml = await render(
    InviteVendorEmail({
      email,
      link,
    }),
  );
  const result = await transport.sendMail({
    to: email,
    from: process.env.EMAIL_FROM,
    subject:
      "You are invited to partake in assessing the questionnaire for AskInfosec.",
    text: inviteVendorLinkText({ email, link }),
    html: emailHtml,
  });
  const failed = result.rejected?.concat(result.pending).filter(Boolean);
  if (failed.length) {
    throw new Error(`Email (${failed.join(", ")}) could not be sent`);
  }
}

/**
 * Send Email notification to the assessor.
 */
export async function sendEmailVendorAnswerLink({
  email,
  name,
  link,
  orgId,
  userId,
}: {
  email: string;
  name: string;
  link: string;
  orgId: string;
  userId: string;
}) {
  const emailHtml = await render(
    AcceptAssesseeAnswer({
      name: name !== null ? name : email,
      link,
    }),
  );

  const result = await transport.sendMail({
    to: email,
    from: process.env.EMAIL_FROM,
    subject: "Security Assessment Questionnaire Completed for Review",
    text: inviteVendorLinkText({ email, link }),
    html: emailHtml,
  });
  const failed = result.rejected?.concat(result.pending).filter(Boolean);
  if (failed.length) {
    throw new Error(`Email (${failed.join(", ")}) could not be sent`);
  }
}

/**
 * Generates a link that will be used to send the assessee's answers
 * to the assessor's organization
 */
function generateVendorAnswerLink({
  org,
  assesseeQid,
  assessorQid,
}: {
  org: string;
  assesseeQid: string;
  assessorQid: string;
}) {
  const baseurl = stripTrailingSlash(
    process.env.NEXTAUTH_URL ?? "https://localhost:3000/",
  );
  const link = `${baseurl}/${org}/questionnaires/${assessorQid}?assesseeQid=${assesseeQid}&assessorQid=${assessorQid}`;

  return link;
}

export async function returnVendorAnswerLink({
  questionnaireId,
  assessorQid,
  email,
  db,
  orgId,
  userId,
}: {
  email: string;
  questionnaireId: string;
  assessorQid: string;
  db: PrismaClient;
  orgId: string;
  userId: string;
}) {
  const queryQuestionnaire = await db.questionnaire.findFirst({
    where: { id: questionnaireId },
    select: { created_by: true, organization_id: true, vendor: true },
  });

  if (!queryQuestionnaire) throw new Error("Questionnaire does not exist");

  const queryAssessorQuestionnaire =
    await extendedPrisma.questionnaire.findFirst({
      where: { id: assessorQid },
      select: {
        created_by: true,
        organization_id: true,
        vendor: true,
      },
    });

  if (!queryAssessorQuestionnaire)
    throw new Error("Assessor Questionnaire does not exist");

  const link = generateVendorAnswerLink({
    org: queryAssessorQuestionnaire.organization_id,
    assesseeQid: questionnaireId,
    assessorQid: assessorQid,
  });

  const messageTemplate = `[${queryAssessorQuestionnaire.vendor?.email} has completed your questionnaire. Click here to view](${link})`;

  try {
    const notification = {
      message: "You have been invited to complete a questionnaire",
      status: "unread" as keyof NotificationStatus,
      type: "user_invite" as keyof NotificationType,
      user_id: queryAssessorQuestionnaire.created_by,
      organization_id: queryAssessorQuestionnaire.organization_id,
    };

    await addNotificationForUser({
      org: queryAssessorQuestionnaire.organization_id,
      user: queryAssessorQuestionnaire.created_by,
      notification,
    });
  } catch (error) {
    console.error("Error sending system notification:", error);
  }

  try {
    await sendEmailVendorAnswerLink({
      email: email ?? "",
      name: email.replace(/@[\w.-]+/, "") ?? "",
      link,
      orgId,
      userId,
    });
  } catch (error) {
    console.error("Error sending email notification:", error);
  }

  const response = new NextResponse(
    JSON.stringify({
      message: "Answers sent successfully!",
    }),
    { status: 200 },
  );
  return response;
}

export async function getInviteVendor(questionnaireId: string) {
  if (questionnaireId) {
    const existingInvite = await extendedPrisma.invite.findFirst({
      where: {
        reference: questionnaireId,
      },
      select: {
        email: true,
        expired_at: true,
        reference: true,
      },
    });
    return existingInvite;
  }
  return null;
}

export async function getQuestionnaireRef(questionnaireId: string) {
  const queryAssessee = await extendedPrisma.questionnaire.findFirst({
    where: {
      reference: {
        startsWith: questionnaireId + ",",
      },
    },
    orderBy: {
      created_at: "desc",
    },
    take: 1,
  });

  return queryAssessee?.reference || null;
}
