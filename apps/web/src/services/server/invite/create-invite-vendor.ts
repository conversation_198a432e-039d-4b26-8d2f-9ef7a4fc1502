import { jwtService } from "@/lib/jwt";
import { PrismaClient } from "@askinfosec/database";
import { add, sub } from "date-fns";
import { nanoid } from "nanoid";
import { sendEmailVendorInviteLink } from "./invite";
import { authAdapter } from "@/lib/auth-helpers";
import { createHash } from "@/lib/utils";
import { getQuestionnaire } from "../questionnaires";
import { prisma } from "@/lib/prisma";

export const stripTrailingSlash = (str: string) => {
  return str.endsWith("/") ? str.slice(0, -1) : str;
};

interface createInviteToAnswerQuestionnaireParam {
  email: string;
  user: string;
  org: string;
  vendor: string;
  db: PrismaClient;
  questionnaireId: string;
}

/**
 * Returns a link that will be used to accept invite and replicate all the documents, questions
 * and questionnaire.
 */
export async function createInviteToAnswerQuestionnaire({
  user,
  org,
  vendor,
  email,
  db,
  questionnaireId,
}: createInviteToAnswerQuestionnaireParam) {
  // Check if a vendor copy already exists
  const vendorCopy = await db.questionnaire.findFirst({
    where: {
      reference: {
        startsWith: `${questionnaireId},`,
      },
      organization_id: org,
    },
  });

  // If no vendor copy exists, create one
  if (!vendorCopy) {
    try {
      const { copyQuestionnaireAndQuestions } = await import(
        "../questionnaires"
      );
      // Get vendor user
      const vendorUser = await db.user.findFirst({
        where: { email },
      });

      if (vendorUser) {
        // Create a vendor copy
        const vendorCopy = await copyQuestionnaireAndQuestions({
          id: questionnaireId,
          org,
          user: vendorUser.id,
          email,
        });
        console.log(
          `Created vendor copy for questionnaire ${questionnaireId} during invitation`,
        );

        // Explicitly set the other_data field to include the override_scope_type
        try {
          await db.questionnaire.update({
            where: { id: vendorCopy.questionnaireId },
            data: {
              other_data: { override_scope_type: "vendor" },
            },
          });
          console.log(
            `Updated vendor copy with scope override: ${vendorCopy.questionnaireId}`,
          );
        } catch (error) {
          console.warn(
            "Could not update vendor copy with scope override:",
            error,
          );
        }
      }
    } catch (error) {
      console.error("Error creating vendor copy during invitation:", error);
      // Continue with invitation even if copy creation fails
    }
  }

  const inviteId = nanoid(32);
  const secret = process.env.NEXTAUTH_SECRET;
  const baseurl = stripTrailingSlash(
    process.env.NEXTAUTH_URL ?? "https://localhost:3000/",
  );
  const jwt = await generateJWT(inviteId);
  const invite = await createOrDeleteVendorInvite({
    id: questionnaireId,
    db,
    inviteId,
    expires: jwt.expires,
    jwt: jwt.jwt,
    user,
    org,
    vendor,
    email,
  });

  // Simplify the callback URL structure
  const callbackUrl = `/accept-vendor-invite?token=${invite.token}&questionnaireId=${questionnaireId}`;
  const link = `${baseurl}/api/auth/callback/email?callbackUrl=${encodeURIComponent(callbackUrl)}&token=${inviteId}&email=${email}`;

  if (invite) {
    const verificationToken = await createHash(`${inviteId}${secret}`);
    if (authAdapter?.createVerificationToken) {
      await authAdapter.createVerificationToken({
        identifier: email,
        token: verificationToken,
        expires: invite.expired_at,
      });
    }
    await sendEmailVendorInviteLink({ email, link });
  }
  return link;
}

export async function generateJWT(inviteId: string) {
  const maxAge = 24 * 60 * 60; // 1 day (expressed in seconds)
  const expires = add(new Date(), { seconds: maxAge });
  const jwt = await jwtService.encode({ token: { id: inviteId }, maxAge });
  return { jwt, expires };
}

export async function createOrDeleteVendorInvite({
  id,
  db,
  user,
  email,
  org,
  inviteId,
  jwt,
  expires,
  vendor,
}: {
  id: string;
  db: PrismaClient;
  vendor: string;
  user: string;
  email: string;
  org: string;
  inviteId: string;
  expires: Date;
  jwt: string;
}) {
  const { questionnaire } = await getQuestionnaire(org, id);
  const u = await prisma.user.findFirst({
    where: {
      id: questionnaire.created_by,
    },
  });

  const queryVendor = await db.vendor.findFirst({
    where: {
      id: vendor,
    },
  });
  if (!queryVendor) throw Error("Vendor does not exist");

  // Get the system vendor role
  const vendorRole = await db.role.findFirst({
    where: {
      name: "vendor",
      is_system: true,
    },
  });

  if (!vendorRole) {
    throw new Error("System vendor role not found");
  }

  // Check if this role is already associated with the organization
  const orgRole = await db.role.findFirst({
    where: {
      name: "vendor",
      organization_id: org,
    },
  });

  // If not associated, create the organization-specific role reference
  if (!orgRole) {
    await db.role.update({
      where: {
        id: vendorRole.id,
      },
      data: {
        organization_id: org,
      },
    });
  }

  const activeInvite = await db.invite.findFirst({
    where: {
      created_by: user,
      organization_id: org,
      email: queryVendor.email,
      created_at: { gt: sub(new Date(), { seconds: 60 * 60 }) },
    },
  });

  if (activeInvite) {
    return { ...activeInvite };
  } else {
    const invite = await db.invite.create({
      data: {
        id: inviteId,
        token: jwt,
        organization_id: org,
        created_by: user,
        expired_at: expires,
        image: "vendor-invite",
        email: queryVendor.email,
        role_id: vendorRole.id,
        reference: questionnaire.id,
      },
    });

    return { ...invite };
  }
}
