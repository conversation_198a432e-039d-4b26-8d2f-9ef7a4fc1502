"use server";

import type { Member, MemberSelector, MemberUser } from "@/models/member";
import { guardedPrisma, otherDataAuditLog } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { HttpError } from "@/lib/errors";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();

/**
 * Get a list of organization members and invitations
 * @param org Organization ID
 * @param userId User ID
 */

async function getMember(org: string, userId: string) {
  const dbGuardedPrisma = guardedPrisma({ orgId: org, userId });
  const users = await dbGuardedPrisma.member.findMany({
    where: {
      organization_id: org,
    },
    select: { id: true, user: { select: { id: true, email: true } } },
  });
  return users;
}

async function getAllMembers(org: string, userId: string) {
  const dbGuardedPrisma = guardedPrisma({ orgId: org, userId });

  const members = await dbGuardedPrisma.member.findMany({
    where: {
      organization_id: org,
    },
    select: {
      id: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          created_at: true,
        },
      },
      role: {
        select: {
          name: true,
        },
      },
      deleted_at: true,
    },
  });

  const transformedMembers = members.map((member) => ({
    name: member.user.name ?? "",
    email: member.user.email ?? "",
    image: member.user.image ?? "",
    role: member.role.name.replace(/^./, member.role.name[0].toUpperCase()),
    dateJoined: member.user.created_at,
    job: "",
    id: member.id,
    userId: member.user.id,
    status: member.deleted_at === null ? "Active" : "Inactive",
  }));

  const selectors = members.map((member) => ({
    userId: member.user.id,
    email: member.user.email ?? "",
  }));

  return [transformedMembers, selectors] as [MemberUser[], MemberSelector[]];
}

async function getMembersAndInvites(org: string, userId: string) {
  try {
    const dbGuardedPrisma = guardedPrisma({ orgId: org, userId });

    // Get members with their user and role info
    const members = await dbGuardedPrisma.member.findMany({
      where: {
        organization_id: org,
      },
      select: {
        id: true,
        deleted_at: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            created_at: true,
          },
        },
        role: {
          select: {
            name: true,
          },
        },
      },
    });

    // Get all invites (both active and expired)
    const invites = await dbGuardedPrisma.invite.findMany({
      where: {
        organization_id: org,
        email: { not: null },
        image: { not: "vendor-invite" },
      },
      select: {
        id: true,
        email: true,
        expired_at: true,
        role: {
          select: {
            name: true,
          },
        },
      },
    });

    const transformedMembers = members.map((member) => ({
      id: member.id,
      userId: member.user.id,
      name: member.user.name ?? "",
      email: member.user.email ?? "",
      image: member.user.image ?? "",
      role: member.role.name
        .replace("_", " ")
        .replace(/^\w/, (c) => c.toUpperCase()),
      job: "",
      dateJoined: member.user.created_at,
      status: member.deleted_at === null ? "Active" : "Inactive",
    }));

    const transformedInvites = invites.map((invite) => ({
      id: invite.id,
      userId: "",
      name: invite.email ?? "",
      email: invite.email ?? "",
      image: "",
      role: invite.role.name
        .replace("_", " ")
        .replace(/^\w/, (c) => c.toUpperCase()),
      job: "",
      dateJoined: invite.expired_at,
      expiredAt: invite.expired_at,
      status: new Date(invite.expired_at) < new Date() ? "Expired" : "Pending",
    }));

    return [...transformedMembers, ...transformedInvites];
  } catch (error) {
    logger.error(
      `getMembersAndInvites() error: ${error instanceof Error ? error.message : String(error)}`,
    );
    return [];
  }
}

/**
 * @param org - organization id
 * @param member - member id
 * @param db - prisma client
 * @returns updated member status
 */
async function updateMemberStatus({
  org,
  member,
  db,
}: {
  org: string;
  member: string;
  db: PrismaClient;
}) {
  const user = await db.member.findFirst({
    where: {
      id: member,
      organization_id: org,
    },
  });

  if (!user) throw Error("User does not exists");
  // Checks if we are deleting the owner of the organization
  if (user.role_id === "owner")
    throw Error("Organization owner cannot be removed");

  const updatedMember = await db.$extends(otherDataAuditLog()).member.delete({
    where: {
      id: member,
    },
  });

  return updatedMember;
}

interface LeaveOrgParams {
  orgId: string;
  userId: string;
}

async function leaveOrg({
  orgId,
  userId,
}: LeaveOrgParams): Promise<Member & { main_org_id?: string }> {
  // Check if user is the org's main_owner
  const db = guardedPrisma({ orgId, userId });
  const org = await db.organization.findFirst();
  if (!org) {
    throw new HttpError(404, `organization with id '${orgId}' not found`, true);
  }
  if (org.main_owner_id === userId) {
    throw new HttpError(
      403,
      "user is not allowed to leave his own organization",
      true,
    );
  }
  try {
    const userMainOrg = await db.organization.findFirst({
      where: { main_owner_id: userId },
    });
    const { id, organization_id, user_id, role_id } = await db.member // .$extends(withAuditLogger({ orgId, userId, message: "leave organization" })) // We have issue here using the `withAuditLogger` since the this user once leave the org will not have access to the orgId anymore, hence, adding record to audit table will get an error
      .delete({
        where: {
          organization_id_user_id: { organization_id: orgId, user_id: userId },
        },
      });
    logger.info(
      `leaveOrg(): Member with user id ${user_id}  leave organization ${organization_id}`,
    );
    return {
      id,
      organization_id,
      user_id,
      role_id,
      main_org_id: userMainOrg?.id,
    };
  } catch (error) {
    logger.error(
      `leaveOrg(): Econuntered error ${error} : Member with user id ${userId}  leave organization ${orgId}`,
    );
    throw new Error(`leaveOrg(): Econuntered error ${error}`);
  }
}

/**
 * Get the user role derived from the `member` table. `Member` table is protected by RLS which require you to set the `app.current_organization_id` and `app.current_user_id`
 */
async function getUserRole({
  userId,
  orgId,
}: {
  userId: string;
  orgId: string;
}) {
  const result = await guardedPrisma({ userId, orgId }).member.findFirst({
    where: { organization_id: orgId, user_id: userId },
    select: { role: { select: { name: true } } },
  });
  return result?.role.name;
}

export {
  getMembersAndInvites,
  leaveOrg,
  getUserRole,
  updateMemberStatus,
  getMember,
  getAllMembers,
};
