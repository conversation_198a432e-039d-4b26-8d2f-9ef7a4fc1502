import useSWRMutation from "swr/mutation";
import { getCsrfToken } from "./helpers";
import {
  CreatePolicy,
  updatePolicyLinkDocs,
} from "~/src/app/[lang]/(private)/[org]/policies/[id]/actions/individual-policy-actions";
import { PrismaClient } from "@askinfosec/database";

export const useCreatePolicy = () => {
  return useSWRMutation(
    "createPolicy",
    async (
      _: string,
      {
        arg: {
          org,
          userId,
          name,
          description,
          category,
          assigned_id,
          approver,
        },
      }: {
        arg: {
          org: string;
          userId: string;
          name: string;
          description: string;
          category: string;
          assigned_id: string[];
          approver: string[];
        };
      },
    ) => {
      const result = await CreatePolicy({
        orgId: org,
        userId,
        name,
        description,
        category,
        approver,
        assigned_id,
      });

      if (result.status === "success") {
        return result.message;
      } else {
        throw new Error(result.message);
      }
    },
  );
};

export const useUpdatePolicyLinkDocs = () => {
  return useSWRMutation(
    "updatePolicyLinkDocs",
    async (
      _: string,
      {
        arg: { id, policyId, orgId, userId },
      }: {
        arg: { id: string; policyId: string; orgId: string; userId: string };
      },
    ) => {
      try {
        const result = await updatePolicyLinkDocs({
          id,
          policyId,
          orgId,
          userId,
        });

        return result;
      } catch (error) {
        throw new Error("Error linking documents: ");
      }
    },
  );
};
