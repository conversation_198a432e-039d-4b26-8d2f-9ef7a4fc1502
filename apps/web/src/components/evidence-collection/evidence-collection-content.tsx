"use client";

import React, { useEffect, useState } from "react";
import { CardContent } from "../ui/card";
import { useTransition } from "react";
import EvidenceCollectionDialog from "./evidence-collection-diag";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import { Button } from "../ui/button";
import DocumentViewerDialog from "@/app/[lang]/(private)/[org]/docs/(document-viewer-dialog)/dialog-document-viewer";
import { CheckIcon, LucideEdit2, Trash2Icon, XIcon } from "lucide-react";
import {
  DeleteEvidenceParams,
  UpdateEvidenceNoteParams,
  deleteEvidence,
  updateEvidenceNote,
} from "./actions";
import { ReloadIcon } from "@radix-ui/react-icons";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Input,
  DialogTitle,
  DialogDescription,
} from "../ui";
import { getCsrfToken } from "@/services/client/helpers";
import Image from "next/image";
import { LoadDocContent } from "~/src/app/[lang]/(private)/[org]/docs/_actions/load-doc-content";
import { ACCEPTED_IMAGE_EXTENSIONS, convertFileToUrl, cn } from "@/lib/utils";
import { parse } from "path";
import { DialogTrigger } from "@radix-ui/react-dialog";
import { SpinnerBasic } from "@/components/spinner";
import DeleteDialog from "../delete-dialog";
import { useCurrentOrg } from "@/services/client/organization";
import { $Enums, Prisma } from "@askinfosec/database";
import EvidenceSkeleton from "./evidence-skeleton";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useGetQuestionById } from "@/services/client/questionnaire";
import { useGetKbById } from "@/services/client/kb";

export enum EvidenceType {
  KnowledgeBase = "Knowledge Base",
  Question = "Question",
  Document = "Document",
  Procedure = "Procedure",
  Control = "Control",
}

const questionnaireFormSchema = z.object({
  id: z.string(),
  orgId: z.string(),
  owned_by_id: z.string(),
  assigned_id: z.string(),
  question: z.string(),
});

type QuestionnaireFormValues = z.infer<typeof questionnaireFormSchema>;

const knowledgeBaseFormSchema = z.object({
  id: z.string(),
  orgId: z.string(),
  owned_by_id: z.string().optional(),
  assigned_id: z.string().optional(),
  question: z.string(),
});

type KnowledgeBaseFormValues = z.infer<typeof knowledgeBaseFormSchema>;

// Evidences here are files so we are only using necessary properties such as name, created  at and etc.
interface EvidenceCollectionInterface {
  id: string;
  evidenceType: EvidenceType;
  evidences: {
    id: string;
    name: string;
    path: string;
    fileId: string;
    created_at: Date;
    created_by: {
      name: string | null;
      email: string | null;
      image: string | null;
    };
    note: string | null;
  }[];
  type: string;
  org: string;
  user: string;
  questionId: string;
  questionnaireId: string;
  scope?: string;
}

type EvidenceItem = {
  id: string;
  name: string;
};

const EvidenceCollectionContent = ({
  id,
  evidences,
  org,
  type,
  user,
  questionnaireId,
  scope,
}: EvidenceCollectionInterface) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState<EvidenceItem | null>(null);
  const [items, setItems] = useState(evidences.slice(0, 10));
  const [maximumItem, setMaximumItem] = useState<number>(10);
  const [noteToEdit, setNoteToEdit] = useState<UpdateEvidenceNoteParams>({
    id: "",
    orgId: org,
    note: "",
    objectType:
      type === "kb" ? "kb" : type === "document" ? "document" : "question",
    questionnaireId: "",
  });
  const [url, setUrl] = useState<string>("");
  const [isPending, startTransition] = useTransition();
  const [isImage, setIsImage] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [isImageLoading, setIsImageLoading] = useState(true);
  const questionForm = useForm<QuestionnaireFormValues>({
    resolver: zodResolver(questionnaireFormSchema),
  });
  const kbForm = useForm<KnowledgeBaseFormValues>({
    resolver: zodResolver(knowledgeBaseFormSchema),
  });
  const getQuestionByIdSWR = useGetQuestionById();
  const getKbByIdSWR = useGetKbById();
  const handleOpenDeleteDialog = (item: EvidenceItem) => {
    setDeleteItem(item);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
  };

  const handleDelete = async () => {
    if (deleteItem) {
      const toDelete: DeleteEvidenceParams = {
        id: deleteItem.id,
        orgId: org,
        objectType:
          type === "kb" ? "kb" : type === "document" ? "document" : "question",
      };

      startTransition(async () => {
        try {
          // try to use server action first
          const result = await deleteEvidence(toDelete);
          if (!result) {
            // we get here maybe because server action failed. retry using api
            await fetch(`/api/org/${org}/evidence`, {
              method: "DELETE",
              body: JSON.stringify(toDelete),
              headers: { "x-csrf-token": getCsrfToken() },
            });
          }
          setItems(items.filter((i) => i.id !== toDelete.id));
        } catch (error) {
          console.error("Error deleting evidence:", error);
        } finally {
          setDeleteItem(null);
          setIsDeleteDialogOpen(false);
          setNoteToEdit({ ...noteToEdit, id: "", note: "" });
        }
      });
    }
  };

  useEffect(() => {
    setItems(evidences.slice(0, 10));
  }, [evidences]);
  const handleLoadMore = () => {
    setItems((prevItems: any) => [
      ...prevItems,
      ...evidences.slice(maximumItem, maximumItem + 10),
    ]);
    setMaximumItem((prev: any) => prev + 10);
  };

  const handleAccordionClick = async (id: string, path: string) => {
    setUrl("");
    setIsImageLoading(true);
    const { ext } = parse(path);

    if (ACCEPTED_IMAGE_EXTENSIONS.includes(ext)) {
      setIsImage(true);
      try {
        const buffer = await LoadDocContent({ fileId: id, orgId: org });
        if (!buffer) {
          // Fallback to API if server action fails
          const response = await fetch(`/api/org/${org}/docs/${id}`);
          const data = await response.json();
          await convertFileToUrl({
            data: data.data,
            path: path,
            setDocument: setUrl,
          });
        } else {
          await convertFileToUrl({
            data: buffer,
            path: path,
            setDocument: setUrl,
          });
        }
      } catch (error) {
        console.error("Error loading image:", error);
      }
    } else {
      setIsImage(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (type === "questionnaire") {
          const data = await getQuestionByIdSWR.trigger({
            id,
            questionnaireId,
            org,
          });
          const d = JSON.parse(data);
          questionForm.setValue("id", d.id);
          questionForm.setValue("question", d.question);
          questionForm.setValue("owned_by_id", d.created_by);
          questionForm.setValue("assigned_id", d.assigned_to);
        } else {
          const data = await getKbByIdSWR.trigger({ id, org });
          const d = JSON.parse(data);
          kbForm.setValue("id", d.id);
          kbForm.setValue("question", d.question);
          kbForm.setValue("assigned_id", d.assigned_to?.id);
          kbForm.setValue("owned_by_id", d.owned_by?.id);
        }
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [type, setLoading]);

  const assigned =
    questionForm.getValues("assigned_id") ||
    kbForm.getValues("owned_by_id") ||
    "";
  const questionId =
    questionForm.getValues("id") || kbForm.getValues("id") || "";

  if (loading) {
    return <EvidenceSkeleton />;
  } else {
    return (
      <CardContent>
        <div className="flex flex-row justify-between items-center">
          {evidences.length >= 1 ? (
            <div>
              Showing {items.length} of {evidences.length}
            </div>
          ) : null}
          <div className="ml-auto">
            {scope !== "vendor" ? (
              <EvidenceCollectionDialog
                id={id}
                org={org}
                type={type}
                user={user}
                assigned={assigned}
                questionId={questionId}
              />
            ) : null}
          </div>
        </div>
        {items.length >= 1 ? (
          <>
            <Accordion type="single" collapsible>
              {items.map((e: any, index: number) => (
                <AccordionItem value={e.path} key={index}>
                  <div className="flex gap-2">
                    <AccordionTrigger
                      onClick={() => {
                        handleAccordionClick(e.fileId, e.path);
                      }}
                      className="hover:no-underline mr-2"
                    ></AccordionTrigger>
                    <div className="text-sm my-auto flex items-center gap-2">
                      <span className="mr-1">File name:</span>
                      <div className="text-primary-link">
                        <DocumentViewerDialog
                          fileName={e.path}
                          path={e.path}
                          fileId={e.fileId}
                          orgId={org}
                        />
                      </div>
                      <p className="ml-1">
                        collected on{" "}
                        <span className="font-bold ml-1">
                          {new Date(e.created_at).toISOString().slice(0, 10)}
                        </span>
                      </p>
                      {isPending && (
                        <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      {!isPending && (
                        <Trash2Icon
                          id={e.id}
                          size={24}
                          className="hover:cursor-pointer hover:opacity-80 hover:border hover:border-foreground rounded-sm p-1"
                          onClick={() =>
                            handleOpenDeleteDialog({ id: e.id, name: e.name })
                          }
                        />
                      )}
                    </div>
                  </div>
                  <AccordionContent>
                    <div className="ml-6 space-y-1">
                      <div className="space-x-1">
                        <span className="font-bold">Uploaded by:</span>
                        <span>{e.created_by.email}</span>
                      </div>
                      <div className="space-y-1">
                        <span className="font-bold">Notes:</span>
                        <div className="flex flex-row gap-2 items-center ">
                          {noteToEdit.id === "" && (
                            <>
                              <p>{e.note !== "" ? e.note : ""}</p>
                              <LucideEdit2
                                size={20}
                                className="hover:cursor-pointer hover:opacity-80 hover:border hover:border-foreground rounded-sm p-1"
                                id={e.id}
                                onClick={(ev) => {
                                  setNoteToEdit({
                                    ...noteToEdit,
                                    id: ev.currentTarget.id,
                                    note: e.note || "",
                                  });
                                }}
                              />
                            </>
                          )}
                          {noteToEdit.id === e.id && (
                            <div className="flex gap-2 items-center">
                              <Input
                                name="note"
                                className="border w-96"
                                defaultValue={e.note!}
                                onChange={(ev: any) => {
                                  setNoteToEdit({
                                    ...noteToEdit,
                                    note: ev.target.value,
                                  });
                                }}
                              />
                              {isPending && (
                                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                              )}
                              {!isPending && (
                                <>
                                  <CheckIcon
                                    type="button"
                                    className="hover:cursor-pointer hover:opacity-80 hover:border hover:border-foreground rounded-sm p-1"
                                    onClick={() =>
                                      startTransition(() => {
                                        try {
                                          // try to use server action first
                                          updateEvidenceNote(noteToEdit).then(
                                            async (n) => {
                                              if (!n) {
                                                // we get here because server action maybe failed. Retry using api
                                                await fetch(
                                                  `/api/org/${org}/evidence`,
                                                  {
                                                    method: "PATCH",
                                                    body: JSON.stringify(
                                                      noteToEdit,
                                                    ),
                                                    headers: {
                                                      "x-csrf-token":
                                                        getCsrfToken(),
                                                    },
                                                  },
                                                );
                                              }
                                            },
                                          );
                                          setItems(
                                            items.map((i) => {
                                              if (i.id === noteToEdit.id) {
                                                return {
                                                  ...i,
                                                  note: noteToEdit.note,
                                                };
                                              } else {
                                                return i;
                                              }
                                            }),
                                          );
                                        } catch (error) {
                                        } finally {
                                          setNoteToEdit({
                                            ...noteToEdit,
                                            id: "",
                                            note: "",
                                          });
                                        }
                                      })
                                    }
                                  />
                                  <XIcon
                                    className="hover:cursor-pointer hover:opacity-80 hover:border hover:border-foreground rounded-sm p-1"
                                    onClick={() => {
                                      setNoteToEdit({
                                        ...noteToEdit,
                                        id: "",
                                        note: "",
                                      });
                                    }}
                                  />
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      {isImage && (
                        <div className="flex gap-4">
                          <p className="font-bold">Preview:</p>
                          {url !== "" ? (
                            <Dialog open={open} onOpenChange={setOpen}>
                              <DialogTrigger asChild>
                                <div className="cursor-pointer hover:opacity-90 transition-opacity">
                                  <Image
                                    width={120}
                                    height={120}
                                    alt={e.path}
                                    src={url}
                                    className="object-cover rounded-md"
                                    onLoadingComplete={() =>
                                      setIsImageLoading(false)
                                    }
                                  />
                                </div>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[90vw] sm:max-h-[90vh] p-0">
                                <div className="p-4 border-b">
                                  <DialogHeader>
                                    <DialogTitle>{e.path}</DialogTitle>
                                    <DialogDescription>
                                      Preview of uploaded evidence file
                                    </DialogDescription>
                                  </DialogHeader>
                                </div>
                                <div className="relative w-full h-[80vh] bg-black/5">
                                  {isImageLoading ? (
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <SpinnerBasic />
                                    </div>
                                  ) : null}
                                  <div className="relative w-full h-full flex items-center justify-center">
                                    <Image
                                      alt={e.path}
                                      src={url}
                                      className={cn(
                                        "max-w-full max-h-full w-auto h-auto object-contain transition-opacity duration-300",
                                        isImageLoading
                                          ? "opacity-0"
                                          : "opacity-100",
                                      )}
                                      width={1200}
                                      height={800}
                                      onLoadingComplete={() =>
                                        setIsImageLoading(false)
                                      }
                                    />
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          ) : (
                            <div className="w-[120px] h-[120px] flex items-center justify-center bg-muted rounded-md">
                              <ReloadIcon className="h-6 w-6 animate-spin" />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
            <DeleteDialog
              isOpen={isDeleteDialogOpen}
              title="Remove Evidence"
              confirmText="DELETE"
              onConfirm={handleDelete}
              onCancel={handleCloseDeleteDialog}
              isProcessing={isPending}
            >
              <span className="text-muted-foreground">
                Are you sure you want to remove the evidence{" "}
                <span className="text-foreground italic">
                  {" "}
                  {` ${deleteItem?.name}`}{" "}
                </span>{" "}
                ? This action cannot be undone.
              </span>
            </DeleteDialog>
          </>
        ) : (
          <div className="text-center">
            <p className="text-sm italic">No linked files</p>
          </div>
        )}
        {evidences.length > 10 && items.length < evidences.length ? (
          <div className="flex flex-col items-center">
            <Button variant="link" onClick={() => handleLoadMore()}>
              Load More
            </Button>
          </div>
        ) : null}
      </CardContent>
    );
  }
};
export default EvidenceCollectionContent;
