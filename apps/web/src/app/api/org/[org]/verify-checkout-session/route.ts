import { type NextRequest, NextResponse } from "next/server";
import {
  NextRouteHandler,
  withLoggingRouteHandler,
  withRbacRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { stripe } from "@/lib/stripe";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

const verifyCheckoutSessionHandler: NextRouteHandler = async (
  req: NextRequest,
  { params }: { params: { org: string } },
) => {
  try {
    const sessionId = req.nextUrl.searchParams.get("session_id");
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: "Missing session_id" },
        { status: 400 },
      );
    }

    logger.info(
      { sessionId, orgId: params.org, userId: req.session.user.id },
      "Verifying checkout session",
    );

    // Retrieve the checkout session from Stripe
    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId);

    // Check if the session was successful
    if (checkoutSession.payment_status !== "paid") {
      return NextResponse.json(
        { success: false, error: "Payment not completed" },
        { status: 200 },
      );
    }

    // Check if we have a subscription record for this organization
    const subscriptionTierId = checkoutSession.metadata?.subscriptionTierId;

    if (!subscriptionTierId) {
      return NextResponse.json(
        { success: false, error: "Missing subscription tier ID" },
        { status: 200 },
      );
    }

    // Check if we have a subscription record
    const subscription = await prisma.organizationSubscription.findFirst({
      where: {
        organization_id: params.org,
        subscription_tier_id: subscriptionTierId,
        canceled_at: null,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { success: false, error: "Subscription not found" },
        { status: 200 },
      );
    }

    return NextResponse.json(
      { success: true, data: { subscription } },
      { status: 200 },
    );
  } catch (error) {
    logger.error({ error }, "Failed to verify checkout session");
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 400 },
    );
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ org: string }> },
) {
  const resolvedParams = await params;
  const handler = withSessionRouteHandler()(
    withLoggingRouteHandler(
      withRbacRouteHandler({ allowedRoles: ["owner", "admin", "member"] })(
        verifyCheckoutSessionHandler,
      ),
    ),
  );
  return await handler(request, { params: resolvedParams });
}
