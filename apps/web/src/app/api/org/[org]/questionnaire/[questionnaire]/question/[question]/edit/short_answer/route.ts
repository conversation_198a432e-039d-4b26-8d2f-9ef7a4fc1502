import { NextRequest, NextResponse } from "next/server";

import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { guardedPrisma, tenantGuardedPrisma } from "@/lib/prisma";
import {
  updateQnAQuestion,
  updateShortAnswer,
} from "@/services/server/questionnaires";
import { ShortAnswer } from "@askinfosec/database";

interface QuestionRequestBody {
  answer: ShortAnswer;
}
/**
 * Delete question of a questionnaire
 */
const editShortAnswerHandler = async (
  req: NextRequest,
  { params: { org, question } }: NextRouteHandlerContext,
) => {
  const user = req.session.user.id;
  const db = tenantGuardedPrisma(org);
  const { answer }: QuestionRequestBody = await req.json();
  // The q here stands for question, while the question on the handle params is the id.
  const updateQuestion = await updateShortAnswer(question, answer, org);

  return NextResponse.json(updateQuestion);
};
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
) {
  const resolvedParams = await params;
  return await editShortAnswerHandler(request, { params: resolvedParams });
}
