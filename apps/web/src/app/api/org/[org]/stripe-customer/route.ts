import { type NextRequest, NextResponse } from "next/server";
import { type Session } from "next-auth";
import {
  NextRouteHandler,
  withLoggingRouteHandler,
  withRbacRouteHandler,
} from "@/app/api/middlewares";
import { stripe } from "@/lib/stripe";
import { retrieveCustomerId } from "@/services/server/stripe";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

const getStripeCustomerHandler: NextRouteHandler = async (
  req: NextRequest & { session: Session },
  { params }: { params: { org: string } },
) => {
  try {
    // Get the organization from the database first to check if it has a stripe_customer_id
    const organizationBefore = await prisma.organization.findUnique({
      where: { id: params.org },
      select: {
        id: true,
        company_name: true,
        stripe_customer_id: true,
      },
    });

    // Log the state before synchronization
    logger.info(
      {
        orgId: params.org,
        hadStripeCustomerId: !!organizationBefore?.stripe_customer_id,
        stripeCustomerIdBefore:
          organizationBefore?.stripe_customer_id || "null",
      },
      "Organization state before Stripe customer ID retrieval",
    );

    // Get the Stripe customer ID for this organization (this will also sync if needed)
    const customerId = await retrieveCustomerId({ orgId: params.org });

    // Get the organization again to see if it was updated
    const organizationAfter = await prisma.organization.findUnique({
      where: { id: params.org },
      select: {
        id: true,
        company_name: true,
        stripe_customer_id: true,
      },
    });

    // Log the state after synchronization
    logger.info(
      {
        orgId: params.org,
        hasStripeCustomerId: !!organizationAfter?.stripe_customer_id,
        stripeCustomerIdAfter: organizationAfter?.stripe_customer_id || "null",
        wasUpdated:
          organizationBefore?.stripe_customer_id !==
          organizationAfter?.stripe_customer_id,
        customerId,
      },
      "Organization state after Stripe customer ID retrieval",
    );

    if (!customerId) {
      return NextResponse.json(
        { error: "No Stripe customer found for this organization" },
        { status: 404 },
      );
    }

    // Retrieve the customer from Stripe with expanded subscriptions and default payment method
    // Be careful with expand - only expand fields that are guaranteed to exist
    const customer = await stripe.customers.retrieve(customerId, {
      expand: ["subscriptions", "invoice_settings.default_payment_method"],
    });

    if (customer.deleted) {
      return NextResponse.json(
        { error: "Stripe customer has been deleted" },
        { status: 404 },
      );
    }

    // Get the organization from the database
    const organization = await prisma.organization.findUnique({
      where: { id: params.org },
      select: {
        id: true,
        company_name: true,
        stripe_customer_id: true,
      },
    });

    // Get recent invoices for this customer
    // Don't expand payment_intent as it might not exist for all invoices
    const invoices = await stripe.invoices.list({
      customer: customerId,
      limit: 10,
    });

    // Get payment methods for this customer
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: "card",
    });

    return NextResponse.json({
      data: {
        customer,
        organization,
        invoices: invoices.data,
        paymentMethods: paymentMethods.data,
      },
    });
  } catch (error) {
    logger.error({ error }, "Failed to retrieve Stripe customer information");
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 },
    );
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ org: string }> },
) {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withRbacRouteHandler({ allowedRoles: ["owner", "admin"] })(
      getStripeCustomerHandler,
    ),
  );
  return await handler(request, { params: resolvedParams });
}
