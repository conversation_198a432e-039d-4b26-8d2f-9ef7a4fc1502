import { type NextRequest, NextResponse } from "next/server";
import {
  NextRouteHandler,
  withLoggingRouteHandler,
  withRbacRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { stripe } from "@/lib/stripe";
import { retrieveCustomerId, createCustomer } from "@/services/server/stripe";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";
import { isSubscriptionActive } from "@/services/server/utils/get-active-org-subs";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

const createCheckoutSessionHandler: NextRouteHandler = async (
  req: NextRequest,
  { params }: { params: { org: string } },
) => {
  try {
    const { priceId } = await req.json();
    logger.info(
      { priceId, orgId: params.org, userId: req.session.user.id },
      "Creating checkout session",
    );
    const sessionId = await createStripeCheckoutSession({
      userId: req.session.user.id,
      orgId: params.org,
      priceId,
    });
    return NextResponse.json(
      { success: true, data: { sessionId } },
      { status: 200 },
    );
  } catch (error) {
    logger.error({ error }, "Failed to create checkout session");
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 400 },
    );
  }
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ org: string }> },
) {
  const resolvedParams = await params;
  const handler = withSessionRouteHandler()(
    withLoggingRouteHandler(
      withRbacRouteHandler({ allowedRoles: ["owner"] })(
        createCheckoutSessionHandler,
      ),
    ),
  );
  return await handler(request, { params: resolvedParams });
}

async function createStripeCheckoutSession({
  userId,
  orgId,
  priceId,
}: {
  userId: string;
  orgId: string;
  priceId: string;
}): Promise<string> {
  logger.debug(
    { orgId, userId, priceId },
    "Starting Stripe checkout session creation",
  );

  const organization = await prisma.organization.findUnique({
    where: { id: orgId },
    select: { company_name: true, main_owner_id: true },
  });
  if (!organization) throw new Error("Organization not found");

  const userRecord = await prisma.user.findUnique({
    where: { id: userId },
    select: { email: true },
  });
  let email = userRecord?.email;
  if (!email) {
    const ownerRecord = await prisma.user.findUnique({
      where: { id: organization.main_owner_id },
      select: { email: true },
    });
    if (!ownerRecord?.email) throw new Error("Owner email not found");
    email = ownerRecord.email;
  }

  let customerId = await retrieveCustomerId({ orgId });
  if (!customerId) {
    const customer = await createCustomer({
      orgId,
      orgName: organization.company_name,
      email,
      userId,
    });
    customerId = customer.id;
  }

  // Find the subscription tier and its associated product
  const subscriptionTier = await prisma.subscriptionTier.findFirst({
    where: { price: { stripe_price_id: priceId } },
    include: {
      subscription: {
        include: {
          product: true,
        },
      },
    },
  });
  if (!subscriptionTier) throw new Error("Subscription tier not found");

  const productId = subscriptionTier.subscription.product_id;

  // Check if the organization already has an active subscription to this tier
  const existingTierSubscription =
    await prisma.organizationSubscription.findFirst({
      where: {
        organization_id: orgId,
        subscription_tier_id: subscriptionTier.id,
        stripe_sub_id: { not: null },
      },
    });

  // Only block if the subscription is active
  if (
    existingTierSubscription &&
    isSubscriptionActive({
      canceled_at: existingTierSubscription.canceled_at,
      cancel_at_period_end:
        existingTierSubscription.cancel_at_period_end ?? undefined,
      current_period_end: existingTierSubscription.current_period_end,
    })
  ) {
    throw new Error(
      "Organization already has an active subscription to this tier",
    );
  }

  // Check if the organization already has an active subscription to any tier of this product
  const existingProductSubscriptions =
    await prisma.organizationSubscription.findMany({
      where: {
        organization_id: orgId,
        subscription_tier: {
          subscription: {
            product_id: productId,
          },
        },
      },
      include: {
        subscription_tier: {
          include: {
            subscription: true,
          },
        },
      },
    });

  // Filter to only active subscriptions
  const activeProductSubscriptions = existingProductSubscriptions.filter(
    (subscription) =>
      isSubscriptionActive({
        canceled_at: subscription.canceled_at,
        cancel_at_period_end: subscription.cancel_at_period_end ?? undefined,
        current_period_end: subscription.current_period_end,
      }),
  );

  if (activeProductSubscriptions.length > 0) {
    throw new Error(
      "Organization already has an active subscription to this product. Please check your billing page for details.",
    );
  }

  const baseUrl = process.env.NEXTAUTH_URL || "https://localhost:3000";
  const session = await stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ["card"],
    line_items: [{ price: priceId, quantity: 1 }],
    mode: "subscription",
    success_url: `${baseUrl}/en/subscription/success?org=${orgId}&session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${baseUrl}/en/subscription/canceled?org=${orgId}`,
    metadata: {
      orgId,
      userId,
      priceId,
      subscriptionTierId: subscriptionTier.id,
    },
    allow_promotion_codes: true,
    billing_address_collection: "auto",
  });

  return session.id;
}
