import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/auth";
import { passKeyService } from "@/services/server/webauthn/webauthn-service";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "passkey-api", "delete");
const prisma = bypassedPrisma as unknown as PrismaClient;

// Request validation schema
const requestSchema = z.object({
  id: z.string(),
});

/**
 * POST handler for deleting a user's PassKey
 * This endpoint deletes a specified PassKey from the user's account
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const { id } = result.data;

    // Get the organization ID from the URL path parameter instead of querying the database
    // This follows the pattern used in other API routes and avoids database query issues
    const pathParts = request.nextUrl.pathname.split("/");
    const orgIndex = pathParts.findIndex((part) => part === "org") + 1;
    const orgId = pathParts[orgIndex];

    logger.addLog({
      usageType: UsageType.debug,
      message: "Extracted organization ID from URL path",
      obj: { orgId, path: request.nextUrl.pathname },
    });

    if (!orgId) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Organization not found for user",
        obj: { userId: session.user.id },
      });
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 400 },
      );
    }

    // Verify ownership and delete authenticator
    const deleted = await passKeyService.deleteAuthenticator(
      id,
      session.user.id,
      orgId,
    );

    if (!deleted) {
      return NextResponse.json(
        { error: "Authenticator not found or not owned by user" },
        { status: 404 },
      );
    }

    // Check if user has any remaining authenticators
    const remainingAuthenticators = await passKeyService.getUserAuthenticators(
      session.user.id,
      orgId,
    );

    // We no longer automatically disable MFA when all passkeys are deleted
    // This allows users to use other MFA methods even without passkeys

    logger.addLog({
      usageType: UsageType.info,
      message: "Deleted user PassKey",
      obj: {
        userId: session.user.id,
        authenticatorId: id,
        remainingCount: remainingAuthenticators.length,
      },
    });

    // Return success response
    return NextResponse.json({
      success: true,
      remainingPasskeys: remainingAuthenticators.length,
    });
  } catch (error) {
    // Enhanced error logging with more details
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.addLog({
      usageType: UsageType.error,
      message: "Error deleting user PassKey",
      obj: { error: errorMessage, stack: errorStack },
    });

    // Return more detailed error information
    return NextResponse.json(
      {
        error: "Failed to delete security key",
        details: errorMessage,
      },
      { status: 500 },
    );
  }
}
