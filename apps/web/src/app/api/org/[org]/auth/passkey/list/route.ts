import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { passKeyService } from "@/services/server/webauthn/webauthn-service";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

const prisma = bypassedPrisma as unknown as PrismaClient;

// Initialize logger
const logger = new LogManager(LogLevel.debug, "passkey-api", "list");

/**
 * GET handler for listing user's PassKeys
 * This endpoint returns a list of the user's registered PassKeys
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ org: string }> },
) {
  const { org } = await context.params;
  try {
    // Verify authentication
    logger.addLog({
      usageType: UsageType.debug,
      message: "Starting PassKey list endpoint",
      obj: { time: new Date().toISOString() },
    });
    const session = await auth();
    logger.addLog({
      usageType: UsageType.debug,
      message: "Auth result",
      obj: { session },
    });
    if (!session || !session.user?.id) {
      logger.addLog({
        usageType: UsageType.error,
        message: "No session or user ID",
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    if (!org) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Organization not found for user",
        obj: { userId: session.user.id },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 400 },
      );
    }

    // Get user's authenticators
    logger.addLog({
      usageType: UsageType.debug,
      message: "Fetching user authenticators",
      obj: { userId: session.user.id, org },
    });
    const authenticators = await passKeyService.getUserAuthenticators(
      session.user.id,
      org,
    );
    logger.addLog({
      usageType: UsageType.debug,
      message: "Fetched authenticators",
      obj: { authenticators },
    });

    // Map to client-safe format
    const clientAuthenticators = authenticators.map((auth: any) => ({
      id: auth.id,
      name: auth.name || "Unnamed key",
      deviceType: auth.credentialDeviceType,
      backedUp: auth.credentialBackedUp,
      createdAt: auth.createdAt.toISOString(),
    }));

    logger.addLog({
      usageType: UsageType.debug,
      message: "Mapped authenticators for client",
      obj: { clientAuthenticators },
    });
    logger.addLog({
      usageType: UsageType.info,
      message: "Listed user PassKeys",
      obj: {
        userId: session.user.id,
        count: authenticators.length,
      },
    });

    logger.printAll();
    // Return authenticators
    return NextResponse.json({
      authenticators: clientAuthenticators,
    });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error listing user PassKeys",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to list security keys" },
      { status: 500 },
    );
  }
}
