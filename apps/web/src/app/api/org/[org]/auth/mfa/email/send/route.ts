import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { transport } from "@/lib/email";
import { randomInt } from "crypto";
import { MfaMethod } from "@/types/mfa-types";
import { PrismaClient } from "@askinfosec/database";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "mfa-api", "email-send");

// Rate limiting constants
const MAX_REQUESTS = 5; // Maximum 5 code requests
const RATE_LIMIT_WINDOW_MS = 10 * 60 * 1000; // 10 minutes window

/**
 * POST handler for sending MFA email verification code
 * This endpoint generates and sends a verification code via email
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth();
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if MFA is enabled
    if (!session.user.mfaEnabled) {
      return NextResponse.json(
        { error: "MFA is not enabled for this user" },
        { status: 400 },
      );
    }

    // Check if MFA is already verified
    if (session.user.mfaVerified) {
      return NextResponse.json(
        { error: "MFA is already verified for this session" },
        { status: 400 },
      );
    }

    // Get user's email
    const email = session.user.email;
    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 },
      );
    }

    logger.addLog({
      usageType: UsageType.info,
      message: "Processing MFA email code request",
      obj: {
        userId: session.user.id,
        email,
      },
    });

    // Rate limiting check - Get count of recent code requests
    const prisma = bypassedPrisma as unknown as PrismaClient;
    const rateLimitWindowStart = new Date(Date.now() - RATE_LIMIT_WINDOW_MS);

    // Check for recent code requests in email_auth_code table
    try {
      const recentCodeRequests = await prisma.emailAuthCode.findMany({
        where: {
          userId: session.user.id,
          createdAt: {
            gte: rateLimitWindowStart,
          },
        },
      });

      const requestCount = recentCodeRequests.length;

      logger.addLog({
        usageType: UsageType.info,
        message: "Rate limit check",
        obj: {
          userId: session.user.id,
          requestCount,
          maxRequests: MAX_REQUESTS,
          windowStartTime: rateLimitWindowStart,
        },
      });

      // If user has exceeded the limit, return 429 Too Many Requests
      if (requestCount >= MAX_REQUESTS) {
        logger.addLog({
          usageType: UsageType.warning,
          message: "Rate limit exceeded for MFA email code requests",
          obj: {
            userId: session.user.id,
            email,
            requestCount,
            maxRequests: MAX_REQUESTS,
          },
        });

        return NextResponse.json(
          {
            error: "Too many verification code requests",
            message:
              "You've requested too many codes recently. Please try again later.",
            retryAfter: Math.ceil(RATE_LIMIT_WINDOW_MS / 60000), // minutes until reset
          },
          {
            status: 429,
            headers: {
              "Retry-After": Math.ceil(RATE_LIMIT_WINDOW_MS / 1000).toString(), // seconds
            },
          },
        );
      }
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error checking rate limits",
        obj: { error },
      });
      // Continue with code generation even if rate limit check fails
      // This prevents a database error from blocking authentication
    }

    // Generate a random 6-digit code
    const code = randomInt(100000, 999999).toString();

    // Store the code in the database with expiration time (10 minutes)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    logger.addLog({
      usageType: UsageType.info,
      message: "Storing MFA code in user record",
      obj: {
        userId: session.user.id,
        expiresAt,
      },
    });

    // Use raw SQL to update the MFA code fields since they're not in the Prisma schema yet
    // This is a temporary solution until the schema is updated
    try {
      await bypassedPrisma.$executeRaw`
        UPDATE "user" 
        SET mfa_code = ${code}, mfa_code_expires_at = ${expiresAt} 
        WHERE id = ${session.user.id}
      `;
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error updating user MFA code",
        obj: { error },
      });
      throw new Error("Failed to store verification code");
    }

    // Create a new entry in email_auth_code table - this serves both for the code and for rate limiting
    try {
      await prisma.emailAuthCode.create({
        data: {
          userId: session.user.id,
          code,
          expires: expiresAt,
          used: false,
          type: "authentication", // Add type for authentication
        },
      });

      logger.addLog({
        usageType: UsageType.info,
        message: "Created email auth code record",
        obj: {
          userId: session.user.id,
          expires: expiresAt,
          type: "authentication", // Log the type
        },
      });
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error creating email auth code record",
        obj: { error },
      });
      // Continue even if this fails, as the code is already stored in the user record
    }

    // Set user's default MFA method to email if not already set
    if (!session.user.defaultMfaMethod) {
      try {
        // Use raw SQL to update the default MFA method to avoid type issues
        await bypassedPrisma.$executeRaw`
          UPDATE "user" 
          SET default_mfa_method = ${MfaMethod.EMAIL}
          WHERE id = ${session.user.id}
        `;
      } catch (error) {
        logger.addLog({
          usageType: UsageType.error,
          message: "Error updating default MFA method",
          obj: { error },
        });
        // Continue even if this fails
      }
    }

    // Create HTML content for the email
    const htmlContent = `
      <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; font-size: 24px;">Your Verification Code</h1>
        <p style="color: #666; font-size: 16px;">Use the following code to complete your sign-in:</p>
        <div style="background-color: #f5f5f5; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
          <span style="font-size: 32px; font-weight: bold; letter-spacing: 4px;">${code}</span>
        </div>
        <p style="color: #666; font-size: 14px;">This code will expire in 10 minutes.</p>
        <p style="color: #666; font-size: 14px;">If you didn't request this code, please ignore this email.</p>
      </div>
    `;

    // Send the verification code via email
    try {
      await transport.sendMail({
        to: email,
        from: process.env.EMAIL_FROM,
        subject: "Your AskInfosec Verification Code",
        html: htmlContent,
        text: `Your verification code is: ${code}\n\nThis code will expire in 10 minutes.\n\nIf you didn't request this code, please ignore this email.`,
      });

      logger.addLog({
        usageType: UsageType.info,
        message: "Email sent successfully",
        obj: {
          userId: session.user.id,
          email,
        },
      });
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error sending email",
        obj: { error },
      });
      throw new Error("Failed to send verification code email");
    }

    // Get updated count for rate limit info
    let remainingRequests = MAX_REQUESTS - 1; // Default if we can't query
    try {
      const updatedCount = await prisma.emailAuthCode.count({
        where: {
          userId: session.user.id,
          createdAt: {
            gte: rateLimitWindowStart,
          },
        },
      });
      remainingRequests = Math.max(0, MAX_REQUESTS - updatedCount);
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error getting updated rate limit count",
        obj: { error },
      });
    }

    logger.addLog({
      usageType: UsageType.info,
      message: "Sent MFA email verification code",
      obj: {
        userId: session.user.id,
        email,
        remainingRequests,
      },
    });

    // Return success response with rate limit information
    return NextResponse.json({
      success: true,
      message: "Verification code sent",
      rateLimit: {
        remaining: remainingRequests,
        limit: MAX_REQUESTS,
        resetAfter: Math.ceil(RATE_LIMIT_WINDOW_MS / 60000), // minutes
      },
    });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error sending MFA email verification code",
      obj: { error },
    });

    return NextResponse.json(
      {
        error: "Failed to send verification code",
        message:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
