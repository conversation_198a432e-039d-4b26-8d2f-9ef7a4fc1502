import { Document as PrismaDocument } from "@langchain/core/documents";
import { type NextRequest, NextResponse } from "next/server";
import { DocumentVector } from "@askinfosec/database";

import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { updateDoc, getDocContents } from "@/services/server/docs";
import { tenantGuardedPrisma } from "@/lib/prisma";
import {
  initVectorStore,
  ID_COLUMN,
  CONTENT_COLUMN,
} from "@/lib/langchain/vector-store";
import { LogLevel, LogManager } from "@/observability/migration-shim";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { embedData } from "@/lib/langchain/genai";
import { nanoid } from "nanoid";
import { LoadDocContent } from "~/src/app/[lang]/(private)/[org]/docs/_actions/load-doc-content";

interface RequestBody {
  categoryId: string;
  userId: string;
  documentType: string;
  train: boolean;
  scope_id: string;
}

/**
 *
 * @param id - File Id
 * @param org - Organization Id
 * @param categoryId - Category Id
 * @param userId - User Id
 * @param documentType - Type id
 *
 * @returns Updated document / file
 */

const updateDocsHandler = async (
  req: NextRequest,
  { params }: NextRouteHandlerContext,
) => {
  const { org, file } = params as { org: string; file: string };
  const { categoryId, userId, documentType, train, scope_id }: RequestBody =
    await req.json();
  const logManager = new LogManager(LogLevel.info, org, req.session.user.id);

  const db = tenantGuardedPrisma(org);

  try {
    if (train) {
      const vectorStore = initVectorStore<DocumentVector>({
        tableName: "DocumentVector",
        vectorColumnName: "vector",
        columns: { id: ID_COLUMN, content: CONTENT_COLUMN },
        db,
        org,
      });
      const docs = await getDocContents(file, org);
      const res: PrismaDocument<Record<string, unknown>>[] = docs?.map(
        (doc) => {
          return { pageContent: doc.content, metadata: doc };
        },
      )!;
      const transactionId = nanoid();
      await embedData({
        transactionId,
        featureType: "document_upload",
        vectorStore,
        data: res,
        logManager,
        userId,
        org,
        reqId: req.reqId,
      });
      logger.info(
        logManager.eventLogsList,
        `ai:api:org:${org}:docs:${file}:updateDocsHandler`,
      );
    }
  } catch (e) {
    console.log("Error training document: ", e);
  }

  const updatedFile = await updateDoc({
    org,
    file,
    train,
    category: categoryId,
    type: documentType,
    db,
    user: userId,
    scope_id,
  });

  return NextResponse.json(updatedFile);
};

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(updateDocsHandler),
  );
  return await handler(request, { params: resolvedParams });
}

const getFileHandler = async (
  req: NextRequest,
  { params }: NextRouteHandlerContext,
) => {
  const data = await LoadDocContent({ fileId: params.file, orgId: params.org });
  return NextResponse.json(data);
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(getFileHandler),
  );
  return await handler(request, { params: resolvedParams });
}
