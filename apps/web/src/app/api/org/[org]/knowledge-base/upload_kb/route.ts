import { type NextRequest, NextResponse } from "next/server";
import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { Document } from "@langchain/core/documents";
import { KnowledgeBase } from "@askinfosec/database";
import { tenantGuardedPrisma } from "@/lib/prisma";
import { parseKb } from "@/lib/api/utils";
import { uploadKb } from "@/services/server/kb";
import {
  CONTENT_COLUMN,
  ID_COLUMN,
  initVectorStore,
} from "@/lib/langchain/vector-store";
import { LogLevel, LogManager } from "@/observability/migration-shim";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { embedData } from "@/lib/langchain/genai";
import { nanoid } from "nanoid";

/**
 * Add knowledge base from file upload
 */
const createKnowledgeBaseHandler = async (
  req: NextRequest,
  { params: { org } }: NextRouteHandlerContext,
) => {
  const userId = req.session.user.id;
  const db = tenantGuardedPrisma(org);
  const logManager = new LogManager(LogLevel.info, org, req.session.user.id);

  // process uploaded file
  const formData = await req.formData();
  const file: File | null = formData.get("file") as unknown as File;

  if (!file) {
    return new NextResponse("No file uploaded", { status: 400 });
  }

  const questions = await parseKb(file);
  if (!questions) {
    return new NextResponse("Failed to parse file content", { status: 400 });
  }

  const vectorStore = initVectorStore<KnowledgeBase>({
    tableName: "KnowledgeBase",
    vectorColumnName: "vector",
    columns: { id: ID_COLUMN, question: CONTENT_COLUMN },
    db,
    org,
  });
  const result = await Promise.all([
    ...questions.map((question: any) => {
      return uploadKb({
        org,
        question: question.question,
        assigned_id: userId,
        answer: question.answer,
        db,
        userId,
      });
    }),
  ]);
  const res: Document<Record<string, unknown>>[] = result?.map((q) => {
    return { pageContent: q.question, metadata: q };
  });
  const transactionId = nanoid();
  embedData({
    transactionId,
    featureType: "knowledge_base",
    vectorStore,
    data: res,
    logManager,
    userId,
    org,
    reqId: req.reqId,
  });

  logger.info(
    logManager.eventLogsList,
    `ai:api:org:${org}:knowledge-base:upload_kb:createKnowledgeBaseHandler`,
  );
  return NextResponse.json(result);
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(createKnowledgeBaseHandler),
  );
  return await handler(request, { params: resolvedParams });
}
