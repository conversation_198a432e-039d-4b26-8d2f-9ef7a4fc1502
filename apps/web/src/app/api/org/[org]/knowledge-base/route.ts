import { type NextRequest, NextResponse } from "next/server";
import { KnowledgeBase as PrismaKnowledgeBase } from "@askinfosec/database";

import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { getOrg } from "@/services/server/organization";
import { KnowledgeBase } from "@/models/knowledge-base";
import { tenantGuardedPrisma, withAuditLogger } from "@/lib/prisma";
import {
  CONTENT_COLUMN,
  ID_COLUMN,
  initVectorStore,
} from "@/lib/langchain/vector-store";
import { LogLevel, LogManager } from "@/observability/migration-shim";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { embedData } from "@/lib/langchain/genai";
import { nanoid } from "nanoid";

interface RequestBody {
  question: string;
  category_id: string;
  assigned_id: string;
  answer: string;
  scope_id: string;
  questionId?: string;
}
/**
 * Add knowledge base
 */
const createKnowledgeBaseHandler = async (
  req: NextRequest,
  { params: { org } }: NextRouteHandlerContext,
) => {
  const {
    question,
    category_id,
    answer,
    assigned_id,
    scope_id,
    questionId,
  }: RequestBody = await req.json();
  const db = tenantGuardedPrisma(org);
  const userId = req.session.user.id;
  const currentOrg = await getOrg({ userId, orgId: org });
  const logManager = new LogManager(LogLevel.info, org, userId);

  if (
    currentOrg.current_user_role !== "owner" &&
    currentOrg.current_user_role !== "admin"
  ) {
    return NextResponse.json(
      {
        message:
          "Access Denied: You do not have the required permissions to perform this action.",
      },
      {
        status: 401,
      },
    );
  }

  // TODO: This can be improve by using similarity search not just checking for exact question equality
  const existing = await db.knowledgeBase.findMany({
    where: {
      question: question,
    },
  });

  if (existing && existing.length > 0) {
    return NextResponse.json(
      {
        message: "Same question already exist!",
      },
      { status: 500 },
    );
  }

  const item = await db
    .$extends(withAuditLogger({ orgId: org, userId, message: "KB create" }))
    .knowledgeBase.create({
      data: {
        category_id: category_id,
        question: question,
        answer: answer!,
        status: "in_review",
        organization_id: org,
        created_by_id: userId,
        last_update_by_id: userId,
        assigned_id,
        owned_by_id: assigned_id,
        scope_id: scope_id,
      },
    });

  // Pass the questionId which is only optional when we want to copy.
  // check if there is any evidence related to the question id, if there is
  // link to the kb.
  if (questionId) {
    const q = await db.question.findFirst({
      where: {
        id: questionId,
      },
    });

    // Check if question exists and if there is a linked document.
    if (q && q.ref_docs.length >= 1) {
      q.ref_docs.map(async (doc) => {
        await db.evidence.create({
          data: { knowledgeBaseId: item.id, fileId: doc },
        });
      });
    }

    await db.evidence.updateMany({
      where: {
        questionId,
      },
      data: {
        knowledgeBaseId: item.id,
      },
    });
  }

  try {
    const vectorStore = initVectorStore<PrismaKnowledgeBase>({
      tableName: "KnowledgeBase",
      vectorColumnName: "vector",
      columns: { id: ID_COLUMN, question: CONTENT_COLUMN },
      db,
      org,
    });
    const transactionId = nanoid();
    embedData({
      transactionId,
      featureType: "knowledge_base",
      vectorStore,
      data: [
        {
          pageContent: item.question,
          metadata: item,
        },
      ],
      logManager,
      userId,
      org,
      reqId: req.reqId,
    });
  } catch (e) {
    return NextResponse.json({
      message:
        "KB item successfully created, however, a problem occured when create embedding. Error: " +
        e,
      status: 500,
    });
  } finally {
    logger.info(
      logManager.eventLogsList,
      `ai:api:org:${org}:knowledge-base:createKnowledgeBaseHandler`,
    );
  }

  return NextResponse.json(item);
};
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(createKnowledgeBaseHandler),
  );
  return await handler(request, { params: resolvedParams });
}

/**
 * Remove knowledge base
 */
const removeKnowledgeBase = async (
  req: NextRequest,
  { params: { org } }: NextRouteHandlerContext,
) => {
  const reqBody = (await req.json()) as Pick<
    KnowledgeBase,
    "question" | "answer" | "questionnaire_id"
  >;
  const db = tenantGuardedPrisma(org);

  const kb = await db.knowledgeBase.findFirst({
    where: {
      organization_id: org,
      question: reqBody.question,
      answer: reqBody.answer,
    },
  });

  if (!kb) {
    return new NextResponse("Not Found", { status: 404 });
  }

  const userId = req.session.user.id;
  await db
    .$extends(withAuditLogger({ orgId: org, userId, message: "KB delete" }))
    .knowledgeBase.delete({ where: { id: kb.id } });

  return NextResponse.json({ message: "success" });
};
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(removeKnowledgeBase),
  );
  return await handler(request, { params: resolvedParams });
}

/**
 * Update knowledge base
 * @param req
 * @param param1
 * @returns
 */

// interface UpdateKbBody {
//   id: string;
//   userId: string;
//   orgId: string;
//   answer: string;
//   question: string;
//   category: string;
//   curator: string;
//   last_update_by: string;
// }

// const updateKnowledgeBaseHandler = async (
//   req: NextRequest,
//   { params: { org } }: { params: { org: string } }
// ) => {
//   const { id, orgId, question, answer }: UpdateKbBody = await req.json();
//   const userId = req.session.user.id;
//   //TODO
//   // check if the questionnaires need to update simultaneuously upon update
//   const item = await tenantGuardedPrisma(org).knowledgeBase.update({
//     where: {
//       id: id,
//       organization_id: orgId,
//     },
//     data: {
//       question: question,
//       answer: answer,
//       last_update_by_id: userId,
//     },
//   });

//   return NextResponse.json(item);
// };
// export const PUT = withErrorHandler(withSession(updateKnowledgeBaseHandler));
