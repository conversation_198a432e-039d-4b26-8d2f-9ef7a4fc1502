import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { getKbById } from "@/services/server/kb";
import { NextRequest, NextResponse } from "next/server";
import {
  guardedPrisma,
  otherDataAuditLog,
  withAuditLogger,
} from "@/lib/prisma";
import { ShortAnswer } from "@askinfosec/database";

const getKnowledeBaseHandler = async (
  req: NextRequest,
  { params }: NextRouteHandlerContext,
) => {
  const kb = await getKbById(params.org, req.session.user.id, params.id);
  return NextResponse.json(kb);
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(getKnowledeBaseHandler),
  );
  return await handler(request, { params: resolvedParams });
}

interface UpdateKbQnABody {
  answer: string;
  question: string;
  answer_yes_no_na: string;
}

const updateQnAKnowledgeBaseHandler = async (
  req: NextRequest,
  { params: { org, id } }: NextRouteHandlerContext,
) => {
  const { question, answer, answer_yes_no_na }: UpdateKbQnABody =
    await req.json();
  const userId = req.session.user.id;

  const client = guardedPrisma({ orgId: org });

  req.log.info({ question, answer, answer_yes_no_na }, "Update QnA");

  // TODO: If existing is null, we should probably throw an error now since we are updating a non-existing resource (NotFound) - Happy Path for now
  const item = await client
    .$extends(withAuditLogger({ orgId: org, userId, message: "KB QnA update" }))
    .$extends(otherDataAuditLog())
    .knowledgeBase.update({
      where: {
        id: id,
        organization_id: org,
      },
      data: {
        question: question,
        answer: answer,
        last_update_by_id: userId,
        answer_yes_no_na: answer_yes_no_na as ShortAnswer,
      },
    });

  return NextResponse.json(item);
};

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(updateQnAKnowledgeBaseHandler),
  );
  return await handler(request, { params: resolvedParams });
}

interface UpdateQnASettingsBody {
  categoryId: string;
  ownedById: string;
  nextVerification: string;
  scope_id: string;
}

const updateQnASettingsHandler = async (
  req: NextRequest,
  { params: { org, id } }: NextRouteHandlerContext,
) => {
  const userId = req.session.user.id;
  const {
    categoryId,
    ownedById,
    nextVerification,
    scope_id,
  }: UpdateQnASettingsBody = await req.json();
  // TODO: check if the questionnaires need to update simultaneuously upon update
  const client = guardedPrisma({ orgId: org });
  const item = await client
    .$extends(
      withAuditLogger({ orgId: org, userId, message: "KB Settings update" }),
    )
    .knowledgeBase.update({
      where: {
        id: id,
        organization_id: org,
      },
      data: {
        category_id: categoryId,
        owned_by_id: ownedById,
        last_update_by_id: userId,
        next_verification: nextVerification || null,
        scope_id: scope_id,
      },
    });

  return NextResponse.json(item);
};

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(updateQnASettingsHandler),
  );
  return await handler(request, { params: resolvedParams });
}
