import { type NextRequest, NextResponse } from "next/server";
import {
  NextRouteHandlerContext,
  withLoggingRouteHandler,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { KnowledgeBase, Prisma } from "@askinfosec/database";
import { auditLog, tenantGuardedPrisma, withAuditLogger } from "@/lib/prisma";
import {
  CONTENT_COLUMN,
  ID_COLUMN,
  initVectorStore,
} from "@/lib/langchain/vector-store";
import { LogLevel, LogManager } from "@/observability/migration-shim";
import { getUniversalLogger } from "@/observability/migration-shim";

const logger = getUniversalLogger();
import { embedData } from "@/lib/langchain/genai";
import { nanoid } from "nanoid";

/**
 * Train or Re-Train KB
 */
const trainKB = async (
  req: NextRequest,
  { params: { id, org } }: NextRouteHandlerContext,
) => {
  const db = tenantGuardedPrisma(org);
  const userId = req.session.user.id;
  const logManager = new LogManager(LogLevel.info, org, userId);

  const item = await db
    .$extends(withAuditLogger({ orgId: org, userId, message: "train KB" }))
    .knowledgeBase.findUnique({
      where: { id },
    });

  if (!item) {
    return NextResponse.json({ message: "KB not found." });
  }

  const vectorStore = initVectorStore<KnowledgeBase>({
    tableName: "KnowledgeBase",
    vectorColumnName: "vector",
    columns: { id: ID_COLUMN, question: CONTENT_COLUMN },
    db,
    org,
  });
  const transactionId = nanoid();
  embedData({
    transactionId,
    featureType: "knowledge_base",
    vectorStore,
    data: [
      {
        pageContent: item.question,
        metadata: item,
      },
    ],
    logManager,
    userId,
    org,
    reqId: req.reqId,
  });

  try {
    const log = {
      ref_id: id,
      entity: Prisma.ModelName.KnowledgeBase,
      event: "update",
      old_value: {},
      user_id: userId,
      organization_id: org,
      message: "Train KB - vector ommitted for this log.",
      data: [],
    };
    await auditLog(log);
  } catch (e) {
    console.error(
      "KB AI training successful but encountered error in audit log: ",
      e,
    );
  }

  logger.info(
    logManager.eventLogsList,
    `ai:api:org:${org}:knowledge-base:train:${id}trainKB`,
  );

  return NextResponse.json({ message: "success" });
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(withSessionRouteHandler()(trainKB));
  return await handler(request, { params: resolvedParams });
}
