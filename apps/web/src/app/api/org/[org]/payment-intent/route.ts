import { type NextRequest, NextResponse } from "next/server";
import { type Session } from "next-auth";
import {
  NextRouteHandler,
  withLoggingRouteHandler,
  withRbacRouteHandler,
} from "@/app/api/middlewares";
import {
  createPaymentIntent,
  retrieveCustomerId,
  createCustomer,
} from "@/services/server/stripe";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

const createPaymentIntentHandler: NextRouteHandler = async (
  req: NextRequest & { session: Session },
  { params }: { params: { org: string } },
) => {
  try {
    const { amount, currency = "usd", metadata = {} } = await req.json();

    if (!amount || amount <= 0) {
      return NextResponse.json({ error: "Invalid amount" }, { status: 400 });
    }

    // Get or create Stripe customer
    let customerId = await retrieveCustomerId({ orgId: params.org });

    if (!customerId) {
      // Get organization details
      const organization = await prisma.organization.findUnique({
        where: { id: params.org },
        select: { id: true, company_name: true },
      });

      if (!organization) {
        return NextResponse.json(
          { error: "Organization not found" },
          { status: 404 },
        );
      }

      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: req.session.user.id },
        select: { email: true },
      });

      if (!user || !user.email) {
        return NextResponse.json(
          { error: "User email not found" },
          { status: 400 },
        );
      }

      // Create customer
      const customer = await createCustomer({
        orgId: params.org,
        orgName: organization.company_name,
        email: user.email,
        userId: req.session.user.id,
      });

      customerId = customer.id;

      // Save the customer ID
      // First check if a subscription already exists for this organization
      const existingOrgSub = await prisma.organizationSubscription.findFirst({
        where: { organization_id: params.org },
      });

      if (existingOrgSub) {
        // Update existing subscription
      } else {
        // Create new subscription
        await prisma.organizationSubscription.create({
          data: {
            organization_id: params.org,
            start_date: new Date(),
          },
        });
      }
    }

    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      amount,
      currency,
      customerId,
      metadata: {
        ...metadata,
        orgId: params.org,
        userId: req.session.user.id,
      },
    });

    return NextResponse.json({
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      },
    });
  } catch (error) {
    logger.error({ error }, "Failed to create payment intent");
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 400 },
    );
  }
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ org: string }> },
) {
  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withRbacRouteHandler({ allowedRoles: ["owner"] })(
      createPaymentIntentHandler,
    ),
  );
  return await handler(request, { params: resolvedParams });
}
