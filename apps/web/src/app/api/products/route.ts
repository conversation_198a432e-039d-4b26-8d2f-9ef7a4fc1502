import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { Decimal } from "@askinfosec/database";

interface Module {
  id: string;
  name: string;
  display_name: string;
  description: string | null;
  is_core: boolean;
}

interface ProductModule {
  module: Module;
}

interface SubscriptionTier {
  id: string;
  name: string;
  price?: {
    id: string;
    stripe_price_id: string | null;
    amount: Decimal;
    currency: string;
    is_active: boolean;
  } | null;
}

interface Subscription {
  id: string;
  description: string;
  amount: Decimal | null;
  subscription_tiers: SubscriptionTier[];
}

interface Product {
  id: string;
  name: string;
  description: string;
  product_modules: ProductModule[];
  subscriptions: Subscription[];
}

export async function GET() {
  try {
    const products = await prisma.product.findMany({
      where: { is_active: true },
      orderBy: { display_order: "asc" },
      include: {
        product_modules: {
          include: {
            module: true,
          },
        },
        subscriptions: {
          include: {
            subscription_tiers: {
              include: {
                price: true,
              },
            },
          },
        },
      },
    });

    // Format the data for the frontend
    const formattedProducts = products.map((product: Product) => {
      // Find subscription tiers with prices
      const subscriptionTiers = product.subscriptions.flatMap((sub) =>
        sub.subscription_tiers.map((tier) => ({
          id: tier.id,
          name: tier.name,
          subscriptionId: sub.id,
          price: tier.price
            ? {
                id: tier.price.id,
                stripeId: tier.price.stripe_price_id,
                amount: Number(tier.price.amount),
                currency: tier.price.currency,
                isActive: tier.price.is_active,
              }
            : null,
        })),
      );

      return {
        id: product.id,
        name: product.name,
        description: product.description,
        modules: product.product_modules
          .filter((pm: ProductModule) => !pm.module.is_core)
          .map((pm: ProductModule) => pm.module),
        subscriptionTiers: subscriptionTiers,
        // For backward compatibility, provide the first price if available
        price:
          subscriptionTiers.length > 0 && subscriptionTiers[0].price
            ? subscriptionTiers[0].price
            : null,
      };
    });

    return NextResponse.json(formattedProducts);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 },
    );
  }
}
