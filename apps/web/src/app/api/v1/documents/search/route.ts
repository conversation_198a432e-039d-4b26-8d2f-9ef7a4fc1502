import { NextResponse } from "next/server";
import {
  withDirectOrgA<PERSON><PERSON>eyAuth,
  withApiRateLimiting,
  withApiLogging,
  withSecurityHeaders,
  withPublicCors,
  type NextRouteHandler,
} from "@/app/api/public-api-middleware";
import { tenantGuardedPrisma } from "@/lib/prisma";
import {
  initVectorStore,
  ID_COLUMN,
  CONTENT_COLUMN,
} from "@/lib/langchain/vector-store";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";
import { nanoid } from "nanoid";
import { z } from "zod";
import { documentSearchCache } from "@/lib/cache";
import { measurePerformance, createPerformanceTracker } from "@/lib/telemetry";

// Type definitions for clarity
type SnippetMap = Map<string, Array<{ pageContent: string; score: number }>>;
type SearchResult = {
  fileIds: string[];
  snippets: SnippetMap;
};
type ProcessedDocument = {
  id: string;
  name: string;
  documentType: string | null;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string | null;
  accessLevel: string | null;
  fileType: string | null;
  relevanceScore?: number;
  matchingSnippets?: Array<{ content: string; score: number }>;
  content?: string;
  contentType?: string;
  contentEncoding?: string | null;
};

// Validation schema for search request
const searchSchema = z.object({
  query: z.string().min(1, "Query is required"),
  topK: z.number().int().min(1).max(20).optional().default(5),
  documentTypes: z.array(z.string()).optional(),
  includeContent: z.boolean().optional().default(false),
  searchType: z.enum(["vector", "text"]).optional().default("text"),
  page: z.number().int().min(1).optional().default(1),
  pageSize: z.number().int().min(1).max(50).optional().default(10),
  cursor: z.string().optional(),
  enableCache: z.boolean().optional().default(true),
});

// Define the cursor structure
type PaginationCursor = {
  lastId: string;
  page: number;
  searchType: string;
  query: string;
};

/**
 * Encode a pagination cursor to a base64 string
 */
function encodeCursor(cursor: PaginationCursor): string {
  return Buffer.from(JSON.stringify(cursor)).toString("base64");
}

/**
 * Decode a base64 cursor string to a pagination cursor object
 */
function decodeCursor(cursorStr: string): PaginationCursor | null {
  try {
    return JSON.parse(Buffer.from(cursorStr, "base64").toString());
  } catch (error) {
    logger.warn({ message: "Failed to decode cursor", error });
    return null;
  }
}

/**
 * Perform vector-based search using embeddings
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param query Search query
 * @param topK Maximum number of results to return
 * @returns Search results with file IDs and snippets
 */
async function performVectorSearch(
  db: PrismaClient,
  orgId: string,
  query: string,
  topK: number,
): Promise<SearchResult> {
  // Initialize vector store
  const vectorStore = initVectorStore<any>({
    tableName: "DocumentVector",
    vectorColumnName: "vector",
    columns: {
      id: ID_COLUMN,
      content: CONTENT_COLUMN,
      file_id: Symbol("file_id"),
    },
    db,
    org: orgId,
  });

  // Use similaritySearchVectorWithScore to get scores
  const searchResultsWithScores =
    await vectorStore.similaritySearchVectorWithScore(
      await vectorStore.embeddings.embedQuery(query),
      topK,
    );

  if (!searchResultsWithScores || searchResultsWithScores.length === 0) {
    return { fileIds: [], snippets: new Map() };
  }

  // Process vector search results
  const snippets: SnippetMap = new Map();
  for (const [doc, distance] of searchResultsWithScores) {
    const fileId = doc.metadata.file_id as string;
    // Convert distance to a relevance score (the lower the distance, the higher the score)
    const score = 1 / (1 + (distance ?? 0));
    if (!snippets.has(fileId)) snippets.set(fileId, []);
    snippets.get(fileId)?.push({ pageContent: doc.pageContent, score });
  }

  const fileIds = Array.from(snippets.keys());
  return { fileIds, snippets };
}

/**
 * Search document vectors using text search
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param query Search query
 * @param topK Maximum number of results to return
 * @returns Map of file IDs to snippets
 */
async function searchDocumentVectors(
  db: PrismaClient,
  orgId: string,
  query: string,
  topK: number,
): Promise<SnippetMap> {
  const textSearchQuery = query.split(" ").join(" & "); // Convert to PostgreSQL full-text search format
  const documentVectors = await db.documentVector.findMany({
    where: {
      organization_id: orgId,
      content: {
        search: textSearchQuery,
      },
    },
    take: topK * 2, // Get more results to account for potential duplicates
  });

  // Process text search results
  const snippets: SnippetMap = new Map();
  for (const doc of documentVectors) {
    const fileId = doc.file_id;
    // For text search, we use a simpler relevance model (all matches get a score of 0.8)
    const score = 0.8;
    if (!snippets.has(fileId)) snippets.set(fileId, []);
    snippets.get(fileId)?.push({ pageContent: doc.content, score });
  }

  return snippets;
}

/**
 * Search files directly using text search
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param query Search query
 * @param topK Maximum number of results to return
 * @param existingFileIds IDs of files that have already been found
 * @returns Array of files that match the search criteria
 */
async function searchFiles(
  db: PrismaClient,
  orgId: string,
  query: string,
  topK: number,
  existingFileIds: string[],
): Promise<any[]> {
  const textSearchQuery = query.split(" ").join(" & ");

  // Get files with direct content or name matches
  const files = await db.file.findMany({
    where: {
      organization_id: orgId,
      OR: [
        { name: { contains: query, mode: "insensitive" } },
        { content: { search: textSearchQuery } },
      ],
    },
    select: {
      id: true,
      name: true,
      content: true,
    },
    take: topK - existingFileIds.length,
  });

  return files;
}

/**
 * Search buffer files for text-based file types
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param query Search query
 * @param topK Maximum number of results to return
 * @param existingFileIds IDs of files that have already been found
 * @returns Array of files that match the search criteria
 */
async function searchBufferFiles(
  db: PrismaClient,
  orgId: string,
  query: string,
  topK: number,
  existingFileIds: string[],
): Promise<any[]> {
  const textFileTypes = [
    "txt",
    "md",
    "html",
    "json",
    "csv",
    "xml",
    "pdf",
    "docx",
  ];
  const bufferFiles = await db.file.findMany({
    where: {
      organization_id: orgId,
      buffer_file: { not: null },
      file_type: { in: textFileTypes },
      id: { notIn: existingFileIds }, // Exclude already found files
    },
    select: {
      id: true,
      name: true,
      buffer_file: true,
      file_type: true,
    },
    take: topK - existingFileIds.length,
  });

  const matchingFiles = [];

  // Process buffer files and check if they contain the search terms
  for (const file of bufferFiles) {
    try {
      // TODO: For binary files such as PDF, we need to use a proper text extraction

      // Convert buffer to string - this works for text-based files
      if (file.buffer_file) {
        const content = file.buffer_file.toString();

        // Simple check if content contains any of the search terms
        const searchTerms = query.toLowerCase().split(" ");
        const contentLower = content.toLowerCase();

        if (searchTerms.some((term: string) => contentLower.includes(term))) {
          // Add to results if it contains any search term
          matchingFiles.push({
            id: file.id,
            name: file.name,
            content: content.substring(0, 500), // Just use a preview for matching
          });
        }
      }
    } catch (error) {
      // Skip files that can't be converted to text
      console.error(`Error processing buffer file ${file.id}:`, error);
    }
  }

  return matchingFiles;
}

/**
 * Perform text-based search using PostgreSQL full-text search
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param query Search query
 * @param topK Maximum number of results to return
 * @returns Search results with file IDs and snippets
 */
async function performTextSearch(
  db: PrismaClient,
  orgId: string,
  query: string,
  topK: number,
): Promise<SearchResult> {
  // First, search document vectors
  const snippets = await searchDocumentVectors(db, orgId, query, topK);
  let fileIds = Array.from(snippets.keys());

  // If we need more results, search directly in files
  if (fileIds.length < topK) {
    const files = await searchFiles(db, orgId, query, topK, fileIds);

    // If we still need more results, search in buffer files
    if (files.length + fileIds.length < topK) {
      const allFoundFileIds = [...fileIds, ...files.map((f) => f.id)];
      const bufferFiles = await searchBufferFiles(
        db,
        orgId,
        query,
        topK,
        allFoundFileIds,
      );

      // Add buffer file results to files array
      files.push(...bufferFiles);
    }

    // Add file results to snippets map
    for (const file of files) {
      if (!snippets.has(file.id)) {
        // For direct file matches, we use a lower score (0.6)
        const score = 0.6;
        const content = file.content || file.name;
        snippets.set(file.id, [{ pageContent: content, score }]);
      }
    }

    // Update fileIds
    fileIds = Array.from(snippets.keys());

    // Limit to topK unique files
    if (fileIds.length > topK) {
      fileIds = fileIds.slice(0, topK);
    }
  }

  return { fileIds, snippets };
}

/**
 * Process document results and add relevance scores and content
 * @param documents Array of documents from the database
 * @param snippets Map of file IDs to snippets
 * @param includeContent Whether to include content in the response
 * @returns Array of processed documents
 */
function processDocuments(
  documents: any[],
  snippets: SnippetMap,
  includeContent: boolean,
): ProcessedDocument[] {
  return documents.map((doc) => {
    const result: ProcessedDocument = {
      id: doc.id,
      name: doc.name,
      documentType: doc.document_type,
      createdAt: doc.created_at,
      updatedAt: doc.updated_at,
      categoryId: doc.category_id,
      accessLevel: doc.access_level,
      fileType: doc.file_type,
    };

    // Use the highest score for this document as relevanceScore
    const docSnippets = snippets.get(String(doc.id)) || [];
    if (docSnippets.length > 0) {
      const maxScore = Math.max(...docSnippets.map((s) => s.score));
      result.relevanceScore = maxScore;
      result.matchingSnippets = docSnippets.map((s) => ({
        content: s.pageContent,
        score: s.score,
      }));
    }

    // Add content if requested
    if (includeContent) {
      let contentType = "text/plain";
      let contentEncoding = null;

      if (doc.content) {
        result.content = doc.content;
      } else if (doc.buffer_file) {
        // Check if this is likely a binary file based on file_type
        const fileType = doc.file_type
          ? String(doc.file_type).toLowerCase()
          : "";
        const isBinaryFile =
          fileType &&
          !["txt", "md", "html", "json", "csv", "xml"].includes(fileType);

        if (isBinaryFile) {
          // For binary files, encode as base64
          result.content = doc.buffer_file.toString();
          contentEncoding = "base64";

          // Set appropriate content type based on file_type
          if (fileType) {
            const fileTypeToContentType: Record<string, string> = {
              pdf: "application/pdf",
              docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
              pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
              png: "image/png",
              jpg: "image/jpeg",
              jpeg: "image/jpeg",
            };
            contentType =
              fileTypeToContentType[fileType] || "application/octet-stream";
          } else {
            contentType = "application/octet-stream";
          }
        } else {
          // For text files, convert to string
          result.content = doc.buffer_file.toString();
        }
      }

      // Add content metadata
      result.contentType = contentType;
      result.contentEncoding = contentEncoding;
    }

    // Always include file type
    result.fileType = doc.file_type;

    return result;
  });
}

/**
 * Main handler for document search
 */
const semanticSearchHandler: NextRouteHandler = async (req, _ctx) => {
  // Create a performance tracker for the entire request
  const requestTracker = createPerformanceTracker("document_search_request");

  const { orgId } = req;

  if (!orgId) {
    return NextResponse.json(
      { error: "Organization not found" },
      { status: 404 },
    );
  }

  const reqBody = await req.json();
  const {
    query,
    topK,
    documentTypes,
    includeContent,
    searchType,
    page = 1,
    pageSize = 10,
    cursor: cursorStr,
    enableCache = true,
  } = reqBody;

  // Validate request body
  try {
    searchSchema.parse(reqBody);
  } catch (error) {
    return NextResponse.json(
      { error: "Invalid request body" },
      { status: 400 },
    );
  }

  // Generate a transaction ID for tracking this request
  const transactionId = nanoid();

  // Parse cursor if provided
  let cursor: PaginationCursor | null = null;
  if (cursorStr) {
    cursor = decodeCursor(cursorStr);
    // Validate cursor
    if (!cursor || cursor.query !== query || cursor.searchType !== searchType) {
      return NextResponse.json({ error: "Invalid cursor" }, { status: 400 });
    }
  }

  // Generate cache key
  const cacheKey = JSON.stringify({
    orgId,
    query,
    topK,
    documentTypes,
    searchType,
    cursor: cursorStr,
    page,
    pageSize,
  });

  try {
    // Try to get results from cache if enabled
    if (enableCache) {
      const cachedResult = documentSearchCache.get(cacheKey);
      if (cachedResult) {
        logger.info({
          message: `Cache hit for document search`,
          query,
          searchType,
          orgId,
          transactionId,
          fromCache: true,
        });
        requestTracker.end({ fromCache: true });
        return NextResponse.json(cachedResult);
      }
    }

    const db = tenantGuardedPrisma(orgId);
    let searchResult: SearchResult;

    // Perform search based on searchType
    const searchTracker = createPerformanceTracker("search_operation", {
      searchType,
    });
    if (searchType === "vector") {
      searchResult = await performVectorSearch(db, orgId, query, topK);

      // Return empty results if nothing found
      if (searchResult.fileIds.length === 0) {
        const emptyResult = {
          documents: [],
          page,
          pageSize,
          totalCount: 0,
          hasMore: false,
          cursor: null,
        };
        searchTracker.end({ resultCount: 0 });
        return NextResponse.json(emptyResult);
      }
    } else {
      searchResult = await performTextSearch(db, orgId, query, topK);
    }
    searchTracker.end({ resultCount: searchResult.fileIds.length });

    // Build query to get file details
    const whereConditions: any = {
      id: { in: searchResult.fileIds },
      organization_id: orgId,
    };

    if (documentTypes && documentTypes.length > 0) {
      whereConditions.document_type = { in: documentTypes };
    }

    // Handle cursor-based pagination if cursor is provided
    if (cursor?.lastId) {
      whereConditions.id = {
        ...whereConditions.id,
        gt: cursor.lastId, // Get records after the cursor's last ID
      };
    }

    // Select fields based on whether content is requested
    const selectFields: any = {
      id: true,
      name: true,
      document_type: true,
      created_at: true,
      updated_at: true,
      category_id: true,
      access_level: true,
      file_type: true, // Always include file_type for content type determination
    };

    if (includeContent) {
      selectFields.content = true;
      selectFields.buffer_file = true;
    }

    // Count total matching documents for accurate pagination
    const countTracker = createPerformanceTracker("count_documents");
    const totalCount = await db.file.count({
      where: whereConditions,
    });
    countTracker.end({ totalCount });

    // Variables to hold the final paged documents and hasMore flag
    let pagedDocuments: ProcessedDocument[];
    let hasMore: boolean;
    let nextCursor: string | null = null;

    // Fetch documents with performance tracking
    const fetchTracker = createPerformanceTracker("fetch_documents", {
      searchType,
      cursorBased: !!cursor,
      totalCount,
    });

    // For vector search, we need to sort by relevance score after fetching
    // For text search with cursor, we can apply pagination directly at the database level
    if (
      (searchType === "text" && searchResult.fileIds.length > pageSize) ||
      cursor
    ) {
      // Get only the files needed for the current page with cursor-based pagination
      const documents = await db.file.findMany({
        where: whereConditions,
        select: selectFields,
        take: pageSize + 1, // Fetch one extra to determine if there are more results
        orderBy: { id: "asc" }, // Consistent ordering for cursor-based pagination
      });

      // Check if we have more results
      hasMore = documents.length > pageSize;

      // Remove the extra item if we fetched more than pageSize
      const documentsForPage = hasMore
        ? documents.slice(0, pageSize)
        : documents;

      // Process the page of results
      let processedDocuments = processDocuments(
        documentsForPage,
        searchResult.snippets,
        includeContent,
      );

      // Sort by relevance score
      processedDocuments.sort(
        (a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0),
      );

      // Create next cursor if there are more results
      if (hasMore && documentsForPage.length > 0) {
        const lastDocument = documentsForPage[documentsForPage.length - 1];
        const newCursor: PaginationCursor = {
          lastId: String(lastDocument.id),
          page: cursor ? cursor.page + 1 : page + 1,
          searchType,
          query,
        };
        nextCursor = encodeCursor(newCursor);
      }

      pagedDocuments = processedDocuments;
    } else {
      // For vector search or small result sets, we need all documents to sort by relevance
      const documents = await db.file.findMany({
        where: whereConditions,
        select: selectFields,
      });

      // Process all results
      let processedDocuments = processDocuments(
        documents,
        searchResult.snippets,
        includeContent,
      );

      // Sort by relevance score
      processedDocuments.sort(
        (a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0),
      );

      // Apply pagination after sorting by relevance
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      pagedDocuments = processedDocuments.slice(start, end);
      hasMore = end < totalCount;

      // Create next cursor if there are more results
      if (hasMore && pagedDocuments.length > 0) {
        const lastDocument = pagedDocuments[pagedDocuments.length - 1];
        const newCursor: PaginationCursor = {
          lastId: String(lastDocument.id),
          page: page + 1,
          searchType,
          query,
        };
        nextCursor = encodeCursor(newCursor);
      }
    }
    fetchTracker.end({ resultCount: pagedDocuments.length });

    // Prepare the response object
    const response = {
      documents: pagedDocuments,
      page: cursor ? cursor.page : page,
      pageSize,
      totalCount,
      hasMore,
      cursor: nextCursor,
    };

    // Cache the results if caching is enabled
    if (enableCache) {
      documentSearchCache.set(cacheKey, response);
    }

    // Log search
    logger.info({
      message: `Document search via API (${searchType})`,
      query,
      searchType,
      resultCount: pagedDocuments.length,
      orgId,
      transactionId,
      page: cursor ? cursor.page : page,
      pageSize,
      totalCount,
      hasMore,
      cursorBased: !!cursor,
      cached: false,
    });

    // End the request tracker
    requestTracker.end({
      resultCount: pagedDocuments.length,
      totalCount,
      hasMore,
      cursorBased: !!cursor,
    });

    return NextResponse.json(response);
  } catch (error) {
    logger.error({
      message: `Error performing document search (${searchType})`,
      error,
      query,
      searchType,
      orgId,
      transactionId,
    });
    return NextResponse.json(
      { error: "Failed to perform document search" },
      { status: 500 },
    );
  }
};

// Export POST handler with middleware
export const POST = withPublicCors(
  withSecurityHeaders(
    withApiLogging(
      withDirectOrgApiKeyAuth(
        withApiRateLimiting({ limit: 10, windowMs: 60_000 })(
          semanticSearchHandler,
        ),
      ),
    ),
  ),
);
