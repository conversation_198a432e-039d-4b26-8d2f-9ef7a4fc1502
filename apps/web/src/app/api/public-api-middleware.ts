import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror, ZodType } from "zod";
import { nanoid } from "nanoid";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import createLogger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

// Validate critical environment variables
if (process.env.NODE_ENV === "production") {
  if (!process.env.MASTER_API_KEY) {
    console.warn(
      "WARNING: MASTER_API_KEY not set. Master API access will be unavailable.",
    );
  }
}

type NextRouteHandler<T = any> = (
  request: NextRequest & { orgId?: string; validatedBody?: any; log?: any },
  context: { params: T },
) => Promise<NextResponse>;

const logger = createLogger;

// ----- Rate Limiting Implementation -----

interface RateLimitRecord {
  count: number;
  resetAt: number;
}

// In-memory storage for rate limiting (note: this will reset on server restart)
const rateLimitStore: Map<string, RateLimitRecord> = new Map();

function getRateLimitKey(req: NextRequest, prefix: string = "global"): string {
  const ip = req.headers.get("x-forwarded-for") || "unknown";
  const endpoint = req.nextUrl.pathname;
  const orgId = (req as any).orgId || "anonymous";

  return `${prefix}:${orgId}:${endpoint}:${ip}`;
}

function isRateLimited(key: string, limit: number, windowMs: number): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record) {
    // First request
    rateLimitStore.set(key, {
      count: 1,
      resetAt: now + windowMs,
    });
    return false;
  }

  if (now > record.resetAt) {
    // Window has reset
    rateLimitStore.set(key, {
      count: 1,
      resetAt: now + windowMs,
    });
    return false;
  }

  // Increment and check
  record.count += 1;
  rateLimitStore.set(key, record);

  return record.count > limit;
}

// ----- Error Response Helpers -----

interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    requestId?: string;
  };
}

function createErrorResponse(
  status: number,
  code: string,
  message: string,
  details?: any,
  requestId?: string,
): NextResponse<ApiErrorResponse> {
  return NextResponse.json(
    {
      error: {
        code,
        message,
        ...(details && { details }),
        ...(requestId && { requestId }),
      },
    },
    { status },
  );
}

// ----- Middleware Functions -----

/**
 * Middleware for logging API requests and responses
 */
function withApiLogging(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    const requestId = nanoid();
    const startTime = Date.now();

    // Create child logger with request context
    const childLogger = logger.child({
      requestId,
      endpoint: req.nextUrl.pathname,
      method: req.method,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent"),
      origin: req.headers.get("origin"),
    });

    // Attach logger to request
    req.log = childLogger;

    try {
      // Log incoming request
      childLogger.info("API request received");

      // Process request
      const response = await handler(req, ctx);

      // Log response
      const duration = Date.now() - startTime;
      childLogger.info({
        status: response.status,
        duration,
        msg: "API request completed",
      });

      // Add request ID to response headers
      response.headers.set("x-request-id", requestId);

      return response;
    } catch (error: any) {
      // Log error
      const duration = Date.now() - startTime;
      childLogger.error({
        error: error.message || "Unknown error",
        stack: error.stack,
        duration,
        msg: "API request failed",
      });

      // Return appropriate error response
      if (error instanceof ZodError) {
        return createErrorResponse(
          400,
          "validation_error",
          "Invalid request data",
          error.format(),
          requestId,
        );
      }

      // Return generic error to avoid leaking implementation details
      return createErrorResponse(
        500,
        "internal_server_error",
        "An unexpected error occurred",
        undefined,
        requestId,
      );
    }
  };
}

/**
 * Middleware for rate limiting API requests
 */
function withApiRateLimiting(opts: { limit: number; windowMs: number }) {
  return function (handler: NextRouteHandler): NextRouteHandler {
    return async (req, ctx) => {
      const { limit, windowMs } = opts;
      const key = getRateLimitKey(req);

      if (isRateLimited(key, limit, windowMs)) {
        // Calculate remaining time until rate limit resets
        const record = rateLimitStore.get(key);
        const resetIn = record
          ? Math.ceil((record.resetAt - Date.now()) / 1000)
          : 60;

        req.log?.warn({
          key,
          limit,
          msg: "Rate limit exceeded",
        });

        return createErrorResponse(
          429,
          "rate_limit_exceeded",
          "Too many requests, please try again later",
          undefined,
          nanoid(),
        );
      }

      return handler(req, ctx);
    };
  };
}

/**
 * Middleware to validate request body against a Zod schema
 */
function withRequestValidation<T extends ZodType>(schema: T) {
  return function (handler: NextRouteHandler): NextRouteHandler {
    return async (req, ctx) => {
      try {
        let body;

        try {
          body = await req.json();
        } catch (error) {
          return createErrorResponse(
            400,
            "invalid_json",
            "Invalid JSON payload",
            undefined,
            nanoid(),
          );
        }

        // Validate request body
        const validatedData = schema.parse(body);

        // Attach validated data to request
        req.validatedBody = validatedData;

        return handler(req, ctx);
      } catch (error) {
        if (error instanceof ZodError) {
          req.log?.info({
            validationErrors: error.format(),
            msg: "Request validation failed",
          });

          return createErrorResponse(
            400,
            "validation_error",
            "Invalid request data",
            error.format(),
            nanoid(),
          );
        }

        throw error; // Let the logging middleware handle other errors
      }
    };
  };
}

/**
 * Middleware for resolving the organization from host name and validating API key
 */
function withApiKeyAuth(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    const providedApiKey = req.headers.get("x-api-key");
    const hostname = req.headers.get("x-registered-domain");

    if (!hostname) {
      return createErrorResponse(
        401,
        "invalid_domain",
        "Invalid domain",
        undefined,
        nanoid(),
      );
    }

    const orgApiKey = process.env.MASTER_API_KEY; // TODO: For better security, we should use a different API key for each organization

    if (!providedApiKey || providedApiKey !== orgApiKey) {
      return createErrorResponse(
        401,
        "invalid_api_key",
        "Invalid API key",
        undefined,
        nanoid(),
      );
    }

    // Special case - Internal API key for our main domain
    if (hostname === "trust.askinfosec.tech") {
      return handler(req, ctx);
    }

    // Extract organization from hostname
    const orgId = await getOrgFromHostname(hostname);

    if (!orgId) {
      return createErrorResponse(
        401,
        "invalid_domain",
        "Invalid domain or organization not found",
        undefined,
        nanoid(),
      );
    }

    // Attach organization ID to request for downstream handlers
    req.orgId = orgId;

    return handler(req, ctx);
  };
}

/**
 * Middleware for resolving the organization from host name and validating API key
 */
function withOrgApiKeyAuth(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    const providedMasterApiKey = req.headers.get("x-master-api-key");
    const orgApiKey = req.headers.get("x-api-key");
    const hostname = req.headers.get("host") || "";
    const masterApiKeyFromEnv = process.env.MASTER_API_KEY;

    // Special case - Internal API key for our main domain
    if (hostname === "trust.askinfosec.tech") {
      if (
        !providedMasterApiKey ||
        providedMasterApiKey !== masterApiKeyFromEnv
      ) {
        return createErrorResponse(
          401,
          "invalid_master_api_key",
          "Invalid master API key",
          undefined,
          nanoid(),
        );
      }
      return handler(req, ctx);
    }

    // Standard API key validation for organization domains
    if (!orgApiKey) {
      return createErrorResponse(
        401,
        "api_key_required",
        "API key is required",
        undefined,
        nanoid(),
      );
    }

    if (!providedMasterApiKey || providedMasterApiKey !== masterApiKeyFromEnv) {
      return createErrorResponse(
        401,
        "master_api_key_required",
        "Master API key is required",
        undefined,
        nanoid(),
      );
    }

    // Extract organization from hostname
    const orgId = await getOrgFromHostname(hostname);

    if (!orgId) {
      return createErrorResponse(
        401,
        "invalid_domain",
        "Invalid domain or organization not found",
        undefined,
        nanoid(),
      );
    }

    // Validate API key
    const isValid = await validateApiKey(orgApiKey, orgId);

    if (!isValid) {
      return createErrorResponse(
        401,
        "invalid_api_key",
        "Invalid API key",
        undefined,
        nanoid(),
      );
    }

    // Attach organization ID to request for downstream handlers
    req.orgId = orgId;

    return handler(req, ctx);
  };
}

/**
 * Middleware for validating API keys using x-org-id and x-api-key headers
 * This middleware expects and requires x-master-api-key, x-org-id, and x-api-key in the headers
 */
function withDirectOrgApiKeyAuth(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    const providedMasterApiKey = req.headers.get("x-master-api-key");
    const providedOrgId = req.headers.get("x-org-id");
    const providedApiKey = req.headers.get("x-api-key");
    const masterApiKeyFromEnv = process.env.MASTER_API_KEY;

    // Validate master API key
    if (!providedMasterApiKey || providedMasterApiKey !== masterApiKeyFromEnv) {
      return createErrorResponse(
        401,
        "invalid_master_api_key",
        "Invalid master API key",
        undefined,
        nanoid(),
      );
    }

    // Validate organization ID
    if (!providedOrgId) {
      return createErrorResponse(
        401,
        "org_id_required",
        "Organization ID is required",
        undefined,
        nanoid(),
      );
    }

    // Validate API key
    if (!providedApiKey) {
      return createErrorResponse(
        401,
        "api_key_required",
        "API key is required",
        undefined,
        nanoid(),
      );
    }

    // Verify the organization exists
    try {
      const organization = await prisma.organization.findUnique({
        where: {
          id: providedOrgId,
          deleted_at: null,
        },
        select: {
          id: true,
          openai_settings: true,
        },
      });

      if (!organization) {
        return createErrorResponse(
          401,
          "invalid_org_id",
          "Organization not found",
          undefined,
          nanoid(),
        );
      }

      // Validate API key from openai_settings
      const openaiSettings = organization.openai_settings as any;

      // First check org_api_key (new field), then fall back to app_id (old field)
      if (
        !openaiSettings ||
        ((!openaiSettings.org_api_key ||
          openaiSettings.org_api_key !== providedApiKey) &&
          (!openaiSettings.app_id || openaiSettings.app_id !== providedApiKey))
      ) {
        return createErrorResponse(
          401,
          "invalid_api_key",
          "Invalid API key",
          undefined,
          nanoid(),
        );
      }

      // Attach organization ID to request for downstream handlers
      req.orgId = providedOrgId;

      return handler(req, ctx);
    } catch (error) {
      console.error("Error validating organization and API key:", error);
      return createErrorResponse(
        500,
        "internal_server_error",
        "An error occurred while validating credentials",
        undefined,
        nanoid(),
      );
    }
  };
}

/**
 * Middleware for adding security headers to responses
 */
function withSecurityHeaders(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    const response = await handler(req, ctx);

    // Add security headers
    response.headers.set("Content-Security-Policy", "default-src 'self'");
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-XSS-Protection", "1; mode=block");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
    response.headers.set("Cache-Control", "no-store, max-age=0");

    return response;
  };
}

/**
 * Middleware for handling CORS for public API
 */
function withPublicCors(handler: NextRouteHandler): NextRouteHandler {
  return async (req, ctx) => {
    // Handle preflight requests
    if (req.method === "OPTIONS") {
      return new NextResponse(null, {
        status: 204,
        headers: {
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers":
            "Content-Type, x-api-key, x-master-api-key, x-org-id",
          "Access-Control-Max-Age": "86400", // 24 hours
        },
      });
    }

    // Process the actual request
    const response = await handler(req, ctx);

    // Get origin
    const origin = req.headers.get("origin");

    // Add CORS headers to allow the origin that made the request
    if (origin) {
      response.headers.set("Access-Control-Allow-Origin", origin);
      response.headers.set("Vary", "Origin");
    } else {
      response.headers.set("Access-Control-Allow-Origin", "*");
    }

    response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, x-api-key, x-master-api-key, x-org-id",
    );

    return response;
  };
}

// ----- Helper Functions -----

/**
 * Get organization ID from hostname
 */
async function getOrgFromHostname(hostname: string): Promise<string | null> {
  console.info("getOrgFromHostname():hostname->", hostname);
  try {
    const fullDomain = hostname.toLowerCase();

    // Special case - Our main domain without subdomain
    if (fullDomain === "trust.askinfosec.tech") {
      // For our main domain, we can either:
      // 1. Use a specific organization ID for internal/demo purposes
      // 2. Reject direct access to our main domain

      // Option 1: Return a specific organization ID (e.g., for a demo organization)
      const askinfosecOrgId = "askinfosec";
      if (askinfosecOrgId) {
        return askinfosecOrgId;
      }

      // Option 2: If no demo org configured, reject direct access to main domain
      return null;
    }

    // Handle subdomain pattern (company1.trust.askinfosec.tech)
    if (fullDomain.endsWith(".trust.askinfosec.tech")) {
      const subdomain = fullDomain.split(".")[0];

      // Check for organization with this subdomain
      const settings = await prisma.trustCenterSettings.findFirst({
        where: {
          domain: fullDomain,
          is_domain_verified: true,
        },
        select: {
          organization_id: true,
        },
      });

      if (settings) return settings.organization_id;
    }

    // If no exact match, try matching by removing common prefixes
    // Many companies might use 'trust' or 'security' as subdomain
    if (fullDomain.startsWith("trust.") || fullDomain.startsWith("security.")) {
      const baseDomain = fullDomain.split(".", 2)[1]; // Get the part after the first dot

      const settings = await prisma.trustCenterSettings.findFirst({
        where: {
          domain: baseDomain,
          is_custom_domain: true,
          is_domain_verified: true,
        },
        select: {
          organization_id: true,
        },
      });

      if (settings) return settings.organization_id;
    }

    // handle localhost
    if (fullDomain === "localhost:3000" || fullDomain === "localhost:3001") {
      const settings = await prisma.trustCenterSettings.findFirst({
        where: {
          domain: fullDomain,
          is_domain_verified: true,
        },
        select: {
          organization_id: true,
        },
      });

      if (settings) return settings.organization_id;
    }

    // Last resort, just try to match the hostname
    const settings = await prisma.trustCenterSettings.findFirst({
      where: {
        domain: fullDomain,
        is_domain_verified: true,
      },
      select: {
        organization_id: true,
      },
    });

    if (settings) return settings.organization_id;

    // If we didn't find any matching org, return null
    console.log("We didn't find any matching org.");
    return null;
  } catch (error) {
    console.error("Error resolving organization from hostname:", error);
    return null;
  }
}

/**
 * Validate API key for an organization
 */
async function validateApiKey(apiKey: string, orgId: string): Promise<boolean> {
  try {
    const settings = await prisma.trustCenterSettings.findFirst({
      where: {
        organization_id: orgId,
        api_key: apiKey,
      },
    });

    return !!settings;
  } catch (error) {
    console.error("Error validating API key:", error);
    return false;
  }
}

/**
 * Validate API key for an organization
 */
async function validateMasterApiKey(providedApiKey: string): Promise<boolean> {
  try {
    const match = providedApiKey === process.env.MASTER_API_KEY;

    return !!match;
  } catch (error) {
    console.error("Error validating master API key:", error);
    return false;
  }
}

export {
  withApiLogging,
  withApiRateLimiting,
  withRequestValidation,
  withOrgApiKeyAuth,
  withDirectOrgApiKeyAuth,
  withSecurityHeaders,
  withPublicCors,
  withApiKeyAuth,
  type NextRouteHandler,
};
