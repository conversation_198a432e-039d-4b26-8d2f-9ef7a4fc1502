import { NextRequest, NextResponse } from "next/server";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import logger from "@/lib/logger-new";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export async function GET(req: NextRequest): Promise<Response> {
  try {
    const url = new URL(req.url);
    const orgId = url.searchParams.get("orgId");
    const tierId = url.searchParams.get("tierId");
    const stripeSubId = url.searchParams.get("stripeSubId");

    const query: any = {};

    if (orgId) {
      query.organization_id = orgId;
    }

    if (tierId) {
      query.subscription_tier_id = tierId;
    }

    if (stripeSubId) {
      query.stripe_sub_id = stripeSubId;
    }

    // Get all subscriptions matching the query
    const subscriptions = await prisma.organizationSubscription.findMany({
      where: query,
      orderBy: {
        created_at: "desc",
      },
    });

    // Get all subscription tiers
    const tiers = await prisma.subscriptionTier.findMany({
      include: {
        subscription: true,
        price: true,
      },
    });

    return NextResponse.json({
      success: true,
      subscriptions,
      tiers,
      query,
    });
  } catch (error) {
    logger.error({ error }, "Failed to get subscription debug info");
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
