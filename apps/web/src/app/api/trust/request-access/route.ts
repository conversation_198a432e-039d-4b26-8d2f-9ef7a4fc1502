import { NextResponse } from "next/server";
import { z } from "zod";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  withApiLogging,
  withApiRateLimiting,
  withRequestValidation,
  withSecurityHeaders,
  withPublicCors,
  NextRouteHandler,
  withApiKeyAuth,
} from "../../public-api-middleware";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

// Define the validation schema
const requestAccessSchema = z.object({
  email: z.string().email().max(255),
  name: z.string().min(2).max(127),
  company: z.string().min(1).max(127).optional(),
  documentId: z.string().max(32),
  reason: z.string().min(10).max(1023),
  organizationId: z.string().max(32),
  registeredDomain: z.string().max(64),
});

// Define the request type including the validated body
type RequestWithBody = {
  validatedBody: z.infer<typeof requestAccessSchema>;
  orgId: string;
  log?: any;
};

// Handler function
async function handleRequestAccess(req: RequestWithBody) {
  const {
    email,
    name,
    documentId,
    reason,
    company = "",
    organizationId,
    registeredDomain,
  } = req.validatedBody;
  const orgId = req.orgId;

  console.log("req.validatedBody", req.validatedBody);

  // First, check if the document exists and belongs to the organization
  const document = await prisma.trustCenterDocument.findFirst({
    where: {
      id: documentId,
      organization_id: organizationId,
    },
  });

  if (!document) {
    return NextResponse.json(
      {
        error: {
          code: "document_not_found",
          message: "The requested document does not exist or is not available",
        },
      },
      { status: 404 },
    );
  }

  try {
    // Create access request
    const accessRequest = await prisma.documentAccessRequest.create({
      data: {
        email,
        name,
        company,
        trust_center_document_id: documentId,
        reason,
        status: "pending",
        organization_id: orgId,
        // In a public API context, there's no user session, so we use a system user or the first admin
        updated_by_id: await getSystemOrAdminUserId(orgId),
      },
    });

    // Log successful creation
    req.log?.info({
      requestId: accessRequest.id,
      documentId: documentId,
      orgId,
      msg: "Document access request created",
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: "Access request submitted successfully",
      request_id: accessRequest.id,
    });
  } catch (error: unknown) {
    req.log?.error({
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      msg: "Failed to create document access request",
    });

    return NextResponse.json(
      {
        error: {
          code: "request_failed",
          message: "Failed to process your request. Please try again later.",
        },
      },
      { status: 500 },
    );
  }
}

// Helper function to get a system user or admin user id for the organization
async function getSystemOrAdminUserId(orgId: string): Promise<string> {
  // Try to find an admin user for the organization
  const adminMember = await prisma.member.findFirst({
    where: {
      organization_id: orgId,
      member_role: {
        some: {
          role: {
            name: "admin",
          },
        },
      },
    },
    select: {
      user_id: true,
    },
  });

  if (adminMember) {
    return adminMember.user_id;
  }

  // Fallback to any user in the organization
  const anyMember = await prisma.member.findFirst({
    where: {
      organization_id: orgId,
    },
    select: {
      user_id: true,
    },
  });

  if (anyMember) {
    return anyMember.user_id;
  }

  // Last resort: get any system user (this should be configured in your system)
  // This is a placeholder - implement according to your system's design
  const systemUserId = process.env.SYSTEM_USER_ID;
  if (systemUserId) {
    return systemUserId;
  }

  throw new Error("Could not find a valid user ID for the organization");
}

// Compose middlewares
export const POST = withApiLogging(
  withPublicCors(
    withSecurityHeaders(
      withApiRateLimiting({ limit: 10, windowMs: 60000 })(
        withApiKeyAuth(
          withRequestValidation(requestAccessSchema)(
            handleRequestAccess as NextRouteHandler,
          ),
        ),
      ),
    ),
  ),
);
