import { NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { base64ToFile } from "@/lib/utils";
import { parse } from "path";
import { Prisma } from "@askinfosec/database";
import {
  withApiLogging,
  withApiRateLimiting,
  withRequestValidation,
  withSecurityHeaders,
  withPublicCors,
  NextRouteHandler,
  withApiKeyAuth,
} from "../../public-api-middleware";

// Define the validation schema
const reportIssueSchema = z.object({
  email: z.string().email().max(255),
  name: z.string().min(2).max(255).optional(),
  issueType: z.enum([
    "security",
    "compliance",
    "data_privacy",
    "service_availability",
    "account_access",
    "other",
  ]),
  title: z.string().min(5).max(200).optional(),
  description: z.string().min(20).max(10000),
  severity: z.enum(["low", "medium", "high", "critical"]).optional(),
  attachments: z
    .array(
      z.object({
        url: z.string().url(),
        name: z.string(),
        type: z.string().optional(),
        size: z.number().optional(),
      }),
    )
    .optional()
    .default([]),
  fileAttachments: z
    .array(
      z.object({
        attachment: z.string(),
        fileName: z.string(),
        mimeType: z.string(),
        sortOrder: z.number().optional(),
      }),
    )
    .optional()
    .default([]),
  source: z.string().max(50).optional().default("public_api"),
});

// Define the request type including the validated body
type RequestWithBody = {
  validatedBody: z.infer<typeof reportIssueSchema>;
  orgId: string;
  log?: any;
};

// Function to generate a ticket number
function generateTicketNumber(): string {
  const prefix = "SEC";
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const random = Math.random().toString(36).substring(2, 6).toUpperCase(); // 4 random chars
  return `${prefix}-${timestamp}${random}`;
}

// Handler function
async function handleReportIssue(req: RequestWithBody) {
  const {
    email,
    name,
    issueType,
    title,
    description,
    severity,
    attachments,
    fileAttachments,
    source,
  } = req.validatedBody;
  const orgId = req.orgId;

  try {
    // Generate ticket number
    const ticketNumber = generateTicketNumber();

    // Process file attachments if provided
    let processedAttachments: any = attachments;

    if (fileAttachments && fileAttachments.length > 0) {
      req.log?.info({
        msg: "Processing file attachments",
        count: fileAttachments.length,
        orgId,
      });

      const fileReferences = await Promise.all(
        fileAttachments.map(async (fileData, index) => {
          try {
            // Convert base64 to file
            const file = base64ToFile(
              fileData.attachment,
              fileData.fileName,
              fileData.mimeType,
            );
            const filePath = parse(fileData.fileName);

            // Create file record within a transaction to ensure RLS bypass works
            const fileRecord = await prisma.$transaction(async (tx) => {
              // Set RLS bypass for this transaction
              await tx.$executeRaw`SET LOCAL app.bypass_rls = 'on'`;

              // Create file record in the same transaction
              return await tx.file.create({
                data: {
                  name: filePath.name,
                  path: fileData.fileName,
                  buffer_file: Buffer.from(await file.arrayBuffer()),
                  organization_id: orgId,
                  created_by_id: null,
                  checksum: "",
                  document_type: "issue_report",
                  other_data: {
                    source: "trust_center",
                    originalFileName: fileData.fileName,
                    mimeType: fileData.mimeType,
                  },
                },
              });
            });

            req.log?.info({
              msg: "File saved to database",
              fileId: fileRecord.id,
              fileName: fileData.fileName,
              size: file.size,
            });

            return {
              fileId: fileRecord.id,
              displayName: fileData.fileName,
              uploadedAt: new Date().toISOString(),
              sortOrder: fileData.sortOrder || index,
              fileType: fileData.mimeType,
              fileSize: file.size,
            };
          } catch (error) {
            req.log?.error({
              msg: "Failed to process file attachment",
              fileName: fileData.fileName,
              error: error instanceof Error ? error.message : "Unknown error",
            });
            throw error;
          }
        }),
      );

      // Structure attachments like the help page does
      processedAttachments = {
        files: fileReferences,
        lastUpdated: new Date().toISOString(),
      } as Prisma.JsonValue;

      req.log?.info({
        msg: "All file attachments processed",
        fileCount: fileReferences.length,
      });
    }

    // Create issue report
    const issueReport = await prisma.issueReport.create({
      data: {
        email,
        name: name || null,
        issue_type: issueType,
        description,
        attachments: processedAttachments,
        source,
        status: "new",
        organization_id: orgId,
        other_options: {
          ticket_number: ticketNumber,
          ...(title && { title }),
          ...(severity && { severity }),
        },
        // No user info available from public API
        updated_by_id: null,
        reported_by_id: null,
      },
    });

    // Log successful creation
    req.log?.info({
      issueId: issueReport.id,
      ticketNumber,
      issueType: issueType,
      orgId,
      attachmentCount: fileAttachments?.length || 0,
      msg: "Issue report created with attachments",
    });

    // Return success response with ticket number
    return NextResponse.json({
      success: true,
      ticketNumber,
      message: "Security issue reported successfully",
      issue_id: issueReport.id,
    });
  } catch (error: unknown) {
    req.log?.error({
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      email,
      issueType,
      orgId,
      msg: "Failed to create issue report",
    });

    return NextResponse.json(
      {
        error: {
          code: "report_failed",
          message:
            "Failed to process your issue report. Please try again later.",
        },
      },
      { status: 500 },
    );
  }
}

// Compose middlewares with a longer request limit and rate limit
// Since issue reports can be more complex
export const POST = withApiLogging(
  withPublicCors(
    withSecurityHeaders(
      withApiRateLimiting({ limit: 5, windowMs: 60000 })(
        withApiKeyAuth(
          withRequestValidation(reportIssueSchema)(
            handleReportIssue as NextRouteHandler,
          ),
        ),
      ),
    ),
  ),
);
