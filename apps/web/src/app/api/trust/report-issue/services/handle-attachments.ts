import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient, File } from "@askinfosec/database";
import { Constants, ValidationErrors } from "../validation";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export interface FileUpload {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

export async function validateAndStoreFiles(
  files: FileUpload[],
  organizationId: string,
  createdById: string,
): Promise<{ success: boolean; fileIds?: string[]; error?: string }> {
  try {
    // Validate number of files
    if (files.length > Constants.MAX_FILES) {
      return { success: false, error: ValidationErrors.TOO_MANY_FILES };
    }

    // Validate each file
    for (const file of files) {
      // Check file size
      if (file.size > Constants.MAX_FILE_SIZE) {
        return { success: false, error: ValidationErrors.FILE_TOO_LARGE };
      }

      // Check file type
      if (!Constants.ALLOWED_FILE_TYPES.includes(file.mimetype)) {
        return { success: false, error: ValidationErrors.INVALID_FILE_TYPE };
      }

      // For images, validate dimensions
      if (file.mimetype.startsWith("image/")) {
        // TODO: Implement image dimension validation
        // This would require additional image processing library
        // For now, we'll skip this validation
      }
    }

    // Store files in database
    const storedFiles: File[] = [];
    for (const file of files) {
      const storedFile = await prisma.file.create({
        data: {
          name: file.originalname,
          buffer_file: file.buffer,
          checksum: generateChecksum(file.buffer),
          organization_id: organizationId,
          created_by_id: createdById,
          file_type: file.mimetype,
        },
      });
      storedFiles.push(storedFile);
    }

    return {
      success: true,
      fileIds: storedFiles.map((f) => f.id),
    };
  } catch (error) {
    console.error("Error handling file attachments:", error);
    return {
      success: false,
      error: "Failed to process file attachments",
    };
  }
}

// Helper function to generate checksum
function generateChecksum(buffer: Buffer): string {
  const crypto = require("crypto");
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Future implementation for malware scanning
export async function scanFileForMalware(file: Buffer): Promise<boolean> {
  // TODO: Implement malware scanning
  // This is a placeholder for future implementation
  return true;
}
