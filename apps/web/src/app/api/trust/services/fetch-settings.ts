import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  AppearanceSettings,
  FunctionalitySettings,
  GeneralSettings,
  IntegrationSettings,
  OverviewSettings,
} from "../schemas/settings-schema";
import { transformFileIdToPublicApiImageUrl } from "~/src/services/server/utils/shared-image-handler";
import { isFileIdReference } from "@/utils/image-url";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Transform file ID references in appearance settings to public API URLs
 * This ensures that the client receives fully formed URLs instead of file references
 */
function transformAppearanceFileIds(
  appearance: AppearanceSettings | undefined,
  orgId: string,
): AppearanceSettings | undefined {
  if (!appearance) return undefined;

  const result = { ...appearance };

  // Transform brand image URLs if they exist
  if (result.brand) {
    const brand = { ...result.brand };

    // Logo URL (may be stored as logo or logoUrl depending on schema)
    if (brand.logo && isFileIdReference(brand.logo)) {
      brand.logo =
        transformFileIdToPublicApiImageUrl(brand.logo, orgId) || brand.logo;
    }

    if (brand.logoUrl && isFileIdReference(brand.logoUrl)) {
      brand.logoUrl =
        transformFileIdToPublicApiImageUrl(brand.logoUrl, orgId) ||
        brand.logoUrl;
    }

    // Favicon
    if (brand.favicon && isFileIdReference(brand.favicon)) {
      brand.favicon =
        transformFileIdToPublicApiImageUrl(brand.favicon, orgId) ||
        brand.favicon;
    }

    // Open Graph Image
    if (brand.openGraphImage && isFileIdReference(brand.openGraphImage)) {
      brand.openGraphImage =
        transformFileIdToPublicApiImageUrl(brand.openGraphImage, orgId) ||
        brand.openGraphImage;
    }

    result.brand = brand;
  }

  return result;
}

/**
 * Fetches Trust Center settings for a specific organization
 */
export async function fetchSettingsForOrg(orgId: string): Promise<{
  general: GeneralSettings;
  appearance?: AppearanceSettings;
  functionality?: FunctionalitySettings;
  integration?: IntegrationSettings;
  overview?: OverviewSettings;
}> {
  try {
    // Fetch the TrustCenterSettings for the organization
    const settings = await prisma.trustCenterSettings.findFirst({
      where: { organization_id: orgId },
      select: {
        domain: true,
        is_custom_domain: true,
        is_domain_verified: true,
        api_key: true,
        appearance: true,
        functionality: true,
        integration: true,
        overview: true,
        company_name: true,
        why_trust_us_section: true,
      },
    });

    if (!settings) {
      // If no settings are found, return default values
      return {
        general: {
          domain: undefined,
          is_custom_domain: false,
          is_domain_verified: false,
          api_key: undefined,
          company_name: undefined,
          why_trust_us_section: [],
        },
      };
    }

    // Parse JSON fields
    const parsedAppearance = safeParseJson<AppearanceSettings>(
      settings.appearance,
    );

    // Transform file IDs in appearance settings
    const transformedAppearance = transformAppearanceFileIds(
      parsedAppearance,
      orgId,
    );
    console.log("transformedAppearance", transformedAppearance);
    // Process the JSON fields
    return {
      general: {
        domain: settings.domain || undefined,
        is_custom_domain: settings.is_custom_domain ?? false,
        is_domain_verified: settings.is_domain_verified ?? false,
        api_key: settings.api_key || undefined,
        company_name: settings.company_name || undefined,
        why_trust_us_section: settings.why_trust_us_section as string[],
      },
      appearance: transformedAppearance,
      functionality: safeParseJson<FunctionalitySettings>(
        settings.functionality,
      ),
      integration: safeParseJson<IntegrationSettings>(settings.integration),
      overview: safeParseJson<OverviewSettings>(settings.overview),
    };
  } catch (error) {
    console.error("Error fetching Trust Center settings:", error);
    throw new Error("Failed to load Trust Center settings.");
  }
}

/**
 * Helper to safely parse JSON fields from Prisma
 * For Prisma JSON fields, they should already come back as parsed objects
 */
function safeParseJson<T>(json: any): T | undefined {
  if (json === null || json === undefined) {
    return undefined;
  }

  try {
    // For Prisma JSON fields, they already come back as parsed objects
    return json as T;
  } catch (error) {
    console.error("Error processing JSON field:", error);
    return undefined;
  }
}
