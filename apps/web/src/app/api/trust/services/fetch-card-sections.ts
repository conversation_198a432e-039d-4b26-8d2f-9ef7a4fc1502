import { bypassedPrisma } from "@/lib/prisma";
import {
  PrismaClient,
  TrustCenterDocument,
  TrustCenterSectionCard,
} from "@askinfosec/database";
import { PREDEFINED_SECTION_CARDS } from "@/app/[lang]/(private)/[org]/(trust-center)/trust-center/_components/shared/section-cards";
import {
  MappedDocument,
  MergedSectionCard,
  MaturityLevel,
  PermissionLevel,
  StaticSectionCard,
} from "@/app/[lang]/(private)/[org]/(trust-center)/trust-center/_components/shared/section-cards.types";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Fetches card sections data for a specific organization
 * This is adapted from the admin dashboard's data fetching logic,
 * modified to work with API authentication
 */
export async function fetchCardSectionsForOrg(orgId: string): Promise<{
  sectionCardsByCategory: Record<string, MergedSectionCard[]>;
}> {
  console.log("fetchCardSectionsForOrg", orgId);
  try {
    // Build merged cards, prioritizing DB data
    const mergedCards: MergedSectionCard[] = [];
    const processedDbDocIds = new Set<string>(); // Keep track of DB docs added

    // 1. Fetch ALL TrustCenterDocuments for the org into a Map by ID
    const allDbDocuments = await prisma.trustCenterDocument.findMany({
      where: { organization_id: orgId },
    });
    const dbDocsMap = new Map(allDbDocuments.map((doc) => [doc.id, doc]));

    // 2. Fetch ALL TrustCenterSectionCards for the org into a Map by ID
    const allDbSectionCards = await prisma.trustCenterSectionCard.findMany({
      where: { organization_id: orgId },
      orderBy: { display_order: "asc" },
    });
    const dbCardsMap = new Map(
      allDbSectionCards.map((card) => [card.id, card]),
    );

    // Iterate through static card definitions to establish structure and defaults
    for (const staticCard of PREDEFINED_SECTION_CARDS) {
      const dbCard = dbCardsMap.get(staticCard.id); // Find corresponding DB card, if exists
      const currentCardDocuments: MappedDocument[] = [];

      // Process documents defined statically for this card
      for (const staticDoc of staticCard.documents || []) {
        const dbDoc = dbDocsMap.get(staticDoc.id); // Find corresponding DB doc, if exists

        if (dbDoc) {
          // DB Document exists - Use its data
          if (!dbDoc.is_hidden) {
            currentCardDocuments.push(
              mapDbDocument(dbDoc, dbCard ?? staticCard),
            );
            processedDbDocIds.add(dbDoc.id); // Mark this DB doc as processed
          }
        } else {
          // No DB Document - Use static data as fallback/default (only if PUBLIC)
          if (!staticDoc.isHidden) {
            currentCardDocuments.push(mapStaticDocument(staticDoc, staticCard));
          }
        }
      }

      // Add any *additional* DB documents linked specifically to this card's ID
      // that were NOT defined in the static list (custom docs added to a predefined card)
      if (dbCard) {
        for (const dbDoc of allDbDocuments) {
          if (
            dbDoc.section_card_id === dbCard.id &&
            !processedDbDocIds.has(dbDoc.id) &&
            !dbDoc.is_hidden
          ) {
            currentCardDocuments.push(mapDbDocument(dbDoc, dbCard));
            processedDbDocIds.add(dbDoc.id); // Mark this custom DB doc as processed
          }
        }
      }

      // Only add the card if it has documents and is not hidden
      if (
        currentCardDocuments.length > 0 &&
        !(dbCard?.is_hidden ?? staticCard.isHidden)
      ) {
        // Create the final card object for this static definition
        mergedCards.push({
          id: staticCard.id,
          title: dbCard?.title ?? staticCard.title,
          description: dbCard?.description ?? staticCard.description ?? "",
          category: dbCard?.category ?? staticCard.category ?? "Uncategorized",
          displayOrder: dbCard?.display_order ?? staticCard.displayOrder ?? 0,
          isHidden: dbCard?.is_hidden ?? staticCard.isHidden ?? false,
          documents: currentCardDocuments.sort((a, b) =>
            a.title < b.title ? -1 : 1,
          ),
        });
      }

      dbCardsMap.delete(staticCard.id); // Remove card from map as it's handled
    }

    // Add any remaining DB cards that weren't in the static list (custom cards)
    for (const dbCard of Array.from(dbCardsMap.values())) {
      // Skip hidden cards
      if (dbCard.is_hidden) continue;

      const customCardDocuments: MappedDocument[] = [];
      // Find DB documents belonging ONLY to this custom card
      for (const dbDoc of allDbDocuments) {
        if (
          dbDoc.section_card_id === dbCard.id &&
          !processedDbDocIds.has(dbDoc.id) &&
          dbDoc.permission_level === PermissionLevel.PUBLIC &&
          !dbDoc.is_hidden
        ) {
          customCardDocuments.push(mapDbDocument(dbDoc, dbCard));
          processedDbDocIds.add(dbDoc.id);
        }
      }

      // Only add the custom card if it has documents
      if (customCardDocuments.length > 0) {
        mergedCards.push({
          id: dbCard.id,
          title: dbCard.title,
          description: dbCard.description ?? "",
          category: dbCard.category ?? "Custom",
          displayOrder: dbCard.display_order ?? 0,
          isHidden: dbCard.is_hidden,
          documents: customCardDocuments.sort((a, b) =>
            a.title < b.title ? -1 : 1,
          ),
        });
      }
    }

    // Sort all merged cards by displayOrder
    mergedCards.sort((a, b) => (a.displayOrder ?? 0) - (b.displayOrder ?? 0));

    // Group cards by category for the main section display
    const sectionCardsByCategory: Record<string, MergedSectionCard[]> = {};
    mergedCards.forEach((card) => {
      // Only include cards with documents
      if (card.documents && card.documents.length > 0) {
        if (!sectionCardsByCategory[card.category]) {
          sectionCardsByCategory[card.category] = [];
        }
        sectionCardsByCategory[card.category].push(card);
      }
    });

    console.log("sectionCardsByCategory", sectionCardsByCategory);
    return {
      sectionCardsByCategory,
    };
  } catch (error) {
    console.error("Error fetching Trust Center card sections data:", error);
    throw new Error("Failed to load Trust Center data.");
  }
}

// Helper to map DB document to MappedDocument, applying defaults
function mapDbDocument(
  doc: TrustCenterDocument,
  sectionCard: TrustCenterSectionCard | StaticSectionCard,
): MappedDocument {
  return {
    id: doc.id,
    title: doc.title ?? "Untitled Document",
    description: doc.description ?? "No description",
    maturityLevel:
      (doc.maturity_level as MaturityLevel) ?? MaturityLevel.NOT_STARTED,
    permissionLevel:
      (doc.permission_level as PermissionLevel) ?? PermissionLevel.RESTRICTED,
    documentUrl: doc.document_url ?? "",
    isHidden: doc.is_hidden ?? false,
    sectionCardId: sectionCard.id,
    sectionCardCategory: sectionCard.category ?? "Uncategorized",
    isFeatured: doc.is_featured ?? false,
  };
}

// Helper to map static document to MappedDocument, applying defaults
function mapStaticDocument(
  doc: any, // Use any as a temporary workaround for diverse static structures
  sectionCard: StaticSectionCard,
): MappedDocument {
  return {
    id: doc.id,
    title: doc.title ?? "Untitled Document",
    description: doc.description ?? "No description",
    maturityLevel: doc.maturityLevel ?? MaturityLevel.NOT_STARTED,
    permissionLevel: doc.permissionLevel ?? PermissionLevel.RESTRICTED,
    documentUrl: doc.documentUrl ?? "",
    isHidden: doc.isHidden ?? false,
    sectionCardId: sectionCard.id,
    sectionCardCategory: sectionCard.category ?? "Uncategorized",
    isFeatured: doc.isFeatured ?? false,
  };
}
