import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { RequestWithBody } from "../validation";
import { NextResponse } from "next/server";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

interface OtherOptions {
  preferences?: {
    productUpdates: boolean;
    securityAlerts: boolean;
    complianceUpdates: boolean;
    trustCenterUpdates: boolean;
    newsletter: boolean;
  };
}

export async function handleSubscription(req: RequestWithBody) {
  const {
    email,
    name,
    preferences,
    emailSubscriptionType,
    organizationId,
    registeredDomain,
  } = req.validatedBody;
  const orgId = req.orgId;

  try {
    // Build where clause based on whether organizationId is provided
    const whereClause: any = {
      email,
      email_subscription_type: emailSubscriptionType,
    };
    if (organizationId) {
      whereClause.organization_id = organizationId;
    }

    // Check if subscription already exists
    const existingSubscription = await prisma.emailSubscription.findFirst({
      where: whereClause,
    });

    let subscription;

    if (existingSubscription) {
      // Update existing subscription
      subscription = await prisma.emailSubscription.update({
        where: {
          id: existingSubscription.id,
        },
        data: {
          name: name || existingSubscription.name,
          other_options: {
            preferences:
              preferences ||
              (existingSubscription.other_options as OtherOptions)?.preferences,
          },
          unsubscribed_at: null, // Re-subscribe if previously unsubscribed
        },
      });

      req.log?.info({
        subscriptionId: subscription.id,
        email,
        orgId,
        msg: "Email subscription updated",
      });
    } else {
      // Create new subscription with conditional organizationId
      const createData: any = {
        email,
        name: name || null,
        other_options: {
          preferences,
        },
        email_subscription_type: emailSubscriptionType,
      };

      if (organizationId) {
        createData.organization_id = organizationId;
      }

      subscription = await prisma.emailSubscription.create({
        data: createData,
      });

      req.log?.info({
        subscriptionId: subscription.id,
        email,
        orgId,
        msg: "Email subscription created",
      });
    }

    return NextResponse.json({
      success: true,
      message: existingSubscription
        ? "Subscription updated successfully"
        : "Subscription created successfully",
      subscription_id: subscription.id,
    });
  } catch (error: unknown) {
    req.log?.error({
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      email,
      orgId,
      msg: "Failed to create/update email subscription",
    });

    return NextResponse.json(
      {
        error: {
          code: "subscription_failed",
          message:
            "Failed to process your subscription. Please try again later.",
        },
      },
      { status: 500 },
    );
  }
}
