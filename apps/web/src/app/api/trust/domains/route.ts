import { NextRequest, NextResponse } from "next/server";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  withApiLogging,
  withSecurityHeaders,
  NextRouteHandler,
  withPublicCors,
} from "../../public-api-middleware";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

// Interface for domain-to-API key mapping
interface DomainApiKeyMapping {
  domain: string;
  api_key: string;
  is_custom_domain: boolean;
  organization_id: string;
}

/**
 * <PERSON><PERSON> for retrieving domain-to-API key mappings
 * This endpoint is protected by a master API key and is intended
 * to be used only by the public Trust Center website deployment
 */
async function handleGetDomains(req: NextRequest) {
  // Verify master API key
  const masterApiKey = process.env.MASTER_API_KEY;
  const providedApiKey = req.headers.get("x-master-api-key");

  if (!masterApiKey) {
    console.error("MASTER_API_KEY not configured");
    return NextResponse.json(
      {
        error: {
          code: "configuration_error",
          message: "MASTER_API_KEY not configured",
        },
      },
      { status: 500 },
    );
  }

  if (!providedApiKey || providedApiKey !== masterApiKey) {
    console.error("Invalid master API key");
    return NextResponse.json(
      { error: { code: "unauthorized", message: "Invalid master API key" } },
      { status: 401 },
    );
  }

  try {
    // Fetch all active Trust Center settings with domain and API key
    const trustCenterSettings = await prisma.trustCenterSettings.findMany({
      where: {
        domain: { not: null },
        is_domain_verified: true,
      },
      select: {
        domain: true,
        api_key: true,
        is_custom_domain: true,
        organization_id: true,
      },
    });

    // Map to the expected format
    const domainMappings: DomainApiKeyMapping[] = trustCenterSettings
      .filter((setting) => setting.domain) // Ensure no null values
      .map((setting) => ({
        domain: setting.domain || "",
        api_key: setting.api_key || "",
        is_custom_domain: setting.is_custom_domain || false,
        organization_id: setting.organization_id,
      }));

    // Add the demo/internal organization if configured
    // const demoOrgId = process.env.DEMO_ORG_ID;
    // const internalApiKey = process.env.INTERNAL_API_KEY;

    // if (demoOrgId && internalApiKey) {
    //   domainMappings.push({
    //     domain: process.env.NODE_ENV === "production" ? "trust.askinfosec.tech" : "localhost:3001",
    //     api_key: internalApiKey,
    //     is_custom_domain: false,
    //     organization_id: demoOrgId,
    //   });
    // }

    console.info("Valid domains found for domain->", domainMappings);

    return NextResponse.json({
      domains: domainMappings,
      count: domainMappings.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching domain mappings:", error);
    return NextResponse.json(
      {
        error: {
          code: "fetch_error",
          message: "Failed to fetch domain mappings",
          details: error instanceof Error ? error.message : undefined,
        },
      },
      { status: 500 },
    );
  }
}

// Compose middleware - using only essential middleware
export const GET = withApiLogging(
  withPublicCors(withSecurityHeaders(handleGetDomains as NextRouteHandler)),
);
