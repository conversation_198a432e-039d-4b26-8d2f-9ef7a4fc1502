import { Prisma } from "@askinfosec/database"; // Added for Prisma types

// Define a local interface for the expected payload
interface NewThreadPayload {
  id: string;
  created_at: number; // Unix timestamp
}

/**
 * Saves a new AI agent thread ID for a given application.
 * This function is responsible for persisting the thread ID associated with an organization.
 * Previously, it updated a JSON field in the Organization model. Now, it creates a new entry in the Thread table.
 *
 * @param appId The ID of the application (organization) to which the thread belongs.
 * @param newThread An object containing the thread's ID and creation timestamp.
 * @returns A promise that resolves when the operation is complete.
 * @throws Throws an error if the operation fails.
 */
export async function saveAIAgentThreadId(
  appId: string,
  newThread: NewThreadPayload,
): Promise<void> {
  if (
    !appId ||
    !newThread ||
    !newThread.id ||
    typeof newThread.created_at !== "number"
  ) {
    console.error("Invalid parameters for saveAIAgentThreadId:", {
      appId,
      newThread,
    });
    throw new Error(
      "Invalid parameters: appId, thread ID, and created_at are required.",
    );
  }

  try {
    // Dynamic import to avoid build-time evaluation
    const { prisma } = await import("@/lib/prisma");

    // Check if the thread already exists to prevent duplicates
    const existingThread = await prisma.thread.findUnique({
      where: { id: newThread.id },
    });

    if (existingThread) {
      console.log(
        `Thread ${newThread.id} already exists for organization ${existingThread.organizationId}. Skipping save.`,
      );
      return; // Thread already saved, possibly by a concurrent process or previous run
    }

    // Create a new entry in the Thread table
    await prisma.thread.create({
      data: {
        id: newThread.id,
        organizationId: appId,
        createdAt: new Date(newThread.created_at * 1000), // Convert Unix timestamp to JS Date
        source: "openai", // Set the source as 'openai'
        // 'message' and 'deletedAt' will be null by default as they are optional
      },
    });

    console.log(
      `Successfully saved new thread ${newThread.id} for organization ${appId} to Thread table.`,
    );
  } catch (error) {
    if (
      error instanceof Prisma.PrismaClientKnownRequestError &&
      error.code === "P2002"
    ) {
      // Unique constraint violation (P2002) - should be caught by the findUnique above, but as a safeguard.
      console.warn(
        `Thread ${newThread.id} could not be created due to unique constraint. It likely already exists.`,
      );
      return; // Treat as success if it already exists
    }
    console.error(
      `Error saving AI agent thread ID ${newThread.id} for app ${appId} to Thread table:`,
      error,
    );
    // Depending on requirements, you might want to re-throw or handle more gracefully.
    // For now, re-throwing to make the caller aware.
    throw new Error(
      `Failed to save thread ID ${newThread.id}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
