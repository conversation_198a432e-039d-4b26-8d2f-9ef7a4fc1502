"use server";

import { PrismaClient } from "@askinfosec/database";
import { OpenAISettings } from "~/src/models/organization";
import { unwrapOpenAISettings } from "~/src/lib/utils/openai-settings";

// AppId is the id of the organization
export async function getAIAgentSettings(
  appId: string,
): Promise<OpenAISettings> {
  console.log(
    "1️⃣ getAIAgentSettings: Starting to fetch settings for appId:",
    appId,
  );

  try {
    // Dynamic import to avoid build-time evaluation
    const { bypassedPrisma } = await import("@/lib/prisma");
    const prisma = bypassedPrisma as unknown as PrismaClient;

    console.log("2️⃣ getAIAgentSettings: Querying database for organization");
    const orgData = await prisma.organization.findFirst({
      where: {
        id: appId,
      },
      select: {
        id: true,
        company_name: true,
        openai_settings: true,
      },
    });

    console.log("3️⃣ getAIAgentSettings: Database query result:", {
      found: !!orgData,
      hasSettings: !!orgData?.openai_settings,
      queriedId: appId,
    });

    if (!orgData) {
      console.error("❌ getAIAgentSettings: Organization not found:", {
        appId,
        idLength: appId.length,
        idType: typeof appId,
      });
      throw new Error(`Organization not found for appId: ${appId}`);
    }

    if (!orgData.openai_settings) {
      console.error("❌ getAIAgentSettings: OpenAI settings missing:", {
        orgId: appId,
        orgName: orgData.company_name,
      });
      throw new Error(`OpenAI settings not found for organization: ${appId}`);
    }

    // Unwrap settings if they have a 'set' property
    const openai_settings = (await unwrapOpenAISettings(
      orgData.openai_settings,
      {
        orgId: appId,
        logIssue: true,
      },
    )) as OpenAISettings;

    console.log("4️⃣ getAIAgentSettings: Validating settings");

    // Apply global API key fallback before validation
    // We need to ensure there is always openai api key. If this is not set, we need to use the global openai api key.
    if (!openai_settings.api_key) {
      // TODO: We can formally log this count to the database or logs that we can make a report per organization.
      console.log(
        "ℹ️ getAIAgentSettings: Organization API key not provided, using global API key",
      );
      openai_settings.api_key = process.env.OPENAI_API_KEY;
    }

    // Validate required fields after applying fallback
    if (!openai_settings.api_key) {
      console.error(
        "❌ getAIAgentSettings: OpenAI API key is missing and no global key is configured",
      );
      throw new Error(
        "OpenAI API key is not configured and no global fallback is available",
      );
    }

    // Log detailed information about the assistants configuration
    console.log("🔍 getAIAgentSettings: Assistants configuration:", {
      hasAssistantsArray: !!openai_settings.assistants,
      assistantsCount: openai_settings.assistants
        ? openai_settings.assistants.length
        : 0,
      aiAgentAssistantId: openai_settings.ai_agent_assistant_id || "not set",
      aiCopilotAssistantId:
        openai_settings.ai_copilot_assistant_id || "not set",
    });

    // Check if we have either assistants array or specific assistant IDs
    const hasAssistants =
      !!openai_settings.assistants && openai_settings.assistants.length > 0;
    const hasAgentAssistantId = !!openai_settings.ai_agent_assistant_id;
    const hasCopilotAssistantId = !!openai_settings.ai_copilot_assistant_id;

    // We need at least one of these to be configured
    if (!hasAssistants && !hasAgentAssistantId && !hasCopilotAssistantId) {
      console.warn(
        "⚠️ getAIAgentSettings: No OpenAI Assistants configured, using development mode",
      );

      // For development purposes, create a placeholder assistant configuration
      // This allows the application to continue functioning without a real assistant
      openai_settings.assistants = [
        {
          id: "dev-assistant-id",
          name: "Development Assistant",
          vector_store_ids: [],
          instructions:
            "This is a placeholder assistant for development purposes.",
        },
      ];

      // Set the ai_agent_assistant_id and ai_copilot_assistant_id to the development assistant
      openai_settings.ai_agent_assistant_id = "dev-assistant-id";
      openai_settings.ai_copilot_assistant_id = "dev-assistant-id";

      console.log("🔧 Created development assistant configuration");
    }

    // If we have specific assistant IDs but no assistants array, create an empty array
    if (!openai_settings.assistants) {
      openai_settings.assistants = [];
    }
    // Check if token_secret_key is missing and provide a fallback for development
    if (!openai_settings.token_secret_key) {
      console.warn(
        "⚠️ getAIAgentSettings: Token Secret Key is missing, using fallback for development",
      );
      // Use a default token secret key for development purposes
      openai_settings.token_secret_key =
        process.env.DEFAULT_TOKEN_SECRET_KEY || "dev_token_secret_key";

      // Log that we're using a fallback
      console.log("🔧 Using fallback token_secret_key for development");
    }

    // Check if the original data had an API key before we applied fallback
    const originalApiKeyWasEmpty =
      typeof orgData.openai_settings === "object" &&
      orgData.openai_settings !== null &&
      !(orgData.openai_settings as any).api_key;

    console.log("5️⃣ getAIAgentSettings: Settings validation passed:", {
      hasApiKey: !!openai_settings.api_key,
      isUsingGlobalKey: originalApiKeyWasEmpty && !!openai_settings.api_key,
      aiCopilotAssistantId:
        openai_settings.ai_copilot_assistant_id || "not set",
      aiAgentAssistantId: openai_settings.ai_agent_assistant_id || "not set",
      assistantsCount: openai_settings.assistants
        ? openai_settings.assistants.length
        : 0,
      assistantIds: openai_settings.assistants
        ? openai_settings.assistants.map((a) => a.id)
        : [],
      allowedOriginsCount: openai_settings.allowed_origins?.length ?? 0,
      hasTokenSecretKey: !!openai_settings.token_secret_key,
    });

    return openai_settings;
  } catch (error) {
    console.error("❌ getAIAgentSettings: Error fetching settings:", {
      error: error instanceof Error ? error.message : "Unknown error",
      appId,
      errorType: error instanceof Error ? error.constructor.name : typeof error,
    });
    throw error;
  }
}
