import {
  with<PERSON>og<PERSON><PERSON>outeH<PERSON><PERSON>,
  withSessionRouteHandler,
} from "@/app/api/middlewares";
import { acceptInviteVendor, getOrg } from "@/services/server/organization";
import { addNotificationForUser } from "@/services/server/notification";
import { NotificationStatus, NotificationType } from "@/models/notification";
import { NextRequest, NextResponse } from "next/server";
import { HttpError } from "@/lib/errors";
import logger from "@/lib/logger-new";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

// Define expected request body structure
interface RequestBody {
  invite: {
    id: string; // Invitation ID
    orgId: string; // Organization ID
    roleId: string; // Role to assign
    image: string; // Organization image
    createdBy: string; // User who created the invite
  };
  userId: string; // User accepting the invite
  email: string; // Email associated with invite
  questionnaireId: string; // Associated questionnaire
}

/**
 * API Handler: Processes acceptance of vendor invitations
 * 1. Accepts the vendor invitation
 * 2. Associates questionnaire with vendor
 * 3. Creates notification for organization owner
 */
const acceptVendorInviteHandler = async (req: NextRequest) => {
  // Parse request body
  const { invite, userId, email, questionnaireId }: RequestBody =
    await req.json();

  try {
    // Process vendor invitation acceptance
    const { organization, questionnaireId: qId } = await acceptInviteVendor({
      invite,
      userId,
      email,
      type: "vendor",
      questionnaireId,
    });

    try {
      // Get organization directly without user check for notification
      const org = await prisma.organization.findUnique({
        where: { id: invite.orgId },
        select: {
          id: true,
          main_owner_id: true,
        },
      });

      // Create notification for organization owner
      const notification = {
        message: `Vendor ${email} accepted your invite!`,
        status: "unread" as keyof NotificationStatus,
        type: "user_invite" as keyof NotificationType,
        user_id: invite.createdBy,
        organization_id: invite.orgId,
      };

      await addNotificationForUser({
        org: invite.orgId,
        user: userId,
        notification,
      });
    } catch (e) {
      // Log notification errors but don't fail the request
      logger.error(
        `Error adding notification: ${e instanceof Error ? e.message : String(e)}`,
      );
    }

    // Return success response with organization and questionnaire details
    return new NextResponse(
      JSON.stringify({
        message: "Vendor invite successful!",
        organization: invite.orgId,
        questionnaireId: qId,
      }),
      { status: 200 },
    );
  } catch (e) {
    // Log and throw errors from the main invite acceptance process
    req.log.error(e);
    logger.error(
      `acceptVendorInvite(): ${e instanceof Error ? e.message : String(e)}`,
    );
    throw new HttpError(500, e as any, true);
  }
};

// Check if we're in a build-time context
const isBuildTime =
  typeof process !== "undefined" && process.env.IS_BUILD_TIME === "true";

// Apply logging and session middleware to the handler
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Record<string, string>> },
): Promise<Response> {
  // Return a mock response during build-time
  if (isBuildTime) {
    return new Response("Build-time mock", { status: 200 });
  }

  const resolvedParams = await params;
  const handler = withLoggingRouteHandler(
    withSessionRouteHandler()(acceptVendorInviteHandler),
  );
  return await handler(request, { params: resolvedParams });
}
