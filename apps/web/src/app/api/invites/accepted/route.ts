import { createId } from "@paralleldrive/cuid2";
import { AdapterUser } from "next-auth/adapters";
import { NextRequest, NextResponse } from "next/server";
import { authAdapter } from "@/lib/auth-helpers";
import { jwtService } from "@/lib/jwt";
import logger from "@/lib/logger-new";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { NotificationType } from "@/models/notification";
import { NotificationStatus } from "@/models/notification";
import { addNotificationForUser } from "@/services/server/notification";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export async function GET(request: NextRequest) {
  console.log("[Invite Accept API] Processing invite acceptance request");

  try {
    // Get token from URL
    const token = request.nextUrl.searchParams.get("token");
    console.log(
      "[Invite Accept API] Token from URL:",
      token ? "Present" : "Missing",
    );

    if (!token) {
      console.log("[Invite Accept API] Error: Token is missing");
      return NextResponse.json({ error: "Token is required" }, { status: 400 });
    }

    // Decode JWT token
    console.log("[Invite Accept API] Attempting to decode JWT token");
    let decodedToken;
    try {
      decodedToken = await jwtService.decode({ token });
      console.log("[Invite Accept API] Decoded token:", {
        hasToken: !!decodedToken,
        tokenContent: decodedToken,
      });
    } catch (error) {
      console.error("[Invite Accept API] JWT decode error:", error);
      return NextResponse.json(
        { error: "Failed to decode token" },
        { status: 400 },
      );
    }

    if (!decodedToken || !decodedToken.id) {
      console.log("[Invite Accept API] Error: Invalid token structure", {
        decodedToken,
      });
      return NextResponse.json({ error: "Invalid token" }, { status: 400 });
    }

    // Extract invite ID
    const inviteId = decodedToken.id;
    console.log("[Invite Accept API] Looking up invite with ID:", inviteId);

    // Find invite in database
    const invite = await prisma.invite.findUnique({
      where: { id: inviteId as unknown as string },
      include: {
        organization: true,
        role: true,
      },
    });

    console.log("[Invite Accept API] Invite lookup result:", {
      found: !!invite,
      inviteDetails: invite
        ? {
            id: invite.id,
            email: invite.email,
            orgId: invite.organization_id,
            roleId: invite.role_id,
            expired: invite.expired_at < new Date(),
          }
        : null,
    });

    if (!invite) {
      console.log("[Invite Accept API] Error: Invite not found");
      return NextResponse.redirect(
        new URL(
          `/en/auth-error?error=InviteNotFound`,
          process.env.NEXTAUTH_URL,
        ),
        {
          status: 302,
        },
      );
    }

    if (invite.expired_at < new Date()) {
      console.log("[Invite Accept API] Error: Invite has expired", {
        expiredAt: invite.expired_at,
        now: new Date(),
      });
      return NextResponse.redirect(
        new URL(`/en/expired?type=invite`, process.env.NEXTAUTH_URL),
        {
          status: 302,
        },
      );
    }

    // Get the user object of the invited user. If not found, we need to automatically create a new user account
    let invitedUser = await prisma.user.findUnique({
      where: { email: invite.email! },
    });
    console.log("[Invite Accept API] Invited user lookup result:", {
      found: !!invitedUser,
      userDetails: invitedUser,
    });

    if (!invitedUser) {
      // Create a new user account for the invited user
      // Note that we automatically set the email_verified field to true since the user click this link from the email invite
      // invitedUser = await bypassedPrisma.user.create({
      //   data: {
      //     email: invite.email,
      //     name: invite.email,
      //     emailVerified: new Date(),
      //   },
      // });
      const newUserFromAdapterUser: AdapterUser = {
        id: createId(),
        email: invite.email!,
        emailVerified: new Date(),
      };
      // Make sure authAdapter exists before using it
      if (authAdapter?.createUser) {
        await authAdapter.createUser(newUserFromAdapterUser);
      } else {
        console.error(
          "[Invite Accept API] Auth adapter not available for user creation",
        );
        return NextResponse.json(
          { error: "Auth adapter not available for user creation" },
          { status: 500 },
        );
      }
      invitedUser = await prisma.user.findUnique({
        where: { email: invite.email! },
      });
      if (!invitedUser) {
        console.error(
          "[Invite Accept API] Error on automatically creating new user for invited email that doesn't have existing account.",
        );
        return NextResponse.json(
          {
            error:
              "Error on automatically creating new user for invited email that doesn't have exisint account.",
          },
          { status: 400 },
        );
      }
      // Create default organization
      // const org = await createOrg({
      //   userId: invitedUser.id,
      //   orgName: invitedUser.email?.split("@")[0] || "",
      // });
      // console.log(
      //   "[Invite Accept API] New user created and default organization created:",
      //   invitedUser,
      //   org.id
      // );
    }

    // Add user to organization member table
    const newMemberData = {
      organization_id: invite.organization_id,
      user_id: invitedUser.id,
      role_id: invite.role_id,
    };
    console.log("[Invite Accept API] New member data:", newMemberData);
    if (!newMemberData.user_id) {
      console.log("[Invite Accept API] Error: User not found");
      return NextResponse.json({ error: "User not found" }, { status: 400 });
    }
    console.log("[Invite Accept API] Creating new member:", newMemberData);
    await prisma.member.create({
      data: newMemberData,
    });
    console.log("[Invite Accept API] New member created");
    // Delete invite record
    await prisma.invite.delete({
      where: { id: inviteId as unknown as string },
    });
    console.log("[Invite Accept API] Invite deleted");

    // Get the language from the request URL
    // The api does not have language prefix, so we can use en for now
    // const lang = request.nextUrl.pathname.split("/")[1] || "en";
    const lang = "en";

    // Construct redirect URL with necessary parameters
    const baseUrl = new URL(
      `/${lang}/invite-accepted`,
      process.env.NEXTAUTH_URL,
    );

    // Add the organization ID as the callbackUrl parameter
    const orgCallbackUrl = `/${lang}/${invite.organization_id}`;
    baseUrl.searchParams.set("callbackUrl", orgCallbackUrl);

    // Add userId and email parameters for automatic session creation
    baseUrl.searchParams.set("userId", invitedUser.id);
    baseUrl.searchParams.set("email", invitedUser.email || "");
    baseUrl.searchParams.set("orgId", invite.organization_id);

    console.log("[Invite Accept API] Redirecting with params:", {
      baseUrl: baseUrl.toString(),
      userId: invitedUser.id,
      email: invitedUser.email,
      orgId: invite.organization_id,
      lang,
      callbackUrl: orgCallbackUrl,
      fullRedirectUrl: baseUrl.toString(),
      originalUrl: request.nextUrl.toString(),
    });

    await notifyMemberInviteAccepted(
      invite.organization_id,
      invite.created_by,
      invite.email || "Email not available",
    );

    return NextResponse.redirect(baseUrl.toString(), {
      status: 302,
    });
  } catch (error) {
    console.error("[Invite Accept API] Unexpected error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

const notifyMemberInviteAccepted = async (
  orgId: string,
  invitedBy: string,
  email: string,
) => {
  try {
    // Create notification for organization owner
    const notification = {
      message: `Member ${email} accepted your invite!`,
      status: "unread" as keyof NotificationStatus,
      type: "user_invite" as keyof NotificationType,
      user_id: invitedBy,
      organization_id: orgId,
    };

    await addNotificationForUser({
      org: orgId,
      user: invitedBy,
      notification,
    });
  } catch (e) {
    // Log notification errors but don't fail the request
    logger.error(
      `Error adding notification: ${e instanceof Error ? e.message : String(e)}`,
    );
  }
};
