import logger from "@/lib/logger-new";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import <PERSON><PERSON> from "stripe";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export async function handleCheckoutSessionCompleted(
  payload: Stripe.Checkout.Session,
  requestId: string,
): Promise<void> {
  logger.info(
    {
      requestId,
      checkoutSessionId: payload.id,
      mode: payload.mode,
      metadata: payload.metadata,
    },
    "Processing checkout session",
  );

  // Extract metadata
  const orgId = payload.metadata?.orgId;
  const userId = payload.metadata?.userId;
  const priceId = payload.metadata?.priceId;
  // const organizationSubscriptionId = payload.metadata?.organizationSubscriptionId;

  if (!orgId) {
    logger.warn(
      { requestId, checkoutSessionId: payload.id },
      "Checkout session missing organization ID in metadata",
    );
  }

  // For subscription mode, handle subscription creation
  if (payload.mode === "subscription") {
    if (!payload.subscription) {
      logger.error(
        { requestId, checkoutSessionId: payload.id },
        "Checkout session is in subscription mode but has no subscription ID",
      );
      throw new Error("Missing subscription ID in checkout session");
    }

    if (!payload.customer) {
      logger.error(
        { requestId, checkoutSessionId: payload.id },
        "Checkout session has no customer ID",
      );
      throw new Error("Missing customer ID in checkout session");
    }

    const organization = await prisma.organization.findUnique({
      where: { id: orgId },
    });

    if (!organization) {
      logger.error({ requestId, orgId }, "Organization not found");
    }

    // Make sure the organization has the customer ID, but we only need to update if it's not already set or if it's different
    if (
      organization &&
      (!organization.stripe_customer_id ||
        organization.stripe_customer_id !== payload.customer)
    ) {
      try {
        await prisma.organization.update({
          where: { id: orgId },
          data: { stripe_customer_id: payload.customer as string },
        });

        logger.info(
          { requestId, orgId, customerId: payload.customer },
          "Updated organization with Stripe customer ID",
        );
      } catch (error) {
        logger.error(
          { requestId, error, orgId, customerId: payload.customer },
          "Failed to update organization with Stripe customer ID",
        );
        // Continue processing - this is not a critical error
      }
    }

    // Process the subscription. We are expecting organizationSubscriptionId to be available, this was included in the metadata when the checkout session was created
    // if (organizationSubscriptionId) {
    // const organizationSubscription = await bypassedPrisma.organizationSubscription.findUnique({
    //   where: { id: organizationSubscriptionId },
    // });

    // if (!organizationSubscription) {
    //   logger.error(
    //     { requestId, organizationSubscriptionId },
    //     "Organization subscription not found"
    //   );
    // }

    // const updateResult = await bypassedPrisma.organizationSubscription.update({
    //   where: { id: organizationSubscription?.id },
    //   data: {
    //     stripe_sub_id: payload.subscription as string,
    //   },
    // });

    // Logging
    // if (updateResult === null) {
    //   logger.error(
    //     { requestId, organizationSubscriptionId },
    //     `Update to organization subscription ${organizationSubscriptionId} failed. updateResult: ${updateResult}`
    //   );
    // } else {
    //   logger.info(
    //     {
    //       requestId,
    //       checkoutSessionId: payload.id,
    //       customerId: payload.customer,
    //       subscriptionId: payload.subscription,
    //       orgId,
    //       userId,
    //       priceId,
    //     },
    //     "Subscription created from checkout session"
    //   );
    // }
    // }
  }
  // For payment mode, handle one-time payments with Payment Intents
  // else if (payload.mode === "payment" && payload.payment_intent) {
  //   // Retrieve the payment intent to get payment status
  //   const paymentIntentId =
  //     typeof payload.payment_intent === "string"
  //       ? payload.payment_intent
  //       : payload.payment_intent.id;

  //   try {
  //     const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

  //     // Process the payment based on its status
  //     if (paymentIntent.status === "succeeded") {
  //       // Handle successful payment
  //       logger.info(
  //         {
  //           requestId,
  //           paymentIntentId,
  //           checkoutSessionId: payload.id,
  //           orgId,
  //           userId,
  //         },
  //         "Payment succeeded"
  //       );

  //       // Additional payment processing logic here
  //     } else {
  //       // Payment is still processing or requires action
  //       logger.info(
  //         { requestId, paymentIntentId, status: paymentIntent.status, orgId, userId },
  //         "Payment not yet succeeded"
  //       );
  //     }
  //   } catch (error) {
  //     logger.error(
  //       { requestId, paymentIntentId, error, orgId, userId },
  //       "Failed to retrieve payment intent"
  //     );
  //     throw error;
  //   }
  // }
}
