import logger from "@/lib/logger-new";
import <PERSON><PERSON> from "stripe";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { stripe } from "~/src/lib/stripe";
import { syncOrganizationModules } from "~/src/services/server/org-module-sync";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export async function handleCustomerSubscriptionCreated(
  payload: Stripe.Subscription,
  requestId: string,
): Promise<void> {
  try {
    if (typeof payload.customer !== "string") {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Subscription customer is not a string",
      );
      throw new Error("Invalid customer ID in subscription");
    }

    const current_period_end = payload.items.data[0].current_period_end;
    const current_period_start = payload.items.data[0].current_period_start;
    const customer = payload.customer;

    const org = await prisma.organization.findFirst({
      where: {
        stripe_customer_id: customer,
      },
      select: {
        id: true,
      },
    });

    if (!org) {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Organization not found",
      );
      throw new Error("Organization not found");
    }

    const subscriptionTier = await prisma.subscriptionTier.findFirst({
      where: {
        price: {
          stripe_price_id: payload.items.data[0].plan.id,
        },
      },
    });

    if (!subscriptionTier) {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Subscription tier not found",
      );
      throw new Error("Subscription tier not found");
    }

    const createdOrgSub = await prisma.organizationSubscription.create({
      data: {
        organization_id: org.id,
        stripe_sub_id: payload.id,
        subscription_tier_id: subscriptionTier.id,
        current_period_start: new Date(current_period_start * 1000),
        current_period_end: new Date(current_period_end * 1000),
        status: payload.status,
      },
    });

    if (!createdOrgSub) {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Failed to create organization subscription",
      );
      throw new Error("Failed to create organization subscription");
    }

    logger.info(
      {
        requestId,
        subscriptionId: payload.id,
        customerId: payload.customer,
        status: payload.status,
      },
      "Subscription created successfully",
    );

    logger.info(
      {
        requestId,
        subscriptionId: payload.id,
        customerId: payload.customer,
        status: payload.status,
      },
      "Subscription created successfully",
    );

    // Sync modules after subscription change
    try {
      await syncOrganizationModules(org.id);
    } catch (syncError) {
      logger.error(
        { org, syncError },
        "Failed to sync organization modules after subscription creation",
      );
    }

    try {
      // Update stripe subscription metadata with org subscription id
      await stripe.subscriptions.update(payload.id, {
        metadata: {
          organizationSubscriptionId: createdOrgSub.id,
        },
      });
    } catch (syncError) {
      logger.error(
        { org, syncError },
        "Failed to update stripe subscription metadata with org subscription id",
      );
    }
  } catch (error) {
    logger.error(
      { requestId, error, eventType: "customer.subscription.created" },
      "Failed to manage subscription status change",
    );
    throw error;
  }
}
