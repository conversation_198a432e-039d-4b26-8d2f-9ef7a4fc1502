import logger from "@/lib/logger-new";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import <PERSON><PERSON> from "stripe";
import { syncOrganizationModules } from "~/src/services/server/org-module-sync";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export async function handleSubscriptionEvent(
  payload: Stripe.Subscription,
  requestId: string,
  eventType: "customer.subscription.updated" | "customer.subscription.deleted",
): Promise<void> {
  try {
    if (typeof payload.customer !== "string") {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Subscription customer is not a string",
      );
      throw new Error("Invalid customer ID in subscription");
    }

    const organizationSubscription =
      await prisma.organizationSubscription.findFirst({
        where: { stripe_sub_id: payload.id },
      });

    if (!organizationSubscription) {
      logger.error(
        { requestId, subscriptionId: payload.id },
        "Organization subscription not found",
      );
      throw new Error("Organization subscription not found");
    }

    // Update common subscription fields
    const updateData: any = {
      canceled_at: payload.canceled_at
        ? new Date(payload.canceled_at * 1000)
        : null,
      cancel_at_period_end: payload.cancel_at_period_end,
      current_period_end: new Date(
        payload.items.data[0].current_period_end * 1000,
      ),
      current_period_start: new Date(
        payload.items.data[0].current_period_start * 1000,
      ),
      end_date: payload.ended_at ? new Date(payload.ended_at * 1000) : null,
      start_date: new Date(payload.start_date * 1000),
      status: payload.status,
    };

    // For deleted subscriptions, add additional status updates
    if (eventType === "customer.subscription.deleted") {
      updateData.status = "canceled";
    }

    await prisma.organizationSubscription.update({
      where: { id: organizationSubscription.id },
      data: updateData,
    });

    // Sync modules after subscription change
    try {
      await syncOrganizationModules(organizationSubscription.organization_id);
    } catch (syncError) {
      logger.error(
        { organizationSubscription, syncError },
        `Failed to sync organization modules after ${eventType === "customer.subscription.deleted" ? "deletion" : "update"}`,
      );
    }

    logger.info(
      {
        requestId,
        subscriptionId: payload.id,
        customerId: payload.customer,
        status: payload.status,
        eventType,
      },
      `Subscription ${eventType === "customer.subscription.deleted" ? "deleted" : "updated"} successfully`,
    );
  } catch (error) {
    logger.error(
      { requestId, error, eventType },
      `Failed to manage subscription ${eventType}`,
    );
    throw error;
  }
}
