import { NextRequest, NextResponse } from "next/server";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { createSession } from "@/lib/auth-api";
import { z } from "zod";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "create-session-api", "n/a");

// Define validation schema for request body
const createSessionSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  email: z.string().email("Valid email is required"),
  callbackUrl: z.string().optional().default("/en"),
});

/**
 * POST handler for creating a session
 * This endpoint creates a session for a user and returns a redirect URL
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();

    logger.addLog({
      usageType: UsageType.debug,
      message: "Create session request received",
      obj: { body },
    });

    // Validate input with Zod
    const validationResult = createSessionSchema.safeParse(body);
    if (!validationResult.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request data for session creation",
        obj: { errors: validationResult.error.format() },
      });
      logger.printAll();

      return NextResponse.json(
        {
          message: "Invalid request data",
          errors: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const { userId, email, callbackUrl } = validationResult.data;

    // Verify the user exists in the database
    const prisma = bypassedPrisma;

    logger.addLog({
      usageType: UsageType.debug,
      message: "Verifying user in database",
      obj: { userId, email },
    });

    try {
      // Use Prisma client with proper typing
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
        },
      });

      if (!user || user.email !== email) {
        logger.addLog({
          usageType: UsageType.error,
          message: "User not found or email mismatch",
          obj: { userId, email, foundEmail: user?.email },
        });
        logger.printAll();

        return NextResponse.json(
          {
            message: "User not found or email mismatch",
            code: "USER_NOT_FOUND",
          },
          { status: 404 },
        );
      }

      logger.addLog({
        usageType: UsageType.info,
        message: "User verified successfully",
        obj: { userId, email },
      });

      // Create a session for the user using our reusable function
      const { response, error } = await createSession({
        userId,
        email,
        callbackUrl: callbackUrl || "/en",
        logger,
      });

      if (error) {
        logger.addLog({
          usageType: UsageType.error,
          message: "Error creating session",
          obj: { error },
        });
        logger.printAll();

        return NextResponse.json(
          {
            message: "Failed to create session",
            error,
          },
          { status: 500 },
        );
      }

      // Return success response with redirect URL
      // We need to extract the redirect URL from the response
      const redirectUrl = response.headers.get("Location") || callbackUrl;

      logger.addLog({
        usageType: UsageType.info,
        message: "Session created successfully",
        obj: { redirectUrl },
      });
      logger.printAll();

      return NextResponse.json(
        {
          message: "Session created successfully",
          redirectUrl,
        },
        { status: 200 },
      );
    } catch (dbError) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Database error when verifying user",
        obj: { error: dbError, userId, email },
      });
      logger.printAll();

      return NextResponse.json(
        {
          message: "Database error",
          code: "DATABASE_ERROR",
        },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error in create session endpoint",
      obj: { error },
    });
    logger.printAll();

    return NextResponse.json(
      {
        message: "Server error",
        code: "SERVER_ERROR",
      },
      { status: 500 },
    );
  }
}
