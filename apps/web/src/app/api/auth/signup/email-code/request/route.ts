import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { transport } from "@/lib/email";
import { render } from "@react-email/render";
import EmailCode from "../../../../../../../emails/email-code";

// Force Node.js runtime for email sending
export const runtime = "nodejs";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "email-code", "signup-request");

// Rate limiting constants
const COOLDOWN_PERIOD_MS = 5 * 60 * 1000; // 5 minutes in milliseconds

// Request validation schema
const requestSchema = z.object({
  email: z.string().email(),
  csrfToken: z.string().optional(),
});

/**
 * Generates a random 6-digit code
 * @returns A 6-digit numeric code as a string
 */
function generateCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * POST handler for requesting an email verification code for signup
 * Generates a code, stores it in the database, and sends it via email
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request",
        obj: { error: result.error.format() },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const { email } = result.data;
    const prisma = bypassedPrisma as unknown as PrismaClient;

    // Check if the user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      // For security reasons, don't reveal that the user exists
      // Instead, return a generic security message
      logger.addLog({
        usageType: UsageType.warning,
        message: "Attempted signup with existing email",
        obj: { email },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "We encountered an issue registering this account" },
        { status: 400 },
      );
    }

    // Create a temporary user record for verification
    const tempUser = await prisma.user.create({
      data: {
        email,
        name: email.split("@")[0], // Use part of email as initial name
        emailVerified: null, // Will be set after verification
      },
    });

    // Generate a 6-digit code
    const code = generateCode();

    // Set expiry time (10 minutes from now)
    const expires = new Date(Date.now() + 10 * 60 * 1000);

    // Create a new code entry
    await prisma.emailAuthCode.create({
      data: {
        userId: tempUser.id,
        code,
        expires,
        used: false,
        type: "registration", // Add type for registration
      },
    });

    // Send the code via email
    const emailHtml = await render(
      EmailCode({
        code,
        email: tempUser.email!,
        name: tempUser.name || tempUser.email!.split("@")[0],
        isSignup: true, // Flag to indicate this is for signup
      }),
    );

    await transport.sendMail({
      to: tempUser.email!,
      from: process.env.EMAIL_FROM!,
      subject: "Your AskInfosec Verification Code",
      html: emailHtml,
      text: `Your verification code is: ${code}. It will expire in 10 minutes.`,
    });

    logger.addLog({
      usageType: UsageType.info,
      message: "Signup email code sent successfully",
      obj: { userId: tempUser.id, email: tempUser.email },
    });
    logger.printAll();

    return NextResponse.json({ success: true, userId: tempUser.id });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error sending signup email code",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to send verification code" },
      { status: 500 },
    );
  }
}
