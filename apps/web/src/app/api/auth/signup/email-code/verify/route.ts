import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { signIn } from "@/auth";

// Force Node.js runtime for database operations
export const runtime = "nodejs";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "email-code", "signup-verify");

// Request validation schema
const requestSchema = z.object({
  email: z.string().email(),
  code: z.string().length(6),
  callbackUrl: z.string().optional(),
});

/**
 * POST handler for verifying an email code during signup
 * Validates the code against the database, completes the account creation, and signs in the user
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request",
        obj: { error: result.error.format() },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const { email, code, callbackUrl = "/en" } = result.data;
    const prisma = bypassedPrisma as unknown as PrismaClient;

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      logger.addLog({
        usageType: UsageType.error,
        message: "User not found",
        obj: { email },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "No account found with this email address" },
        { status: 404 },
      );
    }

    // Find the code in the database
    const emailAuthCode = await prisma.emailAuthCode.findFirst({
      where: {
        userId: user.id,
        used: false,
        expires: { gt: new Date() },
      },
    });

    if (!emailAuthCode) {
      logger.addLog({
        usageType: UsageType.error,
        message: "No code found for user",
        obj: { userId: user.id },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "No verification code found. Please request a new code." },
        { status: 404 },
      );
    }

    // Check if the code is expired
    if (emailAuthCode.expires < new Date()) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Code expired",
        obj: { userId: user.id, expires: emailAuthCode.expires },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Verification code has expired. Please request a new code." },
        { status: 400 },
      );
    }

    // Check if the code is already used
    if (emailAuthCode.used) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Code already used",
        obj: { userId: user.id },
      });
      logger.printAll();
      return NextResponse.json(
        {
          error:
            "Verification code has already been used. Please request a new code.",
        },
        { status: 400 },
      );
    }

    // Check if the code matches
    if (emailAuthCode.code !== code) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid code",
        obj: { userId: user.id, providedCode: code },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid verification code. Please try again." },
        { status: 400 },
      );
    }

    // Mark the code as used
    await prisma.emailAuthCode.update({
      where: { id: emailAuthCode.id },
      data: { used: true },
    });

    // Complete the account creation by setting emailVerified
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: new Date(),
      },
    });

    logger.addLog({
      usageType: UsageType.info,
      message: "Signup code verified successfully",
      obj: { userId: user.id, email: user.email },
    });

    // Redirect to the callback URL with user information
    const redirectUrl = `/api/auth/callback/email-code?email=${encodeURIComponent(
      user.email!,
    )}&userId=${user.id}&codeVerified=true&callbackUrl=${encodeURIComponent(callbackUrl)}`;

    logger.addLog({
      usageType: UsageType.info,
      message: "Redirecting to callback",
      obj: { redirectUrl },
    });
    logger.printAll();

    return NextResponse.json({ success: true, redirectUrl });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error verifying signup email code",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to verify code" },
      { status: 500 },
    );
  }
}
