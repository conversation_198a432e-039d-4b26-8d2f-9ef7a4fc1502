import { NextRequest, NextResponse } from "next/server";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "passkey-signin", "n/a");

/**
 * GET handler for passkey sign-in
 * This endpoint processes the token from the verification endpoint and redirects to NextAuth
 */
export async function GET(request: NextRequest) {
  try {
    // Log the request details
    logger.addLog({
      usageType: UsageType.debug,
      message: "Passkey sign-in endpoint called",
      obj: {
        url: request.url,
        method: request.method,
      },
    });

    // Get the token from the URL
    const url = new URL(request.url);
    const token = url.searchParams.get("token");

    if (!token) {
      logger.addLog({
        usageType: UsageType.error,
        message: "No token provided",
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=NoToken", request.url),
      );
    }

    // Decode the token
    let tokenData;
    try {
      const decodedToken = Buffer.from(token, "base64").toString();
      tokenData = JSON.parse(decodedToken);

      logger.addLog({
        usageType: UsageType.debug,
        message: "Token decoded successfully",
        obj: {
          id: tokenData.id,
          email: tokenData.email,
          exp: new Date(tokenData.exp * 1000).toISOString(),
        },
      });
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error decoding token",
        obj: { error },
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=InvalidToken", request.url),
      );
    }

    // Verify token expiration
    const now = Math.floor(Date.now() / 1000);
    if (tokenData.exp < now) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Token expired",
        obj: {
          expiration: new Date(tokenData.exp * 1000).toISOString(),
          now: new Date(now * 1000).toISOString(),
        },
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=TokenExpired", request.url),
      );
    }

    // Verify the user exists
    const prisma = bypassedPrisma as unknown as PrismaClient;
    const user = await prisma.$queryRaw<
      Array<{
        id: string;
        email: string;
        name: string | null;
        emailVerified: Date | null;
        image: string | null;
      }>
    >`
      SELECT id, email, name, email_verified as "emailVerified", image
      FROM "user" WHERE id = ${tokenData.id} AND email = ${tokenData.email}
    `.then(
      (
        results: Array<{
          id: string;
          email: string;
          name: string | null;
          emailVerified: Date | null;
          image: string | null;
        }>,
      ) => results[0] || null,
    );

    if (!user) {
      logger.addLog({
        usageType: UsageType.error,
        message: "User not found",
        obj: { userId: tokenData.id, email: tokenData.email },
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=UserNotFound", request.url),
      );
    }

    // Import the signIn function directly from NextAuth
    const { signIn } = await import("@/auth");

    logger.addLog({
      usageType: UsageType.info,
      message: "Calling NextAuth signIn function directly",
      obj: { userId: user.id, email: user.email },
    });

    try {
      // Import the auth adapter and create a session directly
      const { authAdapter } = await import("@/lib/auth-helpers");
      const { randomBytes } = await import("crypto");

      // Generate a session token that matches the database constraints (32 chars)
      const sessionToken = randomBytes(16).toString("hex"); // 32 character hex string

      // Calculate session expiry (default to 30 days if not specified)
      const sessionMaxAge = Number(
        process.env.SESSION_DURATION || 30 * 24 * 60 * 60,
      ); // 30 days in seconds
      const sessionExpiry = new Date(Date.now() + sessionMaxAge * 1000); // Convert to milliseconds

      logger.addLog({
        usageType: UsageType.info,
        message: "Creating session directly with adapter",
        obj: {
          userId: user.id,
          sessionToken: sessionToken.substring(0, 5) + "...", // Log only part of the token for security
          expires: sessionExpiry,
        },
      });

      // Create the session using the adapter
      let session = null;
      try {
        if (authAdapter?.createSession) {
          logger.addLog({
            usageType: UsageType.debug,
            message: "Calling authAdapter.createSession",
            obj: {
              adapter: !!authAdapter,
              createSessionFn: !!authAdapter?.createSession,
              sessionData: {
                sessionToken,
                userId: user.id,
                expires: sessionExpiry,
              },
            },
          });

          // Create the session using the adapter
          session = await authAdapter.createSession({
            sessionToken,
            userId: user.id,
            expires: sessionExpiry,
          });

          logger.addLog({
            usageType: UsageType.debug,
            message: "Result from authAdapter.createSession",
            obj: {
              success: !!session,
              sessionData: session
                ? {
                    sessionToken: session.sessionToken.substring(0, 5) + "...",
                    userId: session.userId,
                    expires: session.expires,
                  }
                : null,
            },
          });

          // Verify the session was created in the database
          try {
            // Use the same Prisma instance as before
            const dbSession = await prisma.session.findFirst({
              where: { sessionToken },
            });

            logger.addLog({
              usageType: UsageType.debug,
              message: "Verified session in database",
              obj: {
                exists: !!dbSession,
                sessionData: dbSession
                  ? {
                      id: dbSession.id,
                      sessionToken:
                        dbSession.sessionToken.substring(0, 5) + "...",
                      userId: dbSession.userId,
                      expires: dbSession.expires,
                    }
                  : null,
              },
            });
          } catch (verifyError) {
            logger.addLog({
              usageType: UsageType.error,
              message: "Error verifying session in database",
              obj: { error: verifyError },
            });
          }
        } else {
          logger.addLog({
            usageType: UsageType.error,
            message: "authAdapter.createSession is not available",
            obj: { adapter: !!authAdapter },
          });
        }
      } catch (sessionError) {
        logger.addLog({
          usageType: UsageType.error,
          message: "Error creating session with adapter",
          obj: { error: sessionError },
        });
      }

      logger.addLog({
        usageType: UsageType.info,
        message: "Session created successfully",
        obj: {
          sessionId: session?.sessionToken.substring(0, 5) + "...",
          userId: session?.userId,
          expires: session?.expires,
        },
      });

      // Import the auth cookie names to ensure consistency
      const { authCookieNames } = await import("@/lib/auth-helpers");

      // Use the same cookie name as defined in auth-helpers.ts
      const cookieName = authCookieNames.sessionToken;

      const cookieValue = sessionToken;
      const cookieExpires = sessionExpiry;

      logger.addLog({
        usageType: UsageType.debug,
        message: "Using session cookie name from auth-helpers",
        obj: { cookieName },
      });

      // Get the callback URL from the query parameters if available
      const callbackUrl = url.searchParams.get("callbackUrl") || "/en";

      logger.addLog({
        usageType: UsageType.info,
        message: "Redirecting after successful authentication",
        obj: { callbackUrl },
      });

      // Set the session cookie
      const response = NextResponse.redirect(new URL(callbackUrl, request.url));
      response.cookies.set({
        name: cookieName,
        value: cookieValue,
        expires: cookieExpires,
        path: "/",
        httpOnly: true,
        secure:
          process.env.NEXTAUTH_URL?.startsWith("https://") ||
          !!process.env.VERCEL,
        sameSite: "lax",
      });

      // Add a debug header to help troubleshoot
      response.headers.set("X-Auth-Debug", "Session created and cookie set");

      logger.addLog({
        usageType: UsageType.info,
        message: "Session cookie set",
        obj: {
          cookieName,
          cookieExpires,
        },
      });
      logger.printAll();

      return response;
    } catch (authError) {
      logger.addLog({
        usageType: UsageType.error,
        message: "NextAuth signIn error",
        obj: { error: authError },
      });
      logger.printAll();

      // Redirect to the error page
      return NextResponse.redirect(
        new URL("/en/auth-error?error=SignInFailed", request.url),
      );
    }
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error during passkey sign-in process",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.redirect(
      new URL("/en/auth-error?error=ServerError", request.url),
    );
  }
}
