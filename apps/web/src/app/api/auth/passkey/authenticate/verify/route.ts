export const runtime = "nodejs";

import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { ChallengeType } from "@/types/mfa-types";
import {
  webAuthnService,
  passKeyService,
} from "@/services/server/webauthn/webauthn-service";
import { signIn } from "@/auth";
import { bypassedPrisma } from "@/lib/prisma";
import type { PrismaClient } from "@askinfosec/database";

// Initialize logger
const logger = new LogManager(
  LogLevel.debug,
  "passkey-api",
  "authenticate-verify",
);

// Request validation schema
const requestSchema = z.object({
  email: z.string().email().optional(), // Now optional
  credentialId: z.string(),
  authenticatorData: z.string(), // Base64 encoded
  clientDataJSON: z.string(), // Base64 encoded
  signature: z.string(), // Base64 encoded
  challenge: z.string(), // The challenge that was used for authentication
  orgId: z.string().optional(), // Organization ID (optional in request)
});

/**
 * POST handler for verifying PassKey authentication
 * This endpoint verifies the authentication response from the WebAuthn API
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request",
        obj: { error: result.error.format() },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const {
      email,
      credentialId,
      authenticatorData,
      clientDataJSON,
      signature,
      challenge,
      orgId = "default",
    } = result.data;

    // Validate organization ID
    if (!orgId) {
      logger.addLog({
        usageType: UsageType.warning,
        message: "Organization ID not provided, using default",
      });
    }

    let user = null;
    if (email) {
      // Normal, email-based authentication
      user = await passKeyService.verifyAuthentication(
        email,
        credentialId,
        Buffer.from(authenticatorData, "base64"),
        Buffer.from(clientDataJSON, "base64"),
        Buffer.from(signature, "base64"),
        challenge,
        orgId,
      );
    } else {
      // Discoverable authentication (no email)
      user = await passKeyService.verifyDiscoverableAuthentication(
        credentialId,
        Buffer.from(authenticatorData, "base64"),
        Buffer.from(clientDataJSON, "base64"),
        Buffer.from(signature, "base64"),
        challenge,
        orgId,
      );
    }

    if (!user) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Authentication failed",
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 },
      );
    }

    // Sign in the user
    try {
      // Type guard: user can be string or object
      if (typeof user === "string") {
        logger.addLog({
          usageType: UsageType.error,
          message:
            "Unexpected user type: string returned from authentication. Cannot sign in.",
          obj: { user },
        });
        logger.printAll();
        return NextResponse.json(
          {
            error:
              "Internal error: invalid user object returned from authentication",
          },
          { status: 500 },
        );
      }

      // Get the full user data from the database to include all required fields
      const prisma = bypassedPrisma as unknown as PrismaClient;
      const userData = await prisma.$queryRaw<
        Array<{
          id: string;
          email: string;
          name: string | null;
          emailVerified: Date | null;
          image: string | null;
        }>
      >`
        SELECT id, email, name, email_verified as "emailVerified", image
        FROM "user" WHERE id = ${user.id}
      `.then((results) => results[0] || null);

      if (!userData) {
        logger.addLog({
          usageType: UsageType.error,
          message: "Failed to retrieve user data after authentication",
          obj: { userId: user.id },
        });
        logger.printAll();
        return NextResponse.json(
          { error: "User data not found after authentication" },
          { status: 500 },
        );
      }

      // Generate a JWT-like token with the user information
      // This token will be used to authenticate the user with NextAuth
      const authToken = Buffer.from(
        JSON.stringify({
          id: userData.id,
          email: userData.email,
          name: userData.name || null,
          emailVerified: userData.emailVerified || null,
          image: userData.image || null,
          credentialId,
          // Add an expiration time (5 minutes)
          exp: Math.floor(Date.now() / 1000) + 5 * 60,
          // Add a nonce to prevent replay attacks
          nonce: Math.random().toString(36).substring(2, 15),
        }),
      ).toString("base64");

      logger.addLog({
        usageType: UsageType.info,
        message: "PassKey authentication successful",
        obj: {
          userId: user.id,
          email: user.email,
          tokenCreated: true,
        },
      });

      // Get the callback URL from the request headers if available
      const callbackUrl = request.headers.get("x-callback-url") || "/en";

      logger.addLog({
        usageType: UsageType.info,
        message: "Preparing authentication response with callback URL",
        obj: { callbackUrl },
      });

      // Return success response with the auth token
      // The client will use this token to authenticate with NextAuth
      return NextResponse.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        },
        // Include the auth token and a direct sign-in URL
        // Explicitly use the credentials-passkey provider
        authToken,
        signInUrl: `/api/auth/signin/passkey?token=${encodeURIComponent(authToken)}&callbackUrl=${encodeURIComponent(callbackUrl)}`,
      });
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Error signing in user after PassKey authentication",
        obj: { error, userId: typeof user === "string" ? user : user.id },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Authentication successful but failed to sign in" },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error verifying PassKey authentication",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to verify authentication" },
      { status: 500 },
    );
  }
}
