import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { transport } from "@/lib/email";
import { render } from "@react-email/render";
import EmailCode from "../../../../../../emails/email-code";

// Force Node.js runtime for email sending
export const runtime = "nodejs";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "email-code", "request");

// Rate limiting constants
const COOLDOWN_PERIOD_MS = 5 * 60 * 1000; // 5 minutes in milliseconds

// Request validation schema
const requestSchema = z.object({
  email: z.string().email(),
});

/**
 * Generates a random 6-digit code
 * @returns A 6-digit numeric code as a string
 */
function generateCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * POST handler for requesting an email verification code
 * Generates a code, stores it in the database, and sends it via email
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request",
        obj: { error: result.error.format() },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const { email } = result.data;
    const prisma = bypassedPrisma as unknown as PrismaClient;

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      logger.addLog({
        usageType: UsageType.error,
        message: "User not found",
        obj: { email },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "No account found with this email address" },
        { status: 404 },
      );
    }

    // Generate a 6-digit code
    const code = generateCode();

    // Set expiry time (10 minutes from now)
    const expires = new Date(Date.now() + 10 * 60 * 1000);

    // Check if there's an existing code for this user
    const existingCode = await prisma.emailAuthCode.findFirst({
      where: { userId: user.id, used: false, expires: { gt: new Date() } },
    });

    // Check if there's a recent code that was created within the cooldown period
    if (existingCode) {
      const createdAt = existingCode.createdAt;
      const now = new Date();
      const timeSinceLastCode = now.getTime() - createdAt.getTime();

      // If the code was created less than 5 minutes ago, enforce rate limiting
      if (timeSinceLastCode < COOLDOWN_PERIOD_MS) {
        const remainingTimeMs = COOLDOWN_PERIOD_MS - timeSinceLastCode;
        const remainingTimeSec = Math.ceil(remainingTimeMs / 1000);
        const remainingTimeMin = Math.ceil(remainingTimeMs / 60000);

        logger.addLog({
          usageType: UsageType.warning,
          message: "Rate limit exceeded for email code request",
          obj: {
            userId: user.id,
            email: user.email,
            timeSinceLastCode,
            cooldownPeriod: COOLDOWN_PERIOD_MS,
            remainingTimeMs,
          },
        });
        logger.printAll();

        return NextResponse.json(
          {
            error: "Too many verification code requests",
            message: "Please wait before requesting another code",
            retryAfter: remainingTimeSec, // seconds until retry is allowed
            retryAfterMinutes: remainingTimeMin, // minutes for user-friendly display
          },
          {
            status: 429, // Too Many Requests
            headers: {
              "Retry-After": remainingTimeSec.toString(), // Standard header for rate limiting
            },
          },
        );
      }

      // Update the existing code if we're past the cooldown period
      await prisma.emailAuthCode.update({
        where: { id: existingCode.id },
        data: {
          code,
          expires,
          used: false,
          type: "authentication", // Add type for authentication
        },
      });
    } else {
      // Create a new code if no existing code was found
      await prisma.emailAuthCode.create({
        data: {
          userId: user.id,
          code,
          expires,
          used: false,
          type: "authentication", // Add type for authentication
        },
      });
    }

    // Send the code via email
    const emailHtml = await render(
      EmailCode({
        code,
        email: user.email!,
        name: user.name || user.email!.split("@")[0],
      }),
    );

    await transport.sendMail({
      to: user.email!,
      from: process.env.EMAIL_FROM!,
      subject: "Your AskInfosec Verification Code",
      html: emailHtml,
      text: `Your verification code is: ${code}. It will expire in 10 minutes.`,
    });

    logger.addLog({
      usageType: UsageType.info,
      message: "Email code sent successfully",
      obj: { userId: user.id, email: user.email },
    });
    logger.printAll();

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error sending email code",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to send verification code" },
      { status: 500 },
    );
  }
}
