import { NextRequest, NextResponse } from "next/server";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";
import { bypassedPrisma } from "@/lib/prisma";
import { createSession } from "@/lib/auth-api";

// Use the extended Prisma client directly
const prisma = bypassedPrisma;

// Initialize logger
const logger = new LogManager(LogLevel.debug, "session-redirect", "n/a");

/**
 * GET handler for session redirect
 * This endpoint creates a session for a user and redirects to the specified URL
 * It follows the same pattern as the email-code and passkey callback routes
 */
export async function GET(request: NextRequest) {
  try {
    // Get parameters from query parameters
    const searchParams = request.nextUrl.searchParams;
    const email = searchParams.get("email");
    const userId = searchParams.get("userId");
    const callbackUrl = searchParams.get("callbackUrl") || "/en";

    logger.addLog({
      usageType: UsageType.debug,
      message: "Session redirect request received",
      obj: { email, userId, callbackUrl },
    });

    // Validate required parameters
    if (!email || !userId) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Missing user credentials in session redirect",
        obj: { email, userId },
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=InvalidCredentials", request.url),
      );
    }

    // Verify the user exists in the database
    logger.addLog({
      usageType: UsageType.debug,
      message: "Verifying user in database",
      obj: { userId, email },
    });

    try {
      // Use Prisma client with proper typing
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
        },
      });

      if (!user || user.email !== email) {
        logger.addLog({
          usageType: UsageType.error,
          message: "User not found or email mismatch in session redirect",
          obj: { userId, email, foundEmail: user?.email },
        });
        logger.printAll();
        return NextResponse.redirect(
          new URL("/en/auth-error?error=UserNotFound", request.url),
        );
      }

      logger.addLog({
        usageType: UsageType.info,
        message: "User verified successfully",
        obj: { userId, email },
      });

      // Create a session for the user using the reusable function
      logger.addLog({
        usageType: UsageType.info,
        message: "Creating session using createSession helper",
        obj: { userId, email, callbackUrl },
      });

      const { response, error } = await createSession({
        userId,
        email,
        callbackUrl,
        logger, // Pass the existing logger to maintain log context
      });

      if (error) {
        logger.addLog({
          usageType: UsageType.error,
          message: "Error from createSession helper",
          obj: { error },
        });
        logger.printAll();
        return NextResponse.redirect(
          new URL("/en/auth-error?error=SessionCreationFailed", request.url),
        );
      }

      logger.addLog({
        usageType: UsageType.info,
        message: "Session created successfully via helper",
        obj: { callbackUrl },
      });
      logger.printAll();

      return response;
    } catch (error) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Database error when verifying user",
        obj: { error, userId, email },
      });
      logger.printAll();
      return NextResponse.redirect(
        new URL("/en/auth-error?error=DatabaseError", request.url),
      );
    }
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error in session redirect",
      obj: { error },
    });
    return NextResponse.redirect(
      new URL("/en/auth-error?error=ServerError", request.url),
    );
  }
}
