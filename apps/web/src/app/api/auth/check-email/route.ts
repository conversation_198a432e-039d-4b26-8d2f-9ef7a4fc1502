import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { bypassedPrisma } from "@/lib/prisma";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";

// Force Node.js runtime for database operations
export const runtime = "nodejs";

// Initialize logger
const logger = new LogManager(LogLevel.debug, "auth", "check-email");

// Request validation schema
const requestSchema = z.object({
  email: z.string().email(),
});

/**
 * POST handler for checking if an email exists
 * Verifies if the email is associated with an active user account
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const result = requestSchema.safeParse(body);

    if (!result.success) {
      logger.addLog({
        usageType: UsageType.error,
        message: "Invalid request",
        obj: { error: result.error.format() },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 },
      );
    }

    const { email } = result.data;
    const prisma = bypassedPrisma;

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
      },
    });

    // If no user is found, return a 404 error
    if (!user) {
      logger.addLog({
        usageType: UsageType.info,
        message: "User not found",
        obj: { email },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "No account found with this email address" },
        { status: 404 },
      );
    }

    // Check if the email is verified
    if (!user.emailVerified) {
      logger.addLog({
        usageType: UsageType.info,
        message: "Email not verified",
        obj: { email },
      });
      logger.printAll();
      return NextResponse.json(
        { error: "Email address not verified" },
        { status: 400 },
      );
    }

    // Return success if the email exists and is verified
    logger.addLog({
      usageType: UsageType.info,
      message: "Email check successful",
      obj: { email },
    });
    logger.printAll();

    return NextResponse.json({
      success: true,
      exists: true,
      verified: true,
    });
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error checking email",
      obj: { error },
    });
    logger.printAll();
    return NextResponse.json(
      { error: "Failed to check email" },
      { status: 500 },
    );
  }
}
