import { verifyAuthRS<PERSON> } from "@/lib/security/server";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { jwtService } from "@/lib/jwt";
import AcceptRedirectVendor from "./accept-redirect-vendor";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

interface AcceptInviteVendorPageProps {
  searchParams: Promise<{
    token?: string;
    questionnaireId?: string;
    callbackUrl?: string;
  }>;
}

export default async function AcceptInviteVendorPage({
  searchParams,
}: AcceptInviteVendorPageProps) {
  const resolvedSearchParams = await searchParams;
  const session = await verifyAuthRSC();

  let token, questionnaireId;

  // First try to get params directly
  if (resolvedSearchParams?.token && resolvedSearchParams?.questionnaireId) {
    token = resolvedSearchParams.token;
    questionnaireId = resolvedSearchParams.questionnaireId;
  }
  // Then try to parse from callbackUrl
  else if (resolvedSearchParams?.callbackUrl) {
    try {
      // Double decode since the callbackUrl is double encoded
      const decodedCallback = decodeURIComponent(
        decodeURIComponent(resolvedSearchParams.callbackUrl),
      );

      // Remove the leading slash if present
      const cleanPath = decodedCallback.startsWith("/")
        ? decodedCallback.slice(1)
        : decodedCallback;
      const urlParams = new URLSearchParams(cleanPath.split("?")[1]);

      token = urlParams.get("token");
      questionnaireId = urlParams.get("questionnaireId");
    } catch (error) {
      console.error("Error parsing callback URL:", error);
    }
  }

  if (!token || !questionnaireId) {
    throw Error("Invalid or missing invitation parameters");
  }

  const decoded = await jwtService.decode<{ id: string }>({
    token: token,
  });

  if (!decoded) {
    throw Error("Invalid invitation token");
  }

  const invite = await prisma.invite.findFirst({ where: { id: decoded.id } });

  if (!invite) {
    throw Error("The invite link maybe expired, or already used.");
  }

  // If invite is for an email, confirm it belongs to the session user's email
  if (invite.email && invite.email !== session.user.email) {
    throw Error("The invite link is not for this user session.");
  }

  return (
    <AcceptRedirectVendor
      id={invite.id}
      orgId={invite.organization_id}
      roleId={invite.role_id}
      userId={session.user.id}
      email={invite.email ?? ""}
      image={invite.image}
      questionnaireId={questionnaireId}
      createdBy={invite.created_by}
    />
  );
}
