import { Organization } from "@askinfosec/database";

interface ExtendedOrganization extends Organization {
  members?: any[];
}

export function calculateTrainingProgress(
  completedTrainings: number,
  totalRequiredTrainings: number,
): number {
  if (totalRequiredTrainings === 0) return 100;
  return Math.round((completedTrainings / totalRequiredTrainings) * 100);
}

export function calculateSecurityHealth(
  organization: ExtendedOrganization,
  accessibleModules: any[],
  risks: any[],
  controls: any[],
  incidents: any[],
): number {
  // Policy Implementation (25%)
  const policyScore =
    Math.min((accessibleModules.length / 20) * 100, 100) * 0.25;

  // Team Security (25%)
  const teamScore = (organization.members?.length ?? 0) > 0 ? 100 * 0.25 : 0;

  // Risk Management (25%)
  const riskScore = calculateRiskScore(risks, controls) * 0.25;

  // Incident Management (25%)
  const incidentScore = calculateIncidentScore(incidents) * 0.25;

  return Math.round(policyScore + teamScore + riskScore + incidentScore);
}

function calculateRiskScore(risks: any[], controls: any[]): number {
  const totalRisks = risks.length;
  if (totalRisks === 0) return 100;

  const mitigatedRisks = risks.filter(
    (risk) => risk.treatment === "mitigated",
  ).length;
  const controlsImplemented = controls.filter(
    (control) => control.status === "implemented",
  ).length;

  return Math.round(
    (mitigatedRisks / totalRisks) * 50 +
      (controlsImplemented / controls.length) * 50,
  );
}

function calculateIncidentScore(incidents: any[]): number {
  if (incidents.length === 0) return 100;

  const resolvedIncidents = incidents.filter(
    (incident) => incident.status === "resolved",
  ).length;

  return Math.round((resolvedIncidents / incidents.length) * 100);
}

export function getComplianceStatus(
  frameworks: any[],
  controls: any[],
): { compliant: number; partial: number; nonCompliant: number } {
  const totalControls = controls.length;
  if (totalControls === 0) {
    return { compliant: 0, partial: 0, nonCompliant: 0 };
  }

  const compliant = controls.filter(
    (control) => control.status === "compliant",
  ).length;
  const partial = controls.filter(
    (control) => control.status === "partial",
  ).length;
  const nonCompliant = totalControls - compliant - partial;

  return {
    compliant: Math.round((compliant / totalControls) * 100),
    partial: Math.round((partial / totalControls) * 100),
    nonCompliant: Math.round((nonCompliant / totalControls) * 100),
  };
}

export function getRiskTrends(risks: any[], timeframe: number = 90): any[] {
  const now = new Date();
  const startDate = new Date(now.setDate(now.getDate() - timeframe));

  return risks
    .filter((risk) => new Date(risk.created_at) >= startDate)
    .reduce((acc: any[], risk) => {
      const date = new Date(risk.created_at).toISOString().split("T")[0];
      const existing = acc.find((item) => item.date === date);

      if (existing) {
        existing.count += 1;
      } else {
        acc.push({ date, count: 1 });
      }

      return acc;
    }, [])
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

export interface QuestionnaireMetrics {
  vendorAssessments: {
    total: number;
    completed: number;
    inProgress: number;
  };
  securityQuestionnaires: {
    total: number;
    internal: number;
    external: number;
  };
  responseRate: {
    percentage: number;
    averageResponseTime: number;
  };
}

export function calculateQuestionnaireMetrics(
  questionnaires: any[],
  timeframe: number = 30,
): QuestionnaireMetrics {
  const now = new Date();
  const startDate = new Date(now.setDate(now.getDate() - timeframe));

  // Filter questionnaires within timeframe
  const recentQuestionnaires = questionnaires.filter(
    (q) => new Date(q.created_at) >= startDate,
  );

  // Calculate vendor assessments
  const vendorAssessments = recentQuestionnaires.filter((q) => q.vendor_id);
  const completedAssessments = vendorAssessments.filter(
    (q) => q.status === "completed",
  );

  // Calculate security questionnaires
  const securityQuestionnaires = recentQuestionnaires.filter(
    (q) => !q.vendor_id,
  );
  const internalQuestionnaires = securityQuestionnaires.filter(
    (q) => q.is_assessor,
  );

  // Calculate response rate and time
  const completedQuestionnaires = recentQuestionnaires.filter(
    (q) => q.status === "completed",
  );
  const responseRate =
    recentQuestionnaires.length > 0
      ? (completedQuestionnaires.length / recentQuestionnaires.length) * 100
      : 0;

  // Calculate average response time (in days)
  const responseTimes = completedQuestionnaires.map((q) => {
    const created = new Date(q.created_at);
    const completed = q.updated_at ? new Date(q.updated_at) : new Date();
    return (completed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24); // Convert to days
  });
  const averageResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 0;

  return {
    vendorAssessments: {
      total: vendorAssessments.length,
      completed: completedAssessments.length,
      inProgress: vendorAssessments.length - completedAssessments.length,
    },
    securityQuestionnaires: {
      total: securityQuestionnaires.length,
      internal: internalQuestionnaires.length,
      external: securityQuestionnaires.length - internalQuestionnaires.length,
    },
    responseRate: {
      percentage: Math.round(responseRate),
      averageResponseTime: Math.round(averageResponseTime * 10) / 10, // Round to 1 decimal place
    },
  };
}
