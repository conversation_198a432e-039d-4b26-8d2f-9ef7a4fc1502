"use server";

import { action } from "@/lib/safe-action";
import { JsonValue } from "@askinfosec/database";
import { z } from "zod";
import { guardedPrisma } from "@/lib/prisma";
import { GetAllPoliciesFromUploadedDocuments } from "../../../policies/actions/policy-actions";

// Define the schema for input validation
const getPoliciesSchema = z.object({
  orgId: z.string(),
  userId: z.string(),
});

// Define the input type based on the schema
type GetPoliciesInput = z.infer<typeof getPoliciesSchema>;

export const getPolicies = action
  .metadata({ actionName: "getPolicies" })
  .schema(getPoliciesSchema)
  .action(async ({ parsedInput }: { parsedInput: GetPoliciesInput }) => {
    const { orgId, userId } = parsedInput;
    const db = guardedPrisma({ orgId, userId, checkMembership: false });

    try {
      const policies = await db.policy.findMany({
        where: { organization_id: orgId },
        select: { id: true, name: true, content: true },
      });

      return { success: true, data: policies };
    } catch (error) {
      console.error("Error fetching policies:", error);
      return { success: false, message: "Failed to fetch policies." };
    }
  });

export async function getTrainingGroupPolicies(
  orgId: string,
  userId: string,
  trainingGroupId: string,
) {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const trainingGroup = await db.trainingGroup.findUnique({
    where: { organization_id: orgId, id: trainingGroupId },
  });
  const policyIds = trainingGroup?.policy_ids;
  if (
    trainingGroup?.settings &&
    JSON.parse(JSON.stringify(trainingGroup?.settings))
      ?.policy_document_source === "internal"
  ) {
    if (policyIds && policyIds.length > 0) {
      const internalPoliciesData = await db.policy.findMany({
        where: { id: { in: policyIds } },
        select: { id: true, name: true, content: true, description: true },
      });
      const internalPolicies = internalPoliciesData.map((policy) => ({
        id: policy.id,
        name: policy.name,
        content: policy.content || "",
        description: policy.description || "",
      }));
      return internalPolicies;
    }
  } else {
    const files = await db.file.findMany({
      where: {
        organization_id: orgId,
        document_type: "policy",
        id: { in: policyIds },
      },
    });
    const uploadedPolicies = files.map((file) => ({
      id: file.id,
      name: file.name,
      content: "",
      description: "",
    }));
    return uploadedPolicies;
  }
}
