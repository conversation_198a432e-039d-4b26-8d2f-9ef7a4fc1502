import { verifyAuthAndOrgAccessRSC } from "@/lib/security/server";
import {
  <PERSON><PERSON>les,
  FileText,
  Shield,
  AlertCircle,
  BookOpen,
  ChartPie,
  CheckSquare,
} from "lucide-react";
import { ComplianceProgessChart } from "./charts/compliance-progress";
import { RequireAction } from "./charts/require-action";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "~/src/components/ui";
import { getAllCommonMetrics } from "~/src/services/server/analytics/common";
import ComplianceMetrics from "./charts/compliance-metric";
import { getAllPolicyMetrics } from "~/src/services/server/analytics/policy";
import { getAllControlMetrics } from "~/src/services/server/analytics/control";
import { getAllEvidenceTaskMetrics } from "~/src/services/server/analytics/evidence-task";
import { getOrg } from "~/src/services/server/organization";
import { redirect } from "next/navigation";
import { verifyModuleAccess } from "~/src/lib/security/module-access";
import { FrameworksTab } from "./components/frameworks-tab";
import { ControlsTab } from "./components/controls-tab";
import { TasksTab } from "./components/tasks-tab";
import { PrismaClient } from "@askinfosec/database";
import { getFrameworkDetail } from "../framework/[id]/actions/framework-requirement-actions";
import { GetAllFrameworks } from "../framework/actions/framework-actions";
import { GenerateReportButton } from "./components/generate-report-button";
import ContentBodyPageHeader from "~/src/components/header/content-body-page-header";
import { tenantGuardedPrisma } from "@/lib/prisma";

export interface CompliancePageProp {
  params: Promise<{
    org: string;
    lang: string;
  }>;
}

export default async function CompliancePage({ params }: CompliancePageProp) {
  const resolvedParams = await params;
  const { session } = await verifyAuthAndOrgAccessRSC(resolvedParams.org);
  const prisma = tenantGuardedPrisma(resolvedParams.org);
  // Then verify module access
  await verifyModuleAccess(
    resolvedParams.org,
    session.user.id,
    "control_management",
  );
  const [
    currentOrg,
    policies,
    controls,
    evidenceTasks,
    policySpecficMetrics,
    controlSpecificMetrics,
    evidenceTaskSpecificMetrics,
    controlsData,
    tasksData,
    frameworksData,
    frameworkRequirements,
  ] = await Promise.all([
    getOrg({ userId: session.user.id, orgId: resolvedParams.org }),
    getAllCommonMetrics({
      orgId: resolvedParams.org,
      userId: session.user.id,
      entity: "Policy",
    }),
    getAllCommonMetrics({
      orgId: resolvedParams.org,
      userId: session.user.id,
      entity: "Control",
    }),
    getAllCommonMetrics({
      orgId: resolvedParams.org,
      userId: session.user.id,
      entity: "EvidenceTask",
    }),
    getAllPolicyMetrics({ orgId: resolvedParams.org, userId: session.user.id }),
    getAllControlMetrics({
      orgId: resolvedParams.org,
      userId: session.user.id,
    }),
    getAllEvidenceTaskMetrics({
      orgId: resolvedParams.org,
      userId: session.user.id,
    }),
    prisma.control.findMany({
      where: { organization_id: resolvedParams.org },
      select: {
        id: true,
        name: true,
        status: true,
        framework: {
          select: {
            name: true,
          },
        },
        assigned: true,
        updated_at: true,
      },
      orderBy: {
        updated_at: "desc",
      },
    }),
    prisma.evidenceTask.findMany({
      where: { organization_id: resolvedParams.org },
      select: {
        id: true,
        name: true,
        status: true,
        assigned: true,
        control: {
          select: {
            code: true,
          },
        },
        created_at: true,
        updated_at: true,
        interval: true,
        evidence_attachment: {
          select: {
            id: true,
            created_at: true,
          },
          orderBy: {
            created_at: "desc",
          },
          take: 1,
        },
      },
      orderBy: {
        updated_at: "desc",
      },
    }),
    GetAllFrameworks({
      orgId: resolvedParams.org,
      userId: session.user.id,
    }).then(JSON.parse),
    GetAllFrameworks({ orgId: resolvedParams.org, userId: session.user.id })
      .then(JSON.parse)
      .then((frameworks) =>
        Promise.all(
          frameworks.map(async (framework: any) => ({
            frameworkKey: framework.code,
            requirements: JSON.parse(
              await getFrameworkDetail({
                orgId: resolvedParams.org,
                userId: session.user.id,
                frameworkId: framework.id,
              }),
            ).requirements,
          })),
        ),
      ),
  ]);
  const complianceStatusMap = controls.status.reduce((acc: any, item: any) => {
    acc[item.status] = item._count._all;
    return acc;
  }, {});
  const ComplianceAssignedChartData = [
    { job: "Policy", assigned: 0, approver: 0 },
    { job: "Control", assigned: 0, approver: 0 },
    { job: "Evidence Task", assigned: 0, approver: 0 },
  ];
  const updatedChartData = ComplianceAssignedChartData.map((item) => {
    if (item.job === "Control") {
      return {
        ...item,
        assigned: controls.userAssigned,
        approver: controls.userApprover,
      };
    }
    if (item.job === "Policy") {
      return {
        ...item,
        assigned: policies.userAssigned,
        approver: policies.userApprover,
      };
    }
    if (item.job === "Evidence Task") {
      return {
        ...item,
        assigned: evidenceTasks.userAssigned,
        approver: evidenceTasks.userApprover,
      };
    }
    return item;
  });
  if (currentOrg.current_user_role === "user") {
    return redirect(`/${resolvedParams.org}/kb`);
  }

  return (
    <>
      <ContentBodyPageHeader>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
            Compliance
          </h1>
          <p className="text-sm text-muted-foreground">
            Monitor compliance status
          </p>
        </div>
      </ContentBodyPageHeader>
      <div className="container mx-auto px-3 sm:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <GenerateReportButton
            data={{
              controls,
              policies,
              evidenceTasks,
              complianceStatusMap,
              orgName: currentOrg.name,
            }}
          />
        </div>

        {/* Quick Stats - 2 columns on mobile */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3 sm:pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">
                Controls
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3">
              <div className="text-xl sm:text-2xl font-bold">
                {controls.total}
              </div>
              <p className="text-[10px] sm:text-xs text-muted-foreground">
                {controls.userAssigned} assigned
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Policies</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{policies.total}</div>
              <p className="text-xs text-muted-foreground">
                {policies.userAssigned} need review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Evidence Tasks
              </CardTitle>
              <Sparkles className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{evidenceTasks.total}</div>
              <p className="text-xs text-muted-foreground">
                {evidenceTasks.userAssigned} pending
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Critical Issues
              </CardTitle>
              <AlertCircle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {complianceStatusMap.failed || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Require immediate attention
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs - Scrollable on mobile with icons */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="w-full justify-start overflow-x-auto no-scrollbar">
            <TabsTrigger value="overview" className="min-w-fit">
              <div className="flex items-center gap-2">
                <ChartPie className="h-4 w-4" />
                <span className="hidden sm:inline">Overview</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="controls" className="min-w-fit">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span className="hidden sm:inline">Controls</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="tasks" className="min-w-fit">
              <div className="flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                <span className="hidden sm:inline">Tasks</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="frameworks" className="min-w-fit">
              <div className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                <span className="hidden sm:inline">Frameworks</span>
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Compliance Progress</CardTitle>
                  <CardDescription>
                    Overall compliance status breakdown
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ComplianceProgessChart
                    complianceStatusMap={complianceStatusMap}
                  />
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Required Actions</CardTitle>
                  <CardDescription>
                    Tasks requiring your attention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RequireAction chartData={updatedChartData} />
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Detailed Metrics</CardTitle>
                <CardDescription>
                  Comprehensive compliance metrics and statistics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ComplianceMetrics
                  policies={policies}
                  controls={controls}
                  evidenceTasks={evidenceTasks}
                  policySpecficMetrics={policySpecficMetrics}
                  controlSpecificMetrics={controlSpecificMetrics}
                  evidenceTaskSpecificMetrics={evidenceTaskSpecificMetrics}
                  org={resolvedParams.org}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="frameworks">
            <FrameworksTab
              frameworks={{
                data: frameworksData,
                requirements: frameworkRequirements,
                metrics: {
                  controls: {
                    total: controls.total,
                    completed: controls.status
                      .filter((item: any) => item.status === "compliant")
                      .reduce(
                        (acc: any, curr: any) => acc + curr._count._all,
                        0,
                      ),
                  },
                  policies: {
                    total: policies.total,
                    completed: policies.status
                      .filter((item: any) => item.status === "published")
                      .reduce(
                        (acc: any, curr: any) => acc + curr._count._all,
                        0,
                      ),
                  },
                  evidences: {
                    total: evidenceTasks.total,
                    completed: evidenceTasks.status
                      .filter((item: any) => item.status === "published")
                      .reduce(
                        (acc: any, curr: any) => acc + curr._count._all,
                        0,
                      ),
                  },
                },
              }}
              org={resolvedParams.org}
            />
          </TabsContent>

          <TabsContent value="controls">
            <ControlsTab
              controls={{
                ...controls,
                data: controlsData,
              }}
              org={resolvedParams.org}
            />
          </TabsContent>

          <TabsContent value="tasks">
            <TasksTab
              tasks={{
                ...evidenceTasks,
                data: tasksData,
              }}
              org={resolvedParams.org}
            />
          </TabsContent>

          {/* Add content for other tabs */}
        </Tabs>
      </div>
    </>
  );
}
