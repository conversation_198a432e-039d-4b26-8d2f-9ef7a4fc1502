"use server";

import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { getFileTypeInfo } from "./file-browser-utils";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

// File browser filter schema
const FileBrowserFilterSchema = z.object({
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  sortBy: z
    .enum(["name", "created_at", "updated_at", "file_type"])
    .default("created_at"),
  sortDirection: z.enum(["asc", "desc"]).default("desc"),
  fileTypes: z.array(z.string()).optional(),
});

export type FileBrowserFilter = z.infer<typeof FileBrowserFilterSchema>;

/**
 * Get all file types mapping
 */
export async function getFileTypesMapping() {
  // Import from utils to avoid direct export in server component
  const { FILE_TYPES, DEFAULT_FILE_ICON } = await import(
    "./file-browser-utils"
  );
  return { FILE_TYPES, DEFAULT_FILE_ICON };
}

/**
 * Get all files for an organization with filtering and pagination
 */
export async function getFilesByOrgId(
  orgId: string,
  filterOptions: FileBrowserFilter,
) {
  const options = FileBrowserFilterSchema.parse(filterOptions);
  const { page, pageSize, search, sortBy, sortDirection, fileTypes } = options;

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Build the where clause
  const whereClause: any = {
    organization_id: orgId,
  };

  // Add search filter
  if (search) {
    whereClause.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { path: { contains: search, mode: "insensitive" } },
    ];
  }

  // Add file type filter
  if (fileTypes && fileTypes.length > 0) {
    whereClause.OR = (whereClause.OR || []).concat(
      fileTypes.map((type) => ({ path: { endsWith: type } })),
    );
  }

  // Get total count for pagination
  const totalCount = await prisma.file.count({
    where: whereClause,
  });

  // Get files
  const files = await prisma.file.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      path: true,
      file_type: true,
      created_at: true,
      updated_at: true,
      created_by: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      buffer_file: false, // Don't fetch the buffer for the listing
    },
    orderBy: {
      [sortBy]: sortDirection,
    },
    skip,
    take: pageSize,
  });

  // Process files to add file type info
  const processedFiles = files.map((file) => {
    const fileTypeInfo = getFileTypeInfo(file.path || file.name || "");
    return {
      ...file,
      file_type_info: fileTypeInfo,
    };
  });

  // Return files with pagination info
  return {
    files: processedFiles,
    pagination: {
      total: totalCount,
      page,
      pageSize,
      pageCount: Math.ceil(totalCount / pageSize),
      hasMore: skip + files.length < totalCount,
    },
  };
}

/**
 * Get a single file by ID
 */
export async function getFileById(fileId: string, orgId: string) {
  const file = await prisma.file.findFirst({
    where: {
      id: fileId,
      organization_id: orgId,
    },
    select: {
      id: true,
      name: true,
      path: true,
      file_type: true,
      created_at: true,
      updated_at: true,
      created_by: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      buffer_file: false,
    },
  });

  if (!file) {
    return null;
  }

  // Get file type info
  const fileTypeInfo = getFileTypeInfo(file.path || file.name || "");

  return {
    ...file,
    file_type_info: fileTypeInfo,
  };
}

/**
 * Delete files by ID
 */
export async function deleteFiles(fileIds: string[], orgId: string) {
  try {
    // Delete the files
    const result = await prisma.file.deleteMany({
      where: {
        id: { in: fileIds },
        organization_id: orgId,
      },
    });

    // Revalidate the documents page
    revalidatePath(`/[lang]/(private)/[org]/docs`);

    return {
      success: true,
      count: result.count,
    };
  } catch (error) {
    console.error("Error deleting files:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
