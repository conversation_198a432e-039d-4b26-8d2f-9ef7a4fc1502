"use server";

import { Metada<PERSON> } from "next";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

interface DocumentPageProps {
  params: {
    org: string;
    id: string;
  };
}

export async function generateMetadata({
  params,
}: DocumentPageProps): Promise<Metadata> {
  const file = await prisma.file.findFirst({
    where: {
      id: params.id,
      organization_id: params.org,
    },
    select: {
      name: true,
    },
  });

  return {
    title: file?.name || "Document",
    description: `View and manage document: ${file?.name || ""}`,
  };
}
