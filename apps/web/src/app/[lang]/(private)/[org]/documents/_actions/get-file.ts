"use server";
import { bypassedPrisma, tenantGuardedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

export interface GetFileParams {
  fileId: string;
  orgId: string;
}

export interface GetFileResult {
  buffer: string;
  fileName: string;
  fileType: string;
  extension: string;
}

export async function GetFile({
  fileId,
  orgId,
}: GetFileParams): Promise<GetFileResult> {
  const file = await tenantGuardedPrisma(orgId).file.findUniqueOrThrow({
    where: { id: fileId },
    select: {
      buffer_file: true,
      content: true,
      name: true,
      path: true,
      document_type: true,
    },
  });

  if (!file.buffer_file && !file.content) {
    throw new Error("No content found in the document");
  }

  let buffer: Buffer;
  let extension = file.path?.split(".").pop() || "";

  if (file.buffer_file) {
    buffer = Buffer.from(file.buffer_file);
    extension = file.path?.split(".").pop() || "";
    if (extension === "csv") {
      extension = "txt";
    }
  } else if (file.content) {
    // Convert HTML content to plain text
    const plainText = file.content
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<\/p>/gi, "\n\n")
      .replace(/<[^>]*>/g, "")
      .replace(/&nbsp;/g, " ")
      .trim();

    buffer = Buffer.from(plainText);
    extension = "txt";
  } else {
    throw new Error("Document has no content");
  }

  // Convert Buffer to base64 string
  const base64Buffer = buffer.toString("base64");

  console.log("GetFile(): base64Buffer ->", base64Buffer);

  return {
    buffer: base64Buffer,
    fileName: file.name,
    fileType: file.document_type || "",
    extension,
  };
}

export async function getFileById(fileId: string, orgId: string) {
  const file = await prisma.file.findFirst({
    where: {
      id: fileId,
      organization_id: orgId,
    },
    select: {
      id: true,
      name: true,
      path: true,
      file_type: true,
      created_at: true,
      updated_at: true,
      created_by: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
  });
  return file;
}
