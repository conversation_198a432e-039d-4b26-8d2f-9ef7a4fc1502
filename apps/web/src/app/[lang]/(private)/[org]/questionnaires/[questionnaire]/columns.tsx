"use client";

import AvatarIcon from "@/components/avatar-icon";
import { IAvatar } from "@/types/org/avatar";
import { Question } from "@askinfosec/database";
import type { ColumnDef } from "@tanstack/react-table";
import { sanitizeText } from "~/src/lib/utils/string";

export const columns: ColumnDef<Question, unknown>[] = [
  {
    accessorKey: "id",
  },
  {
    accessorKey: "question",
    header: "Question",
    cell: ({ row }) => (
      <div className="max-w-[500px] min-w-[350px] text-left">
        <pre className="text-wrap whitespace-pre-wrap font-sans">
          {sanitizeText(row.getValue("question"))}
        </pre>
      </div>
    ),
    minSize: 350,
    size: 400,
    enableSorting: true,
  },
  {
    accessorKey: "answer_detail",
    header: "Answer",
    cell: ({ row }) => (
      <div className="max-w-[500px] min-w-[350px] text-left px-2.5">
        <pre className="text-wrap whitespace-pre-wrap font-sans">
          {row.getValue("answer_detail") !== null &&
            sanitizeText(row.getValue("answer_detail"))}
        </pre>
      </div>
    ),
    minSize: 350,
    size: 400,
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const statusColors: Record<string, string> = {
        open: "bg-yellow-100 text-yellow-800",
        in_review: "bg-blue-100 text-blue-800",
        verified: "bg-green-100 text-green-800",
      };

      return (
        <div className="flex gap-2">
          <span
            className={`px-2.5 py-0.5 rounded-full text-sm font-medium ${statusColors[status] ?? ""}`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        </div>
      );
    },
    enableSorting: true,
    minSize: 100,
    maxSize: 120,
  },
  {
    accessorKey: "assigned",
    header: "Assigned",
    cell: ({ row }) => {
      if (row.getValue("assigned")) {
        const { email, image } = row.getValue("assigned") as IAvatar;
        return (
          <div className="flex items-center gap-2">
            <AvatarIcon image={image} email={email} />
            {/* <span className="text-sm text-muted-foreground">{name || email}</span> */}
          </div>
        );
      }
    },
    enableSorting: true,
    minSize: 100,
    maxSize: 120,
  },
];
