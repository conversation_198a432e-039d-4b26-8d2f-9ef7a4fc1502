import { verifyAuthAndOrgAccessRSC } from "@/lib/security/server";
import { getQuestionnaire } from "@/services/server/questionnaires";
import { FileTypeResult, fileTypeFromBuffer } from "file-type";
import { getAllScopes } from "@/services/server/scope";
import * as Excel from "exceljs";
import QuestionnaireDetailsPage from "./questionnaire-details-page";
import {
  getInviteVendor,
  getQuestionnaireRef,
} from "@/services/server/invite/invite";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";
import { getVendors } from "@/services/server/vendor";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

interface QuestionnairePageProp {
  params: Promise<{
    org: string;
    questionnaire: string;
    vendorId: string;
    email: string;
  }>;
}

export default async function QuestionnairePage({
  params,
}: QuestionnairePageProp) {
  const resolvedParams = await params;
  const { session } = await verifyAuthAndOrgAccessRSC(resolvedParams.org);
  const data = await getQuestionnaire(
    resolvedParams.org,
    resolvedParams.questionnaire,
  );
  const vendors = await getVendors(resolvedParams.org);
  const ref = await prisma.questionnaire.findFirst({
    where: {
      id: data.questionnaire.reference.split(",")[0],
    },
    include: {
      created: {
        select: {
          name: true,
        },
      },
      organization: {
        select: {
          company_name: true,
        },
      },
    },
  });
  const invite = data.questionnaire.id
    ? await getInviteVendor(data.questionnaire.id)
    : null;
  const reference = await getQuestionnaireRef(resolvedParams.questionnaire);
  const verifiedCount = data.questions.filter(
    (question) => question.status === "verified",
  ).length;

  let fileType: FileTypeResult | undefined;
  if (data.questionnaire.original_file !== null) {
    // const fileBuffer = Buffer.from(data.questionnaire.original_file);
    fileType = await fileTypeFromBuffer(
      data.questionnaire.original_file as Buffer,
    );
  }

  const scopes = await getAllScopes({
    org: resolvedParams.org,
  });

  let worksheets: {
    id: string;
    name: string;
  }[] = [];

  if (fileType && fileType.ext === "xlsx") {
    const workbook = new Excel.Workbook();
    const distinctWorksheetIds = data.questions
      .map((q) => q.mapping_worksheet_id)
      .filter((id, index, array) => array.indexOf(id) === index);

    const arrayBuffer = data.questionnaire
      .original_file as unknown as ArrayBuffer;
    const uint8Array = new Uint8Array(arrayBuffer);
    const buffer = Buffer.from(uint8Array);

    const wb = await workbook.xlsx.load(buffer as unknown as Excel.Buffer);
    const allWorksheets = wb.worksheets.map((ws) => {
      return { id: ws.id.toString(), name: ws.name };
    });
    worksheets = allWorksheets.filter((q) =>
      distinctWorksheetIds.includes(q.id),
    );
    // worksheets.push({ id: "0", name: "Common" });
  }
  return (
    <div className="w-full">
      <QuestionnaireDetailsPage
        fileType={fileType ? fileType : undefined}
        scopes={scopes}
        org={resolvedParams.org}
        qid={resolvedParams.questionnaire}
        worksheets={worksheets}
        user={session.user.id}
        invite={invite}
        reference={reference}
        orgName={ref?.organization?.company_name || ""}
        defaultName={ref?.created?.name || ""}
        vendors={vendors}
      />
    </div>
  );
}
