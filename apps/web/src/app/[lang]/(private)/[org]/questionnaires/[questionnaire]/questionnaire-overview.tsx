"use client";

import * as Excel from "exceljs";
import { FileTypeResult } from "file-type";
import { saveAs } from "file-saver";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import EditQuestionnaire from "./edit-questionnaire";
import { useForm } from "react-hook-form";
import { Step1FormSchemaValues, step1FormSchema } from "../upload/model";
import { zodResolver } from "@hookform/resolvers/zod";
import { Scope } from "@/models/scope";
import {
  Dispatch,
  SetStateAction,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from "react";
import { SpinnerBasic } from "@/components/spinner";
import {
  BookOpenCheck,
  Info,
  ListRestart,
  PanelLeftClose,
  PanelRightClose,
} from "lucide-react";
import ProgressCircular from "@/components/progress-circle";
import { getCsrfToken } from "@/services/client/helpers";
import { useCurrentOrg } from "@/services/client/organization";
import { useRouter } from "next/navigation";
import {
  useAskAI,
  useGenerateCopyAnswerLink,
} from "@/services/client/questionnaire";
import { MemberSelector, MemberUser } from "@/models/member";
import { Vendor } from "@/models/vendor";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogHeader,
} from "@/components/ui";
import InviteSent from "@/components/icons/questionnaire/invite-sent";
import { JsonValue } from "@askinfosec/database";
import { useToast } from "@/components/ui/use-toast";
import AvatarIcon from "@/components/avatar-icon";

export interface Questionnaire {
  id: string;
  name: string;
  created_at: Date;
  scope: Scope;
  due_date: Date | null;
  vendor: { id: string; vendor_name: string; email: string } | null;
  status: string;
  created_by: string;
  organization_id: string;
  assigned: Assigned[];
  original_file: Buffer | null;
  reference: string;
  is_assessor: boolean;
  other_data?: any;
}

interface Assigned {
  user_d?: string;
  email?: string | null;
}

export interface Question {
  id: string;
  questionnaire_id: string;
  question: string;
  answer_yes_no_na?: string | null;
  answer_detail: string | null;
  ai_generated_answer: string | null;
  ai_generated_answer_history: JsonValue;
  reference: string | null;
  mapping_question: string | null;
  mapping_yes: string | null;
  mapping_no: string | null;
  mapping_na: string | null;
  mapping_yes_no_na: string | null;
  mapping_answer_detail: string | null;
  mapping_question_id: string | null;
  mapping_worksheet_id: string | null;
  mapping_category: string | null;
  ref_kb: string[];
  ref_docs: string[];
  status: "open" | "in_review" | "verified";
  organization_id: string;
  answer_verified_by_id: string | null;
  answer_verified_date: Date | null;
  created_at: Date;
  created_by_id: string;
  last_update_by_id: string;
  updated_at: Date;
  assigned_id: string | null;
  count_evidence_collected: number;
  scope: Scope;
}

interface Props {
  questionnaire: Questionnaire;
  questionsVerifiedCount: number;
  questions: Question[];
  fileType?: FileTypeResult;
  scopes: Scope[];
  vendors: Vendor[];
  members:
    | {
        data: [MemberUser[], MemberSelector[]];
      }
    | undefined;
  user: string;
  invite: {
    email: string | null;
    expired_at: Date;
    reference: string | null;
  } | null;
  reference: string | null;
  setLoading: React.Dispatch<SetStateAction<boolean>>;
  orgName: string | null;
  orgId: string;
  batchQuestionButtonTriggered: boolean;
  setBatchQuestionButtonTriggered: Dispatch<SetStateAction<boolean>>;
  currentQuestionIndex: number;
  defaultName: string | null;
  userId: string;
  canAddorUpdateVendor: boolean;
}

function QuestionnaireOverview({
  questionnaire,
  questionsVerifiedCount,
  questions,
  fileType,
  scopes,
  members,
  vendors,
  user,
  invite,
  reference,
  setLoading,
  orgName,
  defaultName,
  batchQuestionButtonTriggered,
  setBatchQuestionButtonTriggered,
  currentQuestionIndex,
  orgId,
  userId,
  canAddorUpdateVendor,
}: Props) {
  const [isPending, startTransition] = useTransition();
  const currentProgress = Math.abs(
    (questionsVerifiedCount / questions.length) * 100,
  ).toFixed(2);
  const [toggleOverview, setToggleOverview] = useState(false);
  const { org: currentOrg } = useCurrentOrg(questionnaire.organization_id);
  const router = useRouter();
  const generateLinkSWR = useGenerateCopyAnswerLink(questionnaire.id);
  const [isSendingInvite, setIsSendingInvite] = useState(false);
  const [inviteSent, setInviteSent] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const totalUnansweredQuestions = questions.filter(
    (question) =>
      !question.answer_detail || question.answer_detail.trim() === "",
  );
  const { toast } = useToast();

  const assigned = questionnaire.assigned.map(
    (collaborator) => collaborator.user_d,
  );
  const handleDownloadQACSV = () => {
    const csvString = [
      ["Question", "Answer"],
      ...questions.map((item) => [
        `"${item.question}"`,
        `"${item.answer_detail !== null ? item.answer_detail : ""}"`,
      ]),
    ]
      .map((e) => e.join(","))
      .join("\n");
    const blob = new Blob([csvString], { type: "text/csv" });
    saveAs(blob, `${questionnaire.name}.csv`);
  };

  const handleDownloadOriginal = async () => {
    const workbook = await getWorkBook();
    questions.map((q) => {
      const ws = workbook.getWorksheet(parseInt(q.mapping_worksheet_id!));
      if (!ws) throw new Error("Worksheet is undefined!");

      ws.getCell(q.mapping_answer_detail!).value = q.answer_detail;
      ws.getCell(q.mapping_question!).value = q.question;
    });

    if (fileType && fileType.ext === "xlsx") {
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: fileType.mime });
      saveAs(blob, `${questionnaire.name}.xlsx`);
    } else {
      const buffer = await workbook.csv.writeBuffer();
      const blob = new Blob([buffer], { type: "text/csv" });
      saveAs(blob, `${questionnaire.name}.csv`);
    }
  };

  const getWorkBook = async () => {
    const workbook = new Excel.Workbook();
    const arrayBuffer = new Uint8Array(questionnaire.original_file as any)
      .buffer;
    if (fileType && fileType.ext === "xlsx") {
      return await workbook.xlsx.load(arrayBuffer);
    }
    // Always use browser-compatible fallback for CSV
    const csvString = new TextDecoder().decode(arrayBuffer);
    const rows = csvString.split("\n").map((row) => row.split(","));
    const ws = workbook.addWorksheet("Sheet1");
    rows.forEach((row) => ws.addRow(row));
    return workbook;
  };

  // Copied from upload questionnaire step 1
  const formStep1 = useForm<Step1FormSchemaValues>({
    resolver: zodResolver(step1FormSchema),
    defaultValues: {
      questionnaire_name: questionnaire.name,
      due_date: questionnaire.due_date || undefined,
      assigned: questionnaire.assigned.map((c) => c.user_d),
      vendor: questionnaire.vendor?.id,
      scope_id: questionnaire.scope?.id,
    },
  });

  const handleInviteVendor = async () => {
    if (!questionnaire.vendor) {
      toast({
        description: "Please assign a vendor first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSendingInvite(true);
      const response = await fetch(`/api/org/${orgId}/invites/vendor`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-csrf-token": getCsrfToken(),
        },
        body: JSON.stringify({
          email: questionnaire.vendor.email,
          vendor: questionnaire.vendor.id,
          questionnaireId: questionnaire.id,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send vendor invite");
      }

      setInviteSent(true);
      setIsDialogOpen(true);
      toast({
        description: "Vendor invitation sent successfully",
      });
    } catch (error) {
      console.error("Error sending vendor invite:", error);
      toast({
        description: "Failed to send vendor invitation",
        variant: "destructive",
      });
    } finally {
      setIsSendingInvite(false);
    }
  };

  const shouldDisableInviteVendor = (): boolean | undefined => {
    // The condition when to disable the button are:
    // 1. In higher order, if theres an existing invite and the invite is not yet expired, disable the button
    // 2. If #1 passed, check if reference is still empty.
    // 3. Then by default, do not disable the button, by return false as the last line.
    if (
      invite &&
      new Date(invite.expired_at) > new Date() &&
      questionnaire.id === invite.reference
    ) {
      return true;
    }
    if (!invite && reference !== null) return true;
    return false;
  };

  const handleBatchAnswer = async () => {
    setBatchQuestionButtonTriggered(true);
  };

  // Add detailed logging to debug the "Send Answers" button visibility
  useEffect(() => {
    const hasVendorScope = questionnaire.scope?.scope_type === "vendor";
    const hasVendorOverride =
      questionnaire.other_data &&
      typeof questionnaire.other_data === "object" &&
      questionnaire.other_data.override_scope_type === "vendor";
    const isNotAssessor = !questionnaire.is_assessor;
    const shouldShowButton =
      (hasVendorScope || hasVendorOverride) && isNotAssessor;

    console.log("Questionnaire data:", {
      scope_type: questionnaire.scope?.scope_type,
      is_assessor: questionnaire.is_assessor,
      reference: questionnaire.reference,
      other_data: questionnaire.other_data,
      hasVendorScope,
      hasVendorOverride,
      isNotAssessor,
      shouldShowButton,
      vendor: questionnaire.vendor,
    });

    if (!shouldShowButton) {
      console.log(
        `Send Answers button is HIDDEN because: ${!hasVendorScope ? "Not vendor scope" : ""}${!hasVendorOverride ? " No vendor override" : ""}${!isNotAssessor ? " Is assessor" : ""}`,
      );
    } else {
      console.log("Send Answers button SHOULD be visible");
    }
  }, [questionnaire]);

  if (!toggleOverview) {
    return (
      <Card className="w-full max-w-[380px] shadow-md flex flex-col">
        <CardHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">Overview</CardTitle>
            {/* <PanelRightClose
              size={20}
              className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors"
              onClick={() => setToggleOverview(true)}
            /> */}
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Verification Progress</span>
              <span className="text-sm text-muted-foreground">
                {questionsVerifiedCount}/{questions.length}
              </span>
            </div>
            <Progress value={Number(currentProgress)} className="h-2" />
            <p className="text-sm text-muted-foreground text-right">
              {currentProgress}% Verified
            </p>
          </div>

          <Separator />
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-3">
            {/* // TODO: Temporary disable generating batch answer. We need to find solution on how we can do this in the background. */}
            <Button
              className="w-full"
              disabled={
                batchQuestionButtonTriggered || !assigned.includes(user) || true
              }
              onClick={handleBatchAnswer}
            >
              {batchQuestionButtonTriggered ? (
                <div className="flex items-center gap-2">
                  <SpinnerBasic />
                  <span>
                    Generating {currentQuestionIndex}/
                    {totalUnansweredQuestions.length}...
                  </span>
                </div>
              ) : (
                "Generate Batch Answers"
              )}
            </Button>
          </div>

          <div className="space-y-6">
            <InfoSection
              title="Questionnaire Name"
              value={formStep1.getValues("questionnaire_name")}
              action={
                <EditQuestionnaire
                  orgId={questionnaire.organization_id}
                  scopes={scopes}
                  form={formStep1}
                  members={members?.data[1]}
                  vendors={vendors}
                  questionnaireId={questionnaire.id}
                  direction={
                    questionnaire.scope?.scope_type as "incoming" | "outgoing"
                  }
                  questionnaire={questionnaire}
                  orgName={orgName || "'"}
                  defaultName={defaultName || ""}
                  batchQuestionButtonTriggered={batchQuestionButtonTriggered}
                  userId={user}
                  shouldDisableVendor={shouldDisableInviteVendor()}
                />
              }
            />

            <InfoSection
              title="Due Date"
              value={
                formStep1.getValues("due_date") &&
                format(formStep1.getValues("due_date") || "", "MMMM d, yyyy")
              }
            />

            {questionnaire.scope?.scope_type !== "vendor" ? (
              <>
                <InfoSection
                  title="Assigned"
                  value={questionnaire.assigned.map((c: any, index) => (
                    <span
                      key={`id-${c.user_d}-${index}`}
                      className="mb-1 inline-block mr-2"
                      title={c.email}
                    >
                      <AvatarIcon email={c.email} className="h-7 w-7" />
                      <span className="sr-only">{c.email}</span>
                    </span>
                  ))}
                />
              </>
            ) : null}

            <InfoSection
              title="Vendor"
              value={
                questionnaire.reference === "" ? (
                  questionnaire.vendor?.email ? (
                    <span title={questionnaire.vendor.email}>
                      <AvatarIcon
                        email={questionnaire.vendor.email}
                        className="h-7 w-7"
                      />
                    </span>
                  ) : null
                ) : (
                  <>
                    {orgName}
                    <CardDescription className="flex items-center mt-1">
                      <Info size={15} className="mr-1" />
                      <span className="text-xs">
                        Vendor is not yet added. Click edit questionnaire to
                        add.
                      </span>
                    </CardDescription>
                  </>
                )
              }
            />

            {questionnaire.scope?.scope_type !== "vendor" ? (
              <>
                <InfoSection
                  title="Status"
                  value={questionnaire.status}
                  action={
                    <Button
                      className="rounded-lg text-xs bg-transparent border-muted-foreground"
                      variant="secondary"
                      disabled={
                        isPending ||
                        currentOrg?.current_user_role !== "owner" ||
                        batchQuestionButtonTriggered === true
                      }
                      onClick={() => {
                        const toStatus =
                          questionnaire.status.toLowerCase() !== "completed"
                            ? "completed"
                            : "open";
                        startTransition(() => {
                          const myFunc = async () => {
                            await fetch(
                              `/api/org/${questionnaire.organization_id}/questionnaire`,
                              {
                                method: "PATCH",
                                body: JSON.stringify({
                                  status: toStatus,
                                  questionnaireId: questionnaire.id,
                                }),
                                headers: { "x-csrf-token": getCsrfToken() },
                              },
                            );
                            router.refresh();
                          };
                          myFunc();
                        });
                        setLoading(true);
                      }}
                    >
                      {isPending ? (
                        "Please wait..."
                      ) : questionnaire.status.toLowerCase() !== "completed" ? (
                        <BookOpenCheck className="w-5 h-5" />
                      ) : (
                        <ListRestart className="w-5 h-5" />
                      )}
                    </Button>
                  }
                />
              </>
            ) : null}

            <InfoSection
              title="Created"
              value={format(questionnaire.created_at, "MMMM d, yyyy")}
            />

            {questionnaire.scope?.scope_type !== "vendor" ? (
              <>
                <InfoSection title="Scope" value={questionnaire.scope?.name} />
              </>
            ) : null}
          </div>

          {questionnaire.scope?.scope_type !== "vendor" ? (
            <>
              <Separator />
              <InfoSection
                title="Total Evidence Collected"
                value={questions
                  .map((q) => q)
                  .reduce(
                    (sum, current) => sum + current.count_evidence_collected,
                    0,
                  )}
              />
            </>
          ) : null}

          <Separator />
          <div className="space-y-3 pt-6">
            <Button
              variant="secondary"
              className="w-full"
              disabled={batchQuestionButtonTriggered}
              onClick={handleDownloadQACSV}
            >
              Download CSV
            </Button>

            {questionnaire.scope?.scope_type !== "vendor" ? (
              <>
                <Button
                  className="w-full"
                  disabled={
                    questionnaire.original_file === null ||
                    batchQuestionButtonTriggered
                      ? true
                      : false
                  }
                  onClick={() => handleDownloadOriginal()}
                >
                  Download to original format
                </Button>
              </>
            ) : null}

            {/* For vendors to send back completed questionnaire to assessor */}
            {(questionnaire.scope?.scope_type === "vendor" ||
              (questionnaire.other_data &&
                questionnaire.other_data.override_scope_type === "vendor")) &&
            !questionnaire.is_assessor ? (
              <Button
                data-test="send-answer"
                className={
                  currentProgress === "100.00"
                    ? ""
                    : "opacity-50 pointer-events-none"
                }
                onClick={() => handleInviteVendor()}
                disabled={isPending || inviteSent}
              >
                {isPending ? <SpinnerBasic /> : "Send Answers"}
              </Button>
            ) : null}
          </div>
        </CardContent>
        <Dialog open={isDialogOpen} onOpenChange={() => setIsDialogOpen(false)}>
          <DialogContent>
            <DialogHeader>
              <div className="flex justify-center pb-3">
                <InviteSent className="text-foreground" />
              </div>
              <DialogTitle className="flex justify-center font-bold">
                {questionnaire.reference === ""
                  ? "Invite sent!"
                  : "Answers sent!"}
              </DialogTitle>
            </DialogHeader>
            <DialogDescription className="text-center">
              {questionnaire.reference === ""
                ? "The link is on its way to the vendor. Option to send another invite will be reactivated once the current link expires."
                : "Your answers are on their way and will be received by the recipient anytime soon. Thank you for your cooperation!"}
            </DialogDescription>
            <Button onClick={() => setIsDialogOpen(false)}>OK</Button>
          </DialogContent>
        </Dialog>
      </Card>
    );
  } else {
    return (
      <Card className="flex flex-col items-center p-4 min-w-[80px] shadow-md">
        <PanelLeftClose
          size={20}
          className="mb-6 cursor-pointer text-muted-foreground hover:text-foreground transition-colors"
          onClick={() => setToggleOverview(false)}
        />
        <ProgressCircular
          currentProgress={questionsVerifiedCount}
          totalNum={questions.length}
        />
      </Card>
    );
  }
}

const InfoSection = ({
  title,
  value,
  action,
}: {
  title: string;
  value: any;
  action?: React.ReactNode;
}) => (
  <div className="space-y-1.5">
    <div className="flex items-center justify-between">
      <span className="text-sm text-muted-foreground">{title}</span>
      {action}
    </div>
    <p className="font-medium">{value}</p>
  </div>
);

export default QuestionnaireOverview;
