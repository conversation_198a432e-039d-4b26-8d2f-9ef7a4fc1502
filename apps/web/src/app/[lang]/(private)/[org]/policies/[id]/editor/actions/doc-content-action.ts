"use server";

import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { guardedPrisma, tenantGuardedPrisma } from "@/lib/prisma";
import { Policy } from "@/types/prisma-kysely";
import { Prisma } from "@askinfosec/database";
import { getUserProfile } from "~/src/services/server/user-profile";

export interface GetDocumentContentParams {
  id: string;
  orgId: string;
}

export async function GetDocumentContent({
  id,
  orgId,
}: GetDocumentContentParams): Promise<string> {
  const db = tenantGuardedPrisma(orgId);
  const policy = await db.policy.findFirst({
    where: {
      id: id,
      organization_id: orgId,
    },
    select: {
      content: true,
      content_change_history: true,
      file_id: true,
      name: true,
    },
  });
  if (policy !== null && policy.content === null && policy.file_id !== null) {
    // Return the content if not empty. Else check if there is link to document file and get the content of that file.
    const fileData = await db.file.findUnique({
      where: { id: policy.file_id, organization_id: orgId },
      select: { name: true, path: true, buffer_file: true },
    });
    const fileArray = new Uint8Array(fileData?.buffer_file ?? new Uint8Array());
    // Ideally, the mimeType is a column in the file database table.
    const filePath = (fileData?.path || fileData?.name || "").toLowerCase();
    const mimeType = filePath.endsWith(".pdf")
      ? "application/pdf"
      : filePath.endsWith(".docx")
        ? "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        : "";
    const fileBlob = new Blob([fileArray], {
      type: mimeType,
    });
    const file = new File([fileBlob], fileData?.name!, { type: mimeType });
    if (mimeType === "application/pdf") {
      // TODO: Make it work so that we can also read the PDF file
      // Load PDF content
      // pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://mozilla.github.io/pdf.js/build/pdf.worker.js';
      // const pdfDoc = await pdfjsLib.PDFDocument.load(file);
      // const pages = await pdfDoc.getPages();
      // const content = await pages[0].getTextContent();
      // return JSON.stringify({ content: content.items.map(item => item.str).join(''), contentCurrentVersion: null });
      return JSON.stringify({
        status: "error",
        message: "PDF is not supported currently.",
        content: "",
        contentCurrentVersion: null,
        policyName: policy.name,
      });
    } else if (
      mimeType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      // Load DOCX content
      const loader = new DocxLoader(file);
      const c = await loader.load();
      return JSON.stringify({
        content: c[0].pageContent || "",
        contentCurrentVersion: null,
        policyName: policy.name,
      });
    } else {
      // Handle other file types
      return JSON.stringify({
        status: "error",
        message: "Other file types is not supported",
        content: "",
        contentCurrentVersion: null,
        policyName: policy.name,
      });
    }
  } else {
    return policy === null
      ? JSON.stringify({ status: "error", message: "No policy found." }) // Unlikely this should happen because calling this from editor page, then there must be policy
      : JSON.stringify({
          content: policy.content,
          policyName: policy.name,
          contentCurrentVersion: await getCurrentVersion(
            policy.content_change_history,
            orgId,
          ),
        });
  }
}

export interface UpdateDocumentContentParams {
  orgId: string;
  userId: string;
  content: string;
  id: string;
}

export type ContentChangeHistoryItem = {
  version: string;
  updatedAt: Date;
  updatedById: string;
  updatedBy: string;
  content?: string; // Make content optional
};

export async function UpdateDocumentContent({
  orgId,
  userId,
  content,
  id,
}: UpdateDocumentContentParams): Promise<string> {
  const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
  try {
    const policy = await db.policy.findUnique({
      where: {
        id,
      },
    });
    if (!policy) {
      return JSON.stringify({ status: "error", message: "Policy not found" });
    }
    let currentVersion = "0.1";
    let contentChangeHistory = null;
    if (policy.content_change_history) {
      const parsedHistory = JSON.parse(
        JSON.stringify(policy.content_change_history),
      ) as Array<{
        version: string;
        updatedAt: string;
        updatedById: string;
        content?: string;
      }>;
      contentChangeHistory = parsedHistory.map((item) => ({
        ...item,
        updatedAt: new Date(item.updatedAt),
      }));
      if (contentChangeHistory && contentChangeHistory.length > 0) {
        const lastItem = contentChangeHistory[contentChangeHistory.length - 1];
        currentVersion = lastItem.version;
        const newVersion = currentVersion.split(".").map(Number);
        if (policy.status === "published") {
          newVersion[0]++;
          newVersion[1] = 0;
        } else {
          newVersion[1]++;
        }
        currentVersion = `${newVersion.join(".")}`;
      }
    }

    let newContentChangeHistoryItem: ContentChangeHistoryItem = {
      version: currentVersion,
      content,
      updatedAt: new Date(),
      updatedById: userId,
      updatedBy: (await getUserProfile({ userId }))?.email || "",
    };

    await db.policy.update({
      where: {
        id,
      },
      data: {
        content,
        content_change_history: [
          ...(contentChangeHistory || []).slice(-20),
          newContentChangeHistoryItem,
        ],
      },
    });
    let mostCurrentVersion = await getCurrentVersion(
      policy.content_change_history,
      orgId,
    );
    return JSON.stringify({
      status: "success",
      mostCurrentVersion: mostCurrentVersion,
    });
  } catch (error) {
    return JSON.stringify({
      status: "error",
      message: "Error updating document content",
    });
  }
}

export interface GetPolicyDetailsParams {
  orgId: string;
  userId: string;
  policyId: string;
}

export async function GetPolicyDetails({
  orgId,
  userId,
  policyId,
}: GetPolicyDetailsParams): Promise<Policy | null> {
  const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
  try {
    const policy = await db.policy.findUnique({
      where: {
        id: policyId,
      },
      select: {
        id: true,
        name: true,
        description: true,
        assigned: true,
        category: true,
        created_by_id: true,
        created_at: true,
        updated_by_id: true,
        updated_at: true,
        organization_id: true,
        status: true,
        content_change_history: true,
        approver: true,
        tags: true,
        file_id: true,
        interval: true,
      },
    });

    if (!policy) {
      return null;
    }

    let mostCurrentVersion = await getCurrentVersion(
      policy.content_change_history,
      orgId,
    );

    const policyDetails = {
      id: policy.id,
      name: policy.name,
      description: policy.description,
      assigned: policy.assigned,
      approver: policy.approver,
      category: policy.category,
      createdById: policy.created_by_id,
      createdAt: policy.created_at,
      updatedById: policy.updated_by_id,
      updatedAt: policy.updated_at,
      organizationId: policy.organization_id,
      status: policy.status,
      content: null,
      contentChangeHistory: mostCurrentVersion,
      tags: policy.tags,
      fileId: policy.file_id,
      interval: policy.interval,
    } as unknown as Policy;

    return policyDetails;
  } catch (error) {
    return null;
  }
}

export async function getCurrentVersion(
  content_change_history: Prisma.JsonValue | null,
  orgId: string,
): Promise<ContentChangeHistoryItem | null> {
  if (
    content_change_history !== null &&
    content_change_history !== undefined &&
    Array.isArray(content_change_history) &&
    content_change_history.length > 0
  ) {
    const parsedHistory = JSON.parse(
      JSON.stringify(content_change_history),
    ) as Array<{
      version: string;
      updatedAt: string;
      updatedById: string;
      content?: string;
      updatedBy: string;
    }>;
    const lastItem = parsedHistory[parsedHistory.length - 1];
    if (lastItem && lastItem.updatedById) {
      const db = tenantGuardedPrisma(orgId);
      const user = await db.user.findUnique({
        where: {
          id: lastItem.updatedById,
        },
        select: {
          id: true,
        },
      });

      return {
        version: lastItem.version,
        updatedAt: new Date(lastItem.updatedAt),
        updatedById: user?.id || lastItem.updatedById,
        content: lastItem.content,
        updatedBy: lastItem.updatedBy || "",
      };
    }
  }
  return null;
}
