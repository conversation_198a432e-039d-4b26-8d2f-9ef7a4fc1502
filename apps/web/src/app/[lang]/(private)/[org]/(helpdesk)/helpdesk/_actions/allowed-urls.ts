"use server";

import { z } from "zod";
import { guardedPrisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import {
  AllowedUrlPatternDTO,
  AllowedUrlPatternType,
} from "~/src/types/allowed-url-types";
import { Prisma } from "@askinfosec/database";
import type { AllowedUrlPattern as PrismaGeneratedAllowedUrlPattern } from "@askinfosec/database";
import {
  CreateAllowedUrlPatternActionSchema,
  CreateAllowedUrlPatternInput,
  UpdateAllowedUrlPatternActionSchema,
  UpdateAllowedUrlPatternInput,
} from "~/src/lib/schemas/allowed-url-schemas";
import {
  LogManager,
  LogLevel,
  UsageType,
} from "@/observability/migration-shim";

const logger = new LogManager(
  LogLevel.debug,
  "AllowedUrlsActions",
  UsageType.debug,
);

// Internal mapping function - should not be exported
function mapToAllowedUrlPattern(
  dbPattern: PrismaGeneratedAllowedUrlPattern,
): AllowedUrlPatternDTO {
  return {
    id: dbPattern.id,
    organizationId: dbPattern.organizationId,
    pattern: dbPattern.pattern,
    type: dbPattern.type as AllowedUrlPatternType,
    includeSubdomains: dbPattern.includeSubdomains,
    description: dbPattern.description,
    isEnabled: dbPattern.isEnabled,
    createdAt: dbPattern.createdAt,
    updatedAt: dbPattern.updatedAt,
    createdById: dbPattern.createdById,
    updatedById: dbPattern.updatedById,
  };
}

// Server Action: Create new AllowedUrlPattern
export async function createAllowedUrlPatternAction(
  params: CreateAllowedUrlPatternInput,
): Promise<{
  success: boolean;
  data?: AllowedUrlPatternDTO;
  error?: string;
  issues?: z.ZodIssue[];
}> {
  const validationResult =
    CreateAllowedUrlPatternActionSchema.safeParse(params);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed.",
      issues: validationResult.error.issues,
    };
  }

  const { orgId, userId, ...createInput } = validationResult.data;

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    const newPattern = await guardedPrisma({
      userId,
      orgId,
    }).allowedUrlPattern.create({
      data: {
        ...createInput,
        organizationId: orgId,
        createdById: userId,
        updatedById: userId,
      },
    });

    revalidatePath(`/${orgId}/helpdesk/url-patterns`);

    return {
      success: true,
      data: mapToAllowedUrlPattern(newPattern),
    };
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error creating allowed URL pattern",
      obj: { error },
    });
    return { success: false, error: "Failed to create URL pattern." };
  }
}

// Server Action: Get All AllowedUrlPatterns for an Organization
export async function getAllowedUrlPatternsAction(params: {
  orgId: string;
  userId: string;
}): Promise<{
  success: boolean;
  data?: AllowedUrlPatternDTO[];
  error?: string;
}> {
  const { orgId, userId } = params;

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  logger.addLog({
    usageType: UsageType.info,
    message: "Fetching allowed URL patterns",
    obj: { orgId, userId },
  });

  try {
    const patterns = await guardedPrisma({
      userId,
      orgId,
    }).allowedUrlPattern.findMany({
      where: { organizationId: orgId },
      orderBy: { updatedAt: "desc" },
    });

    logger.addLog({
      usageType: UsageType.info,
      message: "Fetched allowed URL patterns",
      obj: { patterns },
    });
    logger.printAll();

    return {
      success: true,
      data: patterns.map(mapToAllowedUrlPattern),
    };
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error fetching allowed URL patterns",
      obj: { error },
    });
    logger.printAll();
    return { success: false, error: "Failed to fetch URL patterns." };
  }
}

// Server Action: Update AllowedUrlPattern
export async function updateAllowedUrlPatternAction(
  params: UpdateAllowedUrlPatternInput & {
    orgId: string;
    userId: string;
    patternId: string;
  },
): Promise<{
  success: boolean;
  data?: AllowedUrlPatternDTO;
  error?: string;
  issues?: z.ZodIssue[];
}> {
  const validationResult =
    UpdateAllowedUrlPatternActionSchema.safeParse(params);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed.",
      issues: validationResult.error.issues,
    };
  }

  const { orgId, userId, patternId, ...updateInput } = validationResult.data;

  if (Object.keys(updateInput).length === 0) {
    return {
      success: false,
      error: "No fields provided for update.",
    };
  }

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  logger.addLog({
    usageType: UsageType.info,
    message: "Updating allowed URL pattern",
    obj: { orgId, userId, patternId },
  });

  try {
    const updatePayload: Prisma.AllowedUrlPatternUpdateInput = {
      ...updateInput,
      type: updateInput.type as string,
      updatedByUser: {
        connect: {
          id: userId,
        },
      },
    };

    const updatedPattern = await guardedPrisma({
      userId,
      orgId,
    }).allowedUrlPattern.update({
      where: { id: patternId, organizationId: orgId },
      data: updatePayload,
    });

    revalidatePath(`/${orgId}/helpdesk/url-patterns`);

    return {
      success: true,
      data: mapToAllowedUrlPattern(updatedPattern),
    };
  } catch (error) {
    console.error("Error updating allowed URL pattern:", error);
    if (
      error instanceof Prisma.PrismaClientKnownRequestError &&
      error.code === "P2025"
    ) {
      return {
        success: false,
        error: "URL pattern not found or you don't have access.",
      };
    }
    return { success: false, error: "Failed to update URL pattern." };
  }
}

// Server Action: Delete AllowedUrlPattern
export async function deleteAllowedUrlPatternAction(params: {
  orgId: string;
  userId: string;
  patternId: string;
}): Promise<{
  success: boolean;
  error?: string;
}> {
  const { orgId, userId, patternId } = params;

  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  logger.addLog({
    usageType: UsageType.info,
    message: "Deleting allowed URL pattern",
    obj: { orgId, userId, patternId },
  });

  try {
    await guardedPrisma({ userId, orgId }).allowedUrlPattern.delete({
      where: { id: patternId, organizationId: orgId },
    });

    revalidatePath(`/${orgId}/helpdesk/url-patterns`);

    return { success: true };
  } catch (error) {
    logger.addLog({
      usageType: UsageType.error,
      message: "Error deleting allowed URL pattern",
      obj: { error },
    });
    logger.printAll();
    if (
      error instanceof Prisma.PrismaClientKnownRequestError &&
      error.code === "P2025"
    ) {
      return {
        success: false,
        error: "URL pattern not found or you don't have access.",
      };
    }
    logger.printAll();
    return { success: false, error: "Failed to delete URL pattern." };
  }
}
