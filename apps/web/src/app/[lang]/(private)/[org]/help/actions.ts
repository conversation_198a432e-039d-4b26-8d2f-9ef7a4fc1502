"use server";

import { z } from "zod";
import { guardedPrisma } from "@/lib/prisma";
import { action } from "@/lib/safe-action";
import { base64ToFile } from "@/lib/utils";
import { parse } from "path";
import { Prisma } from "@askinfosec/database";

// Define the file attachment schema
const fileAttachmentSchema = z.object({
  fileName: z.string(),
  mimeType: z.string(),
  attachment: z.string(),
  sortOrder: z.number(),
});

// Define the security issue form schema
const issueReportSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  issueType: z.enum(["Security", "Privacy", "Compliance", "Other"], {
    required_error: "Issue type is required",
  }),
  severity: z.enum(["Low", "Medium", "High", "Critical"], {
    required_error: "Severity is required",
  }),
  title: z
    .string()
    .min(5, "Title must be at least 5 characters")
    .max(200, "Title must not exceed 200 characters"),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(2000, "Description must not exceed 2000 characters"),
  userId: z.string(),
  orgId: z.string(),
  fileAttachments: z.array(fileAttachmentSchema).optional(),
});

// Create a function to get the schema instead of exporting it directly
export async function getIssueReportSchema() {
  return issueReportSchema;
}

// Create the server action
export const submitIssueReport = action
  .metadata({ actionName: "submitIssueReport" })
  .schema(issueReportSchema)
  .action(async ({ parsedInput }) => {
    try {
      const db = guardedPrisma({
        orgId: parsedInput.orgId,
        userId: parsedInput.userId,
        checkMembership: false,
      });

      // Create attachments if provided
      const attachments: {
        files: Array<{
          fileId: string;
          displayName: string;
          uploadedAt: string;
          sortOrder: number;
          fileType: string;
          fileSize: number;
        }>;
        lastUpdated?: string;
      } = { files: [] };

      if (
        parsedInput.fileAttachments &&
        parsedInput.fileAttachments.length > 0
      ) {
        const fileReferences = await Promise.all(
          parsedInput.fileAttachments.map(async (fileData, index) => {
            // Convert base64 to file
            const file = base64ToFile(
              fileData.attachment,
              fileData.fileName,
              fileData.mimeType,
            );

            const filePath = parse(fileData.fileName);

            // Create file record
            const fileRecord = await db.file.create({
              data: {
                name: filePath.name,
                path: fileData.fileName,
                buffer_file: await file
                  .arrayBuffer()
                  .then((ab) => Buffer.from(ab)),
                organization_id: parsedInput.orgId,
                created_by_id: parsedInput.userId,
                checksum: "",
                document_type: "issue_report",
                other_data: {
                  source: "help_support_page",
                },
              },
            });

            // Return file reference for attachment
            return {
              fileId: fileRecord.id,
              displayName: fileData.fileName,
              uploadedAt: new Date().toISOString(),
              sortOrder: fileData.sortOrder || index,
              fileType: fileData.mimeType,
              fileSize: file.size,
            };
          }),
        );

        attachments.files = fileReferences;
        attachments.lastUpdated = new Date().toISOString();
      }

      // Create issue report
      const issueReport = await db.issueReport.create({
        data: {
          email: parsedInput.email,
          name: parsedInput.name,
          issue_type: parsedInput.issueType,
          description: parsedInput.description,
          // title: parsedInput.title,
          attachments:
            attachments.files.length > 0 ? attachments : Prisma.JsonNull,
          source: "help_support_page",
          status: "new",
          organization_id: parsedInput.orgId,
          reported_by_id: parsedInput.userId,
          updated_by_id: parsedInput.userId,
        },
      });

      return {
        success: true,
        data: {
          issueId: issueReport.id,
          message: "Issue report submitted successfully",
        },
      };
    } catch (error) {
      console.error("Error submitting issue:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to submit issue report",
      };
    }
  });
