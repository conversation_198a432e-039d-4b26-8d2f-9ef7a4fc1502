"use server";
import { guardedPrisma } from "@/lib/prisma";
import { Framework } from "@/types/prisma-kysely";
import fs from "fs";
import csv from "csv-parser";
import { revalidatePath } from "next/cache";
import { InputJsonObject } from "@askinfosec/database";

interface GetAllFrameworksParams {
  orgId: string;
  userId: string;
}

export async function GetAllFrameworks({
  orgId,
  userId,
}: GetAllFrameworksParams): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const data = await db.framework.findMany({
    where: {
      organization_id: orgId,
    },
    select: {
      id: true,
      code: true,
      name: true,
      description: true,
    },
  });
  const frameworks = data.map((d) => {
    return {
      id: d.id,
      code: d.code,
      name: d.name,
      description: d.description,
    } as Framework;
  });
  return JSON.stringify(frameworks);
}

interface AddFrameworkParams {
  orgId: string;
  userId: string;
  framework_code: string;
}

export async function AddFramework({
  orgId,
  userId,
  framework_code,
}: AddFrameworkParams): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  let existingFramework = await db.framework.findFirst({
    where: {
      code: framework_code,
    },
  });
  if (!existingFramework || existingFramework === null) {
    existingFramework = await db.framework.create({
      data: {
        name: framework_code,
        code: framework_code,
        organization_id: orgId,
        created_by_id: userId,
        updated_by_id: userId,
      },
    });
  }
  const results: {
    code: string;
    name: string;
    description: string;
    question: string;
    category: string;
    organization_id: string;
    status: string;
    category_in_framework: string;
    created_by_id: string;
    updated_by_id: string;
    function_grouping: string;
    assessment_objective: InputJsonObject;
    framework_id: string;
  }[] = [];
  const objectives: Record<string, InputJsonObject> = {};

  const existingControls = (
    await db.control.findMany({
      where: {
        framework_id: existingFramework.id,
        organization_id: orgId,
      },
      select: { code: true },
    })
  ).map((d) => d.code);

  fs.createReadStream(process.cwd() + "/src/data/templates/soc2-objectives.csv")
    .pipe(csv())
    .on("data", async (data) => {
      if (data["Objective"] !== "N/A") {
        const obj = JSON.parse(data["Objective"]);
        const desc = JSON.parse(data["Description"]);
        objectives[data["Control"]] = obj.map((o: string, index: number) => ({
          controlId: data["Control"],
          code: o,
          description: desc[index],
          status: "unanswered",
          explanation: "",
        }));
      }
    })
    .on("end", async () => {
      fs.createReadStream(
        process.cwd() + "/src/data/templates/soc2-controls.csv",
      )
        .pipe(csv())
        .on("data", async (data) => {
          if (existingControls.includes(data["Control Code"])) return;
          results.push({
            code: data["Control Code"],
            name: data["Control Name"],
            description: data["Control Description"],
            question: data["Control Question"],
            category: data["Control Domain"],
            organization_id: orgId,
            status: "non_compliant",
            category_in_framework: data["Control Domain"],
            created_by_id: userId,
            updated_by_id: userId,
            framework_id: existingFramework.id,
            function_grouping: data["Function Grouping"],
            assessment_objective: objectives[data["Control Code"]] || [],
          });
        })
        .on("end", async () => {
          await db.control.createMany({ data: results });
        });
    });
  try {
    revalidatePath(`/${orgId}/frameworks`);
  } catch (error) {
    console.error(error);
  }
  return JSON.stringify({
    status: "success",
    message: `Framework added to organization. Controls added ${""}`,
  });
}
