import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

// Use type assertion to avoid TypeScript errors with the extended Prisma client
const prisma = bypassedPrisma as unknown as PrismaClient;

/**
 * Deletes a user account and all associated organizations and sessions.
 * This is a soft delete, where the deleted_at column is set to the current date.
 *
 * @param {string} userId - The ID of the user account to delete.
 * @returns {boolean} True if the deletion was successful, false otherwise.
 */
const deleteAccount = async (userId: string): Promise<boolean> => {
  try {
    // Start a transaction to ensure all operations are atomic
    const tx = await prisma.$transaction(async (tx) => {
      // Soft delete the user account by setting the deleted_at column
      const user = await tx.userProfile.update({
        where: {
          user_id: userId,
        },
        data: {
          deleted_at: new Date(),
        },
      });
      // Find all organizations owned by the user
      const orgs = await tx.organization.findMany({
        where: {
          main_owner_id: userId,
          deleted_at: null,
        },
      });
      // Soft delete all organizations owned by the user
      await tx.organization.updateMany({
        where: {
          id: {
            in: orgs.map((org) => org.id),
          },
        },
        data: {
          deleted_at: new Date(),
        },
      });

      // Update member status to inactive for any organization memberships
      await tx.member.updateMany({
        where: {
          user_id: userId,
          deleted_at: null,
        },
        data: {
          deleted_at: new Date(),
        },
      });

      // Delete all sessions associated with the user
      await tx.session.deleteMany({
        where: {
          userId: userId,
        },
      });
    });
    // if all operations were successful
    return true;
  } catch (error) {
    // if any operation failed
    console.error("Error deleting account:", error);
    return false;
  }
};

export { deleteAccount };
