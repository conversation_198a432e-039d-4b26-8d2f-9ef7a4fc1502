"use server";

import { action } from "@/lib/safe-action";
import { uploadQuizSetSchema } from "./validations";
import { guardedPrisma, withAuditMetadataLogging } from "@/lib/prisma";
import { createId } from "@paralleldrive/cuid2";
import { AnswerChoices } from "~/src/models/lms";
import { CourseSection } from "@askinfosec/database";

export const uploadQuizSet = action
  .metadata({ actionName: "uploadQuizSet" })
  .schema(uploadQuizSetSchema)
  .action(async ({ parsedInput }) => {
    const db = guardedPrisma({
      orgId: parsedInput.orgId,
      userId: parsedInput.userId,
      checkMembership: false,
    });
    try {
      const courseSection = await db.courseSection.findFirst({
        where: {
          id: parsedInput.chapterId,
        },
      });

      if (!courseSection) {
        return { success: false, error: "Course not found" };
      }

      const getQuizSetName = async (
        courseSection: CourseSection,
        quizSetName: string,
      ) => {
        const isQuizSetNameExist = await db.quizSet.findFirst({
          where: {
            course_section_id: courseSection.id,
            name: {
              equals: quizSetName,
              mode: "insensitive",
            },
          },
        });

        if (!isQuizSetNameExist && quizSetName !== "") return quizSetName;
        else return courseSection.title;
      };

      const new_quiz_set_id = createId();

      const quizzes = parsedInput.quizzes.map((quiz) => ({
        question: quiz.question,
        answer_choices: quiz.answerChoices as AnswerChoices,
        correct_answer: quiz.correctAnswer,
        answer_type: "single", // or "multiple" based on your logic
        organization_id: parsedInput.orgId,
        course_section_id: parsedInput.chapterId,
      }));

      const [newQuizSet] = await db.$transaction([
        db
          .$extends(withAuditMetadataLogging({ userId: parsedInput.userId }))
          .quizSet.create({
            data: {
              id: new_quiz_set_id,
              name: await getQuizSetName(
                courseSection,
                parsedInput.quizSetName || "",
              ),
              organization_id: parsedInput.orgId,
              course_section_id: parsedInput.chapterId,
              quizzes: {
                createMany: {
                  data: quizzes,
                },
              },
            },
          }),
      ]);
      return { success: true, data: newQuizSet };
    } catch (error) {
      console.error("Error uploading quiz set:", error);
      return { success: false, error: "Failed to upload quiz set." };
    }
  });
