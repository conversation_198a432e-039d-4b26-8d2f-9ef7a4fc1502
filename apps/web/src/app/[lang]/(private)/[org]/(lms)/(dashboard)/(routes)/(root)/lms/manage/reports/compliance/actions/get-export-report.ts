"use server";

import { guardedPrisma } from "@/lib/prisma";
import type {
  Member,
  User,
  TrainingGroup,
  UserPolicyAcceptanceTracking,
} from "@askinfosec/database";

export interface ExportReportUser {
  name: string;
  email: string;
  acceptedPoliciesCount: number;
  lastPolicyAcceptanceDate: Date | null;
  completedCoursesCount: number;
  lastCourseCompletionDate: Date | null;
}

/**
 * Fetches and processes training report data for PDF export
 * @param orgId - Organization ID to fetch data for
 * @returns Array of user training report data
 */
export async function getExportReport(
  orgId: string,
): Promise<ExportReportUser[]> {
  const db = guardedPrisma({ orgId, userId: orgId, checkMembership: false });

  try {
    const users = await db.user.findMany({
      where: {
        members: {
          some: {
            organization_id: orgId,
          },
        },
      },
      include: {
        course_enrollments: {
          where: {
            course: {
              organization_id: orgId,
            },
          },
          include: {
            course: {
              include: {
                course_sections: {
                  include: {
                    user_course_section_progress: {
                      where: {
                        organization_id: orgId,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        user_policy_acceptance_trackings: {
          where: {
            training_group: {
              organization_id: orgId,
            },
          },
        },
      },
    });

    return users.map((user) => {
      // Track unique policies accepted
      const uniqueAcceptedPolicies = new Set<string>();
      let lastPolicyAcceptanceDate: Date | null = null;

      user.user_policy_acceptance_trackings.forEach((track) => {
        uniqueAcceptedPolicies.add(track.policy_id);
        if (
          !lastPolicyAcceptanceDate ||
          track.accepted_at > lastPolicyAcceptanceDate
        ) {
          lastPolicyAcceptanceDate = track.accepted_at;
        }
      });

      // Track unique completed courses
      const uniqueCompletedCourses = new Set<string>();
      let lastCourseCompletionDate: Date | null = null;

      user.course_enrollments.forEach((enrollment) => {
        enrollment.course.course_sections.forEach((section) => {
          const completed = section.user_course_section_progress.some(
            (p) => p.user_id === user.id && p.is_completed,
          );
          if (completed) {
            uniqueCompletedCourses.add(enrollment.course_id);
            const latestProgress = section.user_course_section_progress
              .filter((p) => p.user_id === user.id && p.date_last_completed)
              .sort(
                (a, b) =>
                  b.date_last_completed!.getTime() -
                  a.date_last_completed!.getTime(),
              )[0];

            if (
              latestProgress?.date_last_completed &&
              (!lastCourseCompletionDate ||
                latestProgress.date_last_completed > lastCourseCompletionDate)
            ) {
              lastCourseCompletionDate = latestProgress.date_last_completed;
            }
          }
        });
      });

      const userData = {
        name: user.name || "",
        email: user.email || "",
        acceptedPoliciesCount: uniqueAcceptedPolicies.size,
        lastPolicyAcceptanceDate,
        completedCoursesCount: uniqueCompletedCourses.size,
        lastCourseCompletionDate,
      };

      console.log("Processed user:", userData);
      return userData;
    });
  } catch (error) {
    console.error("Error in getExportReport:", error);
    throw error;
  }
}

export async function getTotalPolicies(orgId: string): Promise<number> {
  const db = guardedPrisma({ orgId, userId: orgId, checkMembership: false });
  const policies = await db.policy.count({
    where: {
      organization_id: orgId,
    },
  });
  return policies;
}

export async function getTotalCourses(orgId: string): Promise<number> {
  const db = guardedPrisma({ orgId, userId: orgId, checkMembership: false });
  const courses = await db.course.count({
    where: {
      organization_id: orgId,
    },
  });
  return courses;
}
