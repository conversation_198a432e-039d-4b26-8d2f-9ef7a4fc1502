"use server";
import { guardedPrisma, otherDataAuditLog } from "@/lib/prisma";
import { Control } from "@/types/prisma-kysely";
import { revalidatePath } from "next/cache";
import { getEnumKeyByValue, getEnumValueByKey } from "~/src/lib/utils-web";
import { ControlStatuses } from "~/src/models/statuses";
import { ServerActionErrors, ServerActionResponse } from "~/src/actions/types";
import { AssessmentObjective } from "../components/assessment-objectives";
import { InputJsonValue } from "@askinfosec/database";

interface GetControlDetailsParams {
  orgId: string;
  userId: string;
  controlId: string;
}

export async function GetControlDetails({
  orgId,
  userId,
  controlId,
}: GetControlDetailsParams): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const d = await db.control.findUnique({
    where: {
      id: controlId,
    },
    include: {
      evidence_tasks: true,
    },
  });
  if (d !== null) {
    const control = {
      id: d.id,
      code: d.code,
      name: d.name,
      description: d.description,
      question: d.question,
      category: d.category,
      assigned: d.assigned,
      approver: d.approver,
      status: getEnumValueByKey(ControlStatuses, d.status),
      implementationGuide: d.implementation_guide,
      frameworkId: d.framework_id,
      functionGrouping: d.function_grouping,
      applicability: d.applicability,
      assessmentObjective: d.assessment_objective,
    } as unknown as Control;
    return JSON.stringify({ status: "success", data: control });
  }
  return JSON.stringify({ status: "success", data: null });
}

interface UpdateControlItemParams {
  orgId: string;
  userId: string;
  id: string;
  fldName:
    | "name"
    | "description"
    | "question"
    | "code"
    | "implementation_guide"
    | "assigned"
    | "approver"
    | "status"
    | "function_grouping"
    | "applicability"
    | "category"
    | string;
  fldValue: string | string[];
}
export async function UpdateControlItem({
  orgId,
  userId,
  id,
  fldName,
  fldValue,
}: UpdateControlItemParams) {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const data = await db.$extends(otherDataAuditLog()).control.update({
    where: {
      id: id,
    },
    data: {
      [fldName]: fldValue,
      updated_by_id: userId,
    },
  });
  revalidatePath(`${orgId}/controls/${id}`);
  return JSON.stringify({
    status: "success",
    message: "Successfully updated control.",
    data: data,
  });
}

export interface UpdateControlStatusParams {
  orgId: string;
  userId: string;
  controlId: string;
  action: ControlStatuses;
}

export async function UpdateControlStatus({
  orgId,
  userId,
  controlId,
  action,
}: UpdateControlStatusParams): Promise<{ status: string; message: string }> {
  const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
  // TODO: If updating status to compliant, make sure to perform all necessary validaions.
  try {
    const control = await db.control.findUnique({
      where: {
        id: controlId,
      },
    });
    if (!control) {
      return { status: "error", message: "Control not found" };
    }
    await db.$extends(otherDataAuditLog()).control.update({
      where: {
        id: controlId,
      },
      data: {
        status: getEnumKeyByValue(ControlStatuses, action),
        updated_by_id: userId,
      },
    });
    return {
      status: "success",
      message: "Control status updated successfully!",
    };
  } catch (error) {
    console.error(error);
    return { status: "error", message: "Error updating control status" };
  }
}

export async function saveEditControlName(
  orgId: string,
  userId: string,
  itemId: string,
  fieldValue: string,
) {
  await UpdateControlItem({
    orgId: orgId,
    userId: userId,
    id: itemId,
    fldName: "name",
    fldValue: fieldValue,
  });
}

export async function saveInputText(
  orgId: string,
  userId: string,
  itemId: string,
  fieldName:
    | "name"
    | "description"
    | "category"
    | "code"
    | "implementation_guide"
    | "applicability"
    | "function_grouping"
    | string,
  fieldValue: string,
) {
  await UpdateControlItem({
    orgId: orgId,
    userId: userId,
    id: itemId,
    fldName: fieldName,
    fldValue: fieldValue,
  });
}

export async function saveMultiText(
  orgId: string,
  userId: string,
  itemId: string,
  fieldName: "approver" | "assigned" | string,
  fieldValue: string[],
) {
  await UpdateControlItem({
    orgId: orgId,
    userId: userId,
    id: itemId,
    fldName: fieldName,
    fldValue: fieldValue,
  });
}

interface CreateEvidenceTaskForControlParams {
  orgId: string;
  userId: string;
  controlId: string;
}

export async function CreateEvidenceTaskForControl({
  orgId,
  userId,
  controlId,
}: CreateEvidenceTaskForControlParams): Promise<ServerActionResponse<string>> {
  try {
    const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
    const control = await db.control.findUnique({
      where: { id: controlId, organization_id: orgId },
    });
    const existingEvidenceTask = await db.evidenceTask.findFirst({
      where: {
        organization_id: orgId,
        control_id: controlId,
      },
    });
    if (existingEvidenceTask) {
      return {
        error: ServerActionErrors.badRequest(
          "Main Evidence Task for Control already exist.",
        ),
      };
    }
    const evidenceTaskToCreate = {
      organization_id: orgId,
      created_by_id: userId,
      updated_by_id: userId,
      control_id: controlId,
      name: `${control?.code}:${control?.name}`,
      summary: null,
      status: "draft",
      guidance: null,
    };
    const result = await db.evidenceTask.create({
      data: evidenceTaskToCreate,
    });
    return { data: JSON.stringify(result) };
  } catch (error) {
    console.log(error);
    return { error: ServerActionErrors.badRequest("Bad request") };
  }
}

interface GetEvidenceTaskForControlParams {
  orgId: string;
  userId: string;
  controlId: string;
}

export async function GetEvidenceTaskForControl({
  orgId,
  userId,
  controlId,
}: GetEvidenceTaskForControlParams): Promise<ServerActionResponse<string>> {
  try {
    const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
    const existingEvidenceTask = await db.evidenceTask.findFirst({
      where: {
        organization_id: orgId,
        control_id: controlId,
      },
      include: {
        evidence_attachment: true,
      },
    });
    if (!existingEvidenceTask) {
      return {
        error: ServerActionErrors.badRequest(
          "Main Evidence Task for Control not found.",
        ),
      };
    }
    return { data: JSON.stringify(existingEvidenceTask) };
  } catch (error) {
    console.log(error);
    return { error: ServerActionErrors.badRequest("Bad request") };
  }
}

export interface UpdateControlAssessmentObjectiveParams {
  orgId: string;
  userId: string;
  controlId: string;
  code: string;
  status: string;
}

export async function updateControlAssessmentObjective({
  orgId,
  userId,
  controlId,
  code,
  status,
}: UpdateControlAssessmentObjectiveParams) {
  try {
    const db = guardedPrisma({ orgId: orgId, userId, checkMembership: false });
    const existingControl = await db.control.findUnique({
      where: {
        id: controlId,
      },
      select: {
        assessment_objective: true,
      },
    });
    if (!existingControl || !existingControl.assessment_objective) {
      // We still check even if impossible no existing control, or other_data is undefined. Impossible because we call it from the control page which means exist
      return { error: ServerActionErrors.badRequest("Bad request") };
    }
    const existingControlAssessmentObjectives =
      existingControl?.assessment_objective as unknown as AssessmentObjective[];
    const updatedObjectives = updateAssessmentObjectiveStatus(
      existingControlAssessmentObjectives,
      code,
      status,
    );
    await db.$extends(otherDataAuditLog()).control.update({
      where: {
        organization_id: orgId,
        id: controlId,
      },
      data: {
        assessment_objective: updatedObjectives as unknown as InputJsonValue,
        updated_by_id: userId,
      },
    });
    return { data: updatedObjectives };
  } catch (error) {
    return { error: ServerActionErrors.badRequest("Catch error: Bad request") };
  }
}

function updateAssessmentObjectiveStatus(
  objectives: AssessmentObjective[],
  code: string,
  status: string,
): AssessmentObjective[] {
  return objectives.map((objective) =>
    objective.code === code ? { ...objective, status } : objective,
  );
}
