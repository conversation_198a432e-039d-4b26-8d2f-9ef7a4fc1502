"use server";
import { guardedPrisma } from "@/lib/prisma";
import { Control } from "@/types/prisma-kysely";
import { Control as ControlObjectPrismaType } from "@askinfosec/database";

interface CreateNewControlParams {
  orgId: string;
  userId: string;
  newControl: {
    category: string;
    name: string;
    code?: string;
    description?: string;
    question?: string;
    assigned: string[];
    approver: string[];
  };
}

export async function CreateNewControl({
  orgId,
  userId,
  newControl,
}: CreateNewControlParams): Promise<string> {
  const db = guardedPrisma({ orgId, userId, checkMembership: false });
  const result = await db.control.create({
    data: {
      name: newControl.name,
      description: newControl.description ?? "",
      question: newControl.question ?? "",
      code: newControl.code ?? "",
      assigned: newControl.assigned,
      approver: newControl.approver,
      category: newControl.category,
      status: "non_compliant",
      created_by_id: userId,
      updated_by_id: userId,
      organization_id: orgId,
      category_in_framework: "",
    },
  });
  return JSON.stringify({ status: "success", message: "", data: result });
}
