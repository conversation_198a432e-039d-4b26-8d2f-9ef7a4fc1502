"use server";

import { tenantGuardedPrisma, bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

export interface LoadDocContentParams {
  fileId: string;
  orgId: string;
}

export async function LoadDocContent({
  fileId,
  orgId,
}: LoadDocContentParams): Promise<Buffer> {
  // Check if we're in Edge Runtime
  const isEdgeRuntime =
    typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge";

  let file;

  try {
    // Use the appropriate Prisma client based on the runtime environment
    if (isEdgeRuntime) {
      // In Edge Runtime, we need to use bypassedPrisma
      file = await (bypassedPrisma as PrismaClient).file.findUniqueOrThrow({
        where: {
          id: fileId,
        },
        select: {
          buffer_file: true,
        },
      });
    } else {
      // In regular server environment, use tenant-guarded Prisma
      file = await tenantGuardedPrisma(orgId).file.findUniqueOrThrow({
        where: {
          id: fileId,
        },
        select: {
          buffer_file: true,
        },
      });
    }

    if (!file.buffer_file) {
      throw new Error("File not found");
    }

    return Buffer.from(file.buffer_file);
  } catch (error) {
    console.error("Error loading document content:", error);
    throw new Error("Failed to load document content");
  }
}
