"use server";

import { Prisma, TrustCenterSettings } from "@askinfosec/database";
import { guardedPrisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getServerSession } from "@/lib/auth";
import { generateRandomChars } from "@/lib/utils";

import {
  TrustCenterSection,
  SaveTrustCenterSettingsArgs,
  companyNameSchema,
  domainManagementSchema,
  titleSettingsSchema,
  themeSettingsSchema,
  brandSettingsSchema,
  whyTrustUsSectionSchema,
  functionalitySettingsSchema,
  TitleSettingsPayloadData,
  ThemeSettingsPayloadData,
  BrandSettingsPayloadData,
  // apiKeySettingsSchema is not directly used by saveTrustCenterSettings logic
} from "./settings.shared";

export async function saveTrustCenterSettings(
  orgId: string,
  args: SaveTrustCenterSettingsArgs,
): Promise<TrustCenterSettings | null> {
  const session = await getServerSession();
  if (!session || !session.user || !session.user.id) {
    console.error("User session not found for saveTrustCenterSettings");
    return null;
  }

  if (!orgId || !args || !args.section || !args.data) {
    console.error(
      "orgId, section, and data object are required for saveTrustCenterSettings",
    );
    return null;
  }

  let updateData: Prisma.TrustCenterSettingsUpdateInput = {};

  const existingSettings = await guardedPrisma({
    orgId,
    userId: session.user.id,
  }).trustCenterSettings.findFirst({
    where: { organization_id: orgId },
  });

  // Switch based on the section to process only relevant data
  switch (args.section) {
    case TrustCenterSection.COMPANY_NAME:
      if (
        "companyName" in args.data &&
        typeof args.data.companyName === "string"
      ) {
        const parsedCompanyName = companyNameSchema.safeParse(
          args.data.companyName,
        );
        if (parsedCompanyName.success) {
          updateData.company_name = parsedCompanyName.data;
        } else {
          console.error(
            `Invalid company name data for section ${args.section}:`,
            parsedCompanyName.error.flatten(),
          );
          return null;
        }
      } else {
        console.error(
          `Missing or invalid companyName in data for section ${args.section}`,
        );
        return null;
      }
      break;

    case TrustCenterSection.DOMAIN_SETTINGS:
      console.log("domainManagement", args.data);
      if ("domainManagement" in args.data && args.data.domainManagement) {
        const parsedDomainManagement = domainManagementSchema.safeParse(
          args.data.domainManagement,
        );
        if (parsedDomainManagement.success && parsedDomainManagement.data) {
          const { useCustomDomainSelection, customDomainValue } =
            parsedDomainManagement.data;
          if (useCustomDomainSelection && customDomainValue) {
            updateData.domain = customDomainValue;
            updateData.is_custom_domain = true;
            updateData.is_domain_verified = false;
          } else {
            // Switching to (or confirming) default AskInfosec domain
            updateData.domain = `${customDomainValue}.${"trust.askinfosec.tech"}`;
            updateData.is_custom_domain = false;
            updateData.is_domain_verified = true;
          }
        } else {
          console.error(
            `Invalid domain management data for section ${args.section}:`,
            parsedDomainManagement.error?.flatten(),
          );
          return null;
        }
      } else {
        console.error(
          `Missing domainManagement in data for section ${args.section}`,
        );
        return null;
      }
      break;

    case TrustCenterSection.APPEARANCE_TITLE:
      // Remove this case as it's now handled by a dedicated function
      return saveTrustCenterTitleSettings(orgId, args.data.appearanceSettings);

    case TrustCenterSection.APPEARANCE_THEME:
      // Remove this case as it's now handled by a dedicated function
      return saveTrustCenterThemeSettings(orgId, args.data.appearanceSettings);

    case TrustCenterSection.APPEARANCE_BRAND:
      // Remove this case as it's now handled by a dedicated function
      return saveTrustCenterBrandSettings(orgId, args.data.appearanceSettings);

    case TrustCenterSection.APPEARANCE_EMAIL:
      // TODO: Implement email settings logic here when schema is defined
      console.warn("Email settings save not yet implemented.");
      return null;

    case TrustCenterSection.WHY_TRUST_US:
      if ("advantages" in args.data && Array.isArray(args.data.advantages)) {
        const parsed = whyTrustUsSectionSchema.safeParse({
          advantages: args.data.advantages,
        });
        if (parsed.success) {
          updateData.why_trust_us_section = parsed.data;
        } else {
          console.error(
            `Invalid 'Why Trust Us' data for section ${args.section}:`,
            parsed.error.flatten(),
          );
          return null;
        }
      }
      break;

    case TrustCenterSection.API_KEY_MANAGEMENT:
      const newApiKey = `tc_sk_${generateRandomChars(32)}`;
      const newApiKeyLastFour = newApiKey.slice(-4);
      updateData.api_key = newApiKey;
      updateData.api_key = newApiKeyLastFour;
      break;

    case TrustCenterSection.FUNCTIONALITY:
      if (
        "functionalitySettings" in args.data &&
        args.data.functionalitySettings
      ) {
        const parsedFunctionalitySettings =
          functionalitySettingsSchema.safeParse(
            args.data.functionalitySettings,
          );
        if (
          parsedFunctionalitySettings.success &&
          parsedFunctionalitySettings.data
        ) {
          // Merge with existing functionality data to avoid overwriting other sections
          const currentFunctionality = (existingSettings?.functionality ||
            {}) as Prisma.JsonObject;
          updateData.functionality = {
            ...currentFunctionality,
            ...parsedFunctionalitySettings.data,
          } as Prisma.JsonObject;
        } else {
          console.error(
            `Invalid functionality settings data for section ${args.section}:`,
            parsedFunctionalitySettings.error?.flatten(),
          );
          return null;
        }
      } else {
        console.error(
          `Missing functionalitySettings in data for section ${args.section}`,
        );
        return null;
      }
      break;

    default:
      console.error(
        "Unknown or unhandled section provided to saveTrustCenterSettings.",
      );
      return null;
  }

  // Process section-specific data above

  const savedSettings = await finalSaveTrustCenterSettings(
    orgId,
    session.user.id,
    existingSettings,
    updateData,
  );

  revalidatePath(
    `/app/[lang]/(private)/[org]/(trust-center)/trust-center/manage/settings`,
  );
  console.log(
    "[ServerAction] Path revalidated. Returning savedSettings:",
    JSON.stringify(savedSettings, null, 2),
  );
  return savedSettings;
}

export async function getTrustCenterSettingsByOrgId(
  orgId: string,
): Promise<TrustCenterSettings | null> {
  const session = await getServerSession();
  if (!session || !session.user || !session.user.id) {
    console.error("User session not found for getTrustCenterSettingsByOrgId");
    return null;
  }

  if (!orgId) {
    console.error("orgId is required for getTrustCenterSettingsByOrgId");
    return null;
  }

  try {
    const settings = await guardedPrisma({
      orgId,
      userId: session.user.id,
    }).trustCenterSettings.findFirst({
      where: {
        organization_id: orgId,
      },
    });
    // If no settings, create initial settings. This will guarantee a record exists for the org.
    // Any update can expect theres always a record.
    if (!settings) {
      const createInitialSettings = await guardedPrisma({
        orgId,
        userId: session.user.id,
      }).trustCenterSettings.create({
        data: {
          organization: { connect: { id: orgId } },
          updated_by: { connect: { id: session.user.id } },
          contact_email: "",
          domain: "",
          is_custom_domain: false,
          is_domain_verified: true,
          company_name: "",
          functionality: {},
          integration: {},
          is_hidden_report_issue: false,
          is_hidden_updates_section: true,
        },
      });
      return createInitialSettings;
    }
    return settings;
  } catch (error) {
    console.error("Error fetching Trust Center settings:", error);
    return null;
  }
}

async function finalSaveTrustCenterSettings(
  orgId: string,
  userId: string,
  existingSettings: TrustCenterSettings | null,
  updateData: Prisma.TrustCenterSettingsUpdateInput,
): Promise<TrustCenterSettings | null> {
  // Now handle the database operations based on whether we're updating or creating
  let savedSettings: TrustCenterSettings | null = null;
  console.log(
    "finalSaveTrustCenterSettings():existingSettings",
    existingSettings,
  );
  console.log("finalSaveTrustCenterSettings():updateData", updateData);
  if (existingSettings) {
    if (Object.keys(updateData).length > 0) {
      console.log(
        "[ServerAction] Data for existing record update:",
        JSON.stringify(updateData, null, 2),
      );
      savedSettings = await guardedPrisma({
        orgId,
        userId,
      }).trustCenterSettings.update({
        where: { id: existingSettings.id },
        data: updateData,
      });
      console.log(
        "[ServerAction] Updated settings result:",
        JSON.stringify(savedSettings, null, 2),
      );
    } else {
      // savedSettings = existingSettings; // No changes to apply
    }
  }
  return savedSettings;
}

export async function saveTrustCenterTitleSettings(
  orgId: string,
  data: TitleSettingsPayloadData,
): Promise<TrustCenterSettings | null> {
  const session = await getServerSession();
  if (!session || !session.user || !session.user.id) {
    console.error("User session not found for saveTrustCenterTitleSettings");
    return null;
  }

  if (!orgId || !data) {
    console.error(
      "orgId and data are required for saveTrustCenterTitleSettings",
    );
    return null;
  }

  const parsedTitleSettings = titleSettingsSchema.safeParse(data);
  if (!parsedTitleSettings.success) {
    console.error(
      "Invalid title settings data:",
      parsedTitleSettings.error.flatten(),
    );
    return null;
  }

  try {
    const existingSettings = await guardedPrisma({
      orgId,
      userId: session.user.id,
    }).trustCenterSettings.findFirst({
      where: { organization_id: orgId },
    });

    const currentAppearance = (existingSettings?.appearance ||
      {}) as Prisma.JsonObject;

    const updateData: Prisma.TrustCenterSettingsUpdateInput = {
      appearance: {
        ...currentAppearance,
        trustCenterTitle: parsedTitleSettings.data.title,
        showTrustCenterTitle: parsedTitleSettings.data.showInHeader,
      } as Prisma.JsonObject,
      updated_by: { connect: { id: session.user.id } },
    };

    let savedSettings: TrustCenterSettings | null = null;

    // We are guaranteed a record exists for the org.
    // We handle in getTrustCenterSettingsByOrgId() if no settings exist.
    // There is no need to create a new record here.
    if (existingSettings) {
      savedSettings = await guardedPrisma({
        orgId,
        userId: session.user.id,
      }).trustCenterSettings.update({
        where: { id: existingSettings.id },
        data: updateData,
      });
    }

    revalidatePath(
      `/app/[lang]/(private)/[org]/(trust-center)/trust-center/manage/settings`,
    );
    return savedSettings;
  } catch (error) {
    console.error("Error saving Trust Center title settings:", error);
    return null;
  }
}

export async function saveTrustCenterThemeSettings(
  orgId: string,
  data: ThemeSettingsPayloadData,
): Promise<TrustCenterSettings | null> {
  const session = await getServerSession();
  if (!session || !session.user || !session.user.id) {
    console.error("User session not found for saveTrustCenterThemeSettings");
    return null;
  }

  if (!orgId || !data) {
    console.error(
      "orgId and data are required for saveTrustCenterThemeSettings",
    );
    return null;
  }

  const parsedThemeSettings = themeSettingsSchema.safeParse(data);
  if (!parsedThemeSettings.success) {
    console.error(
      "Invalid theme settings data:",
      parsedThemeSettings.error.flatten(),
    );
    return null;
  }

  try {
    const existingSettings = await guardedPrisma({
      orgId,
      userId: session.user.id,
    }).trustCenterSettings.findFirst({
      where: { organization_id: orgId },
    });

    const currentAppearance = (existingSettings?.appearance ||
      {}) as Prisma.JsonObject;
    const currentTheme = (currentAppearance.theme || {}) as Prisma.JsonObject;

    const updateData: Prisma.TrustCenterSettingsUpdateInput = {
      appearance: {
        ...currentAppearance,
        theme: {
          ...currentTheme,
          primaryColor: parsedThemeSettings.data.primaryColor,
          secondaryColor: parsedThemeSettings.data.secondaryColor,
          mode: parsedThemeSettings.data.mode,
          typography: parsedThemeSettings.data.typography,
          fontFamily: parsedThemeSettings.data.fontFamily,
        },
      } as Prisma.JsonObject,
      updated_by: { connect: { id: session.user.id } },
    };

    let savedSettings: TrustCenterSettings | null = null;

    // We are guaranteed a record exists for the org.
    // We handle in getTrustCenterSettingsByOrgId() if no settings exist.
    // There is no need to create a new record here.
    if (existingSettings) {
      savedSettings = await guardedPrisma({
        orgId,
        userId: session.user.id,
      }).trustCenterSettings.update({
        where: { id: existingSettings.id },
        data: updateData,
      });
    }

    revalidatePath(
      `/app/[lang]/(private)/[org]/(trust-center)/trust-center/manage/settings`,
    );
    return savedSettings;
  } catch (error) {
    console.error("Error saving Trust Center theme settings:", error);
    return null;
  }
}

export async function saveTrustCenterBrandSettings(
  orgId: string,
  data: BrandSettingsPayloadData,
): Promise<TrustCenterSettings | null> {
  const session = await getServerSession();
  if (!session || !session.user || !session.user.id) {
    console.error("User session not found for saveTrustCenterBrandSettings");
    return null;
  }

  if (!orgId || !data) {
    console.error(
      "orgId and data are required for saveTrustCenterBrandSettings",
    );
    return null;
  }

  const parsedBrandSettings = brandSettingsSchema.safeParse(data);
  if (!parsedBrandSettings.success) {
    console.error(
      "Invalid brand settings data:",
      parsedBrandSettings.error.flatten(),
    );
    return null;
  }

  try {
    const existingSettings = await guardedPrisma({
      orgId,
      userId: session.user.id,
    }).trustCenterSettings.findFirst({
      where: { organization_id: orgId },
    });

    const currentAppearance = (existingSettings?.appearance ||
      {}) as Prisma.JsonObject;
    const currentBrand = (currentAppearance.brand || {}) as Prisma.JsonObject;

    const updateData: Prisma.TrustCenterSettingsUpdateInput = {
      appearance: {
        ...currentAppearance,
        brand: {
          ...currentBrand,
          logo: parsedBrandSettings.data.logo,
          logoUrl: parsedBrandSettings.data.logoUrl,
          favicon: parsedBrandSettings.data.favicon,
          openGraphImage: parsedBrandSettings.data.openGraphImage,
        },
      } as Prisma.JsonObject,
      updated_by: { connect: { id: session.user.id } },
    };

    let savedSettings: TrustCenterSettings | null = null;

    // We are guaranteed a record exists for the org.
    // We handle in getTrustCenterSettingsByOrgId() if no settings exist.
    // There is no need to create a new record here.
    if (existingSettings) {
      savedSettings = await guardedPrisma({
        orgId,
        userId: session.user.id,
      }).trustCenterSettings.update({
        where: { id: existingSettings.id },
        data: updateData,
      });
    }

    revalidatePath(
      `/app/[lang]/(private)/[org]/(trust-center)/trust-center/manage/settings`,
    );
    return savedSettings;
  } catch (error) {
    console.error("Error saving Trust Center brand settings:", error);
    return null;
  }
}
