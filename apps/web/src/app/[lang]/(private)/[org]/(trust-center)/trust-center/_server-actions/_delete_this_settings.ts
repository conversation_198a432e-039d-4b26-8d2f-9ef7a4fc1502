"use server";

import { z } from "zod";
import { guardedPrisma } from "@/lib/prisma";
import { Prisma } from "@askinfosec/database";
import { revalidatePath } from "next/cache";
import { getServerSession } from "@/lib/auth";

// --- Added type definitions ---

// Structure for branding settings within appearance
interface BrandingSettings {
  logoUrl?: string | null;
  faviconUrl?: string | null;
  primaryColor?: string | null;
  // Note: Zod schema currently includes secondaryColor, adjust if needed
  secondaryColor?: string | null;
}

// Structure for the 'appearance' JSON field
interface AppearanceSettingsData {
  branding?: BrandingSettings;
  // Add other appearance-related fields here (e.g., layout, font)
}

// Structure for the 'functionality' JSON field
interface FunctionalitySettingsData {
  enableDocumentDownloads?: boolean;
  // Add other functionality-related fields here
}

// Structure for the 'integration' JSON field
interface IntegrationSettingsData {
  // Add integration-related fields here
}

// --- End of type definitions ---

// Define Zod schema for Branding within Appearance
const brandingSchema = z
  .object({
    logoUrl: z.string().url().optional().nullable(), // Changed from logo
    faviconUrl: z.string().url().optional().nullable(), // Added
    primaryColor: z.string().optional().nullable(),
    secondaryColor: z.string().optional().nullable(),
  })
  .optional()
  .nullable();

// Update main settings schema - make contactEmail required again
const settingsSchema = z.object({
  id: z.string().optional(),
  contactEmail: z.string().email(), // Keep required
  appearance: z
    .object({
      // Nest branding under appearance
      branding: brandingSchema,
      // Define other appearance fields here if needed
    })
    .optional()
    .nullable(),
  // Add functionality and integration - using z.any() for now
  // TODO: Define specific Zod schemas for functionality and integration based on interfaces
  functionality: z.any().optional().nullable(),
  integration: z.any().optional().nullable(),
});

export type SettingsInput = z.infer<typeof settingsSchema>;

export async function getSettings(orgId: string) {
  const session = await getServerSession();
  if (!session?.user?.id) throw new Error("Unauthorized");

  const settings = await guardedPrisma({
    orgId,
    userId: session.user.id,
  }).trustCenterSettings.findFirst({
    where: { organization_id: orgId },
  });

  return settings;
}

export async function getPublicSettings(orgId: string) {
  const apiKey = process.env.API_KEY;
  if (!apiKey) throw new Error("API key not configured");

  const settings = await guardedPrisma({ orgId }).trustCenterSettings.findFirst(
    {
      where: { organization_id: orgId },
    },
  );

  return settings;
}

export async function _updateSettings(orgId: string, input: SettingsInput) {
  const session = await getServerSession();
  if (!session?.user?.id) throw new Error("Unauthorized");

  const data = settingsSchema.parse(input);
  const existingSettings = await guardedPrisma({
    orgId,
    userId: session.user.id,
  }).trustCenterSettings.findFirst({
    where: { organization_id: orgId },
    select: { id: true },
  });

  // Prepare data for Prisma
  const createData: Prisma.TrustCenterSettingsUncheckedCreateInput = {
    id: crypto.randomUUID(),
    organization_id: orgId,
    contact_email: data.contactEmail,
    appearance:
      data.appearance === undefined
        ? Prisma.DbNull
        : data.appearance === null
          ? Prisma.JsonNull
          : data.appearance,
    functionality:
      data.functionality === undefined
        ? Prisma.DbNull
        : data.functionality === null
          ? Prisma.JsonNull
          : data.functionality,
    integration:
      data.integration === undefined
        ? Prisma.DbNull
        : data.integration === null
          ? Prisma.JsonNull
          : data.integration,
    updated_by_id: session.user.id,
  };

  // Prepare update data carefully
  const updateData: Prisma.TrustCenterSettingsUpdateInput = {
    updated_by: { connect: { id: session.user.id } },
    ...(data.contactEmail && { contact_email: data.contactEmail }),
    ...(data.appearance !== undefined && {
      appearance: data.appearance === null ? Prisma.JsonNull : data.appearance,
    }),
    ...(data.functionality !== undefined && {
      functionality:
        data.functionality === null ? Prisma.JsonNull : data.functionality,
    }),
    ...(data.integration !== undefined && {
      integration:
        data.integration === null ? Prisma.JsonNull : data.integration,
    }),
  };

  // Remove undefined fields from updateData to avoid accidental overwrites
  Object.keys(updateData).forEach((key) => {
    if (updateData[key as keyof typeof updateData] === undefined) {
      delete updateData[key as keyof typeof updateData];
    }
  });

  const settings = await guardedPrisma({
    orgId,
    userId: session.user.id,
  }).trustCenterSettings.upsert({
    where: {
      id: existingSettings?.id || "non-existent-uuid", // Use a non-matching UUID if no existing ID
      organization_id: orgId,
    },
    create: createData,
    update: updateData,
  });

  revalidatePath(`/[lang]/(private)/[org]/trust-center`);
  return settings;
}
