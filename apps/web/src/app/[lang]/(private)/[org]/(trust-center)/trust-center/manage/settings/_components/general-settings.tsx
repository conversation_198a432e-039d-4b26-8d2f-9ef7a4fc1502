"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useEffect, useState, useTransition } from "react";
import { TrustCenterSettings } from "@askinfosec/database";
import { saveTrustCenterSettings } from "@/app/[lang]/(private)/[org]/(trust-center)/trust-center/manage/settings/_server-actions/settings";
import { usePathname } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import { SpinnerBasic } from "@/components/spinner";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowDown,
  ArrowUp,
  PlusCircle,
  RefreshCw,
  Trash2,
  XCircle,
} from "lucide-react"; // Added RefreshCw
import { ensureVercelDomain } from "@/services/vercelService";
import { TrustCenterSection } from "../_server-actions/settings.shared";

interface GeneralSettingsProps {
  initialSettings: TrustCenterSettings | null;
  orgId: string;
  isLoading: boolean;
}

const generateInternalSlug = () => {
  const prefix = "org";
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  let randomPart = "";
  for (let i = 0; i < 5; i++) {
    randomPart += characters.charAt(
      Math.floor(Math.random() * characters.length),
    );
  }
  return prefix + randomPart;
};

const TRUST_CENTER_BASE_DOMAIN = "trust.askinfosec.tech"; // EXAMPLE: Replace with your actual base domain

// Loading spinner component wrapper
const LoadingSpinner = ({
  size = "sm",
  className = "",
}: {
  size?: "sm" | "lg";
  className?: string;
}) => {
  const sizeClasses = size === "lg" ? "h-8 w-8" : "h-4 w-4";
  return (
    <div className={`${className} ${sizeClasses}`}>
      <SpinnerBasic />
    </div>
  );
};

export function GeneralSettings({
  initialSettings,
  orgId,
  isLoading: isLoadingProps,
}: GeneralSettingsProps) {
  const [isPendingDomain, startTransitionDomain] = useTransition();
  const [isPendingCompanyName, startTransitionCompanyName] = useTransition();
  const [isPendingTrustAdvantages, startTransitionTrustAdvantages] =
    useTransition();

  // Settings state
  const [settings, setSettings] = useState<TrustCenterSettings | null>(
    initialSettings,
  );

  const pathname = usePathname();
  const lang = pathname.split("/")[1];
  // Domain Settings State
  const [domainInput, setDomainInput] = useState<string>(""); // Holds custom domain OR internal slug
  const [isCustom, setIsCustom] = useState<boolean>(false);
  const [useCustomDomain, setUseCustomDomain] = useState<boolean>(false);
  const [customDomain, setCustomDomain] = useState<string>("");
  const [isDomainVerified, setIsDomainVerified] = useState<boolean>(false);
  const [displayedDomain, setDisplayedDomain] = useState<string>(""); // Primarily for custom domain verification UI
  const [verifyLoading, setVerifyLoading] = useState<boolean>(false);
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [verificationError, setVerificationError] = useState<string>("");
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [persistedCustomDomain, setPersistedCustomDomain] =
    useState<string>(""); // Holds SAVED custom domain OR internal slug
  const [persistedIsCustom, setPersistedIsCustom] = useState<boolean>(false);
  const [isSavingDomain, setIsSavingDomain] = useState(false);

  // Company Name State
  const [companyName, setCompanyName] = useState<string>("");

  // Why Trust Us
  const MAX_ADVANTAGES = 10;
  const DEFAULT_ADVANTAGE = "End-to-end encryption for all data";
  const [advantages, setAdvantages] = useState<string[]>([DEFAULT_ADVANTAGE]);
  const [newAdvantage, setNewAdvantage] = useState<string>("");

  const isValidCustomDomain = (val: string) =>
    val.length > 0 &&
    val.includes(".") &&
    !val.startsWith(".") &&
    !val.endsWith(".");

  useEffect(() => {
    if (!isLoadingProps && initialSettings) {
      const initialIsCustomSetting = initialSettings.is_custom_domain || false;
      const initialDomainValueSetting = initialSettings.domain || "";

      setIsCustom(initialIsCustomSetting);
      setPersistedIsCustom(initialIsCustomSetting);
      setDomainInput(initialDomainValueSetting); // Slug if internal, custom domain if custom
      setPersistedCustomDomain(initialDomainValueSetting);
      setIsDomainVerified(initialSettings.is_domain_verified || false);

      if (initialIsCustomSetting) {
        setDisplayedDomain(initialDomainValueSetting); // For custom domain verification UI
      } else {
        // For internal, displayedDomain is not directly shown in an input for the slug.
        // The full URL is constructed in descriptive text. It can mirror the slug or be empty.
        setDisplayedDomain(initialDomainValueSetting); // Mirrors slug, or could be ""
      }

      setCompanyName(initialSettings.company_name || "");

      // Parse the why_trust_us_section data
      const whyTrustUsSection = initialSettings.why_trust_us_section;
      if (
        whyTrustUsSection &&
        typeof whyTrustUsSection === "object" &&
        !Array.isArray(whyTrustUsSection) &&
        "advantages" in whyTrustUsSection &&
        Array.isArray(whyTrustUsSection.advantages)
      ) {
        setAdvantages(
          whyTrustUsSection.advantages.filter((adv) => typeof adv === "string"),
        );
      } else {
        setAdvantages([]);
      }
    } else if (!isLoadingProps && !initialSettings) {
      // Defaults for new settings
      setIsCustom(false);
      setPersistedIsCustom(false);
      setDomainInput("");
      setPersistedCustomDomain("");
      setDisplayedDomain("");
      setIsDomainVerified(false); // Internal is 'verified', custom starts unverified
      setCompanyName("");
      setAdvantages([]);
    }
  }, [initialSettings, isLoadingProps]);

  const handleGenerateInternalSlug = () => {
    const newSlug = generateInternalSlug();
    setDomainInput(newSlug);
  };

  const handleVerifyDomain = async () => {
    const verifyDomain = () => {
      // TODO: implement domain verification
      return false;
    };
    if (!isCustom) {
      toast.error("Please enter a valid custom domain to verify.");
      return;
    }

    if (!isValidCustomDomain(domainInput)) {
      toast.error("Please enter a valid custom domain to verify.");
      return;
    }
    setVerifyLoading(true);
    setIsVerifying(true);
    setVerificationError("");

    await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate API call
    const isLocal = domainInput.toLowerCase().startsWith("localhost");
    const successfullyVerified = isLocal || verifyDomain();
    if (successfullyVerified) {
      setIsDomainVerified(true);
      setIsVerified(true);
      toast.success(`Domain ${domainInput} verified successfully (simulated).`);
      // Optionally save this verification status immediately
    } else {
      setIsDomainVerified(false);
      setIsVerified(false);
      setVerificationError("Domain verification failed. Check DNS settings.");
      toast.error(
        `Failed to verify domain ${domainInput} (simulated). Check DNS settings.`,
      );
    }
    setVerifyLoading(false);
  };

  const handleSaveDomainSettings = async () => {
    setIsSavingDomain(true);
    toast.loading("Verifying and saving domain settings...");

    // Construct the full domain name
    // Ensure your construction logic is correct (e.g., subdomain.base.com)
    // This example assumes the internalSubdomain is just the 'subdomain' part.
    // Also, ensure domainInput is the correct variable holding the subdomain/custom domain part.
    const domainPartToUse = domainInput.trim();
    if (!domainPartToUse) {
      toast.error("Domain input cannot be empty.");
      setIsSavingDomain(false);
      return;
    }
    const fullDomainName = isCustom
      ? domainPartToUse
      : `${domainPartToUse}.${TRUST_CENTER_BASE_DOMAIN}`;

    try {
      // Step 1: Ensure the domain is on Vercel
      const vercelResult = await ensureVercelDomain(
        fullDomainName,
        displayedDomain,
      );

      if (!vercelResult.success) {
        toast.error(
          `Vercel domain setup failed: ${vercelResult.errorMessage || "Unknown error"} (Status: ${vercelResult.statusCode || "N/A"})`,
        );
        setIsSavingDomain(false);
        return;
      }

      toast.success(
        `Domain ${fullDomainName} confirmed on Vercel. ${vercelResult.domainInfo?.verified ? "It's verified." : "Verification may be pending."}`,
      );

      if (
        vercelResult.domainInfo &&
        !vercelResult.domainInfo.verified &&
        vercelResult.domainInfo.verification?.length
      ) {
        console.log(
          "Vercel verification details:",
          vercelResult.domainInfo.verification,
        );
        toast.info(
          "The domain needs to be verified on Vercel. Check your Vercel dashboard or DNS settings.",
        );
      }

      // Step 2: Proceed to save settings to your database
      startTransitionDomain(async () => {
        try {
          const payload = {
            domainManagement: {
              useCustomDomainSelection: isCustom,
              customDomainValue: domainInput, // domainInput holds slug if !isCustom, or full custom domain if isCustom
            },
          };
          const result = await saveTrustCenterSettings(orgId, {
            section: TrustCenterSection.DOMAIN_SETTINGS,
            data: payload,
          });
          if (result) {
            const resultIsCustom = result.is_custom_domain || false;
            const savedDomainValue = result.domain || "";

            setIsCustom(resultIsCustom);
            setPersistedIsCustom(resultIsCustom);
            setDomainInput(savedDomainValue); // Update UI to reflect actual saved value
            setPersistedCustomDomain(savedDomainValue);
            setIsDomainVerified(result.is_domain_verified || false);

            if (resultIsCustom) {
              setDisplayedDomain(savedDomainValue);
            } else {
              setDisplayedDomain(savedDomainValue); // This is the slug
            }
            toast.success("Domain settings saved successfully to AskInfosec!");
          } else {
            toast.error(
              "Failed to save domain settings to AskInfosec. Result was null.",
            );
          }
        } catch (error: any) {
          toast.error(
            error.message ||
              "An unexpected error occurred while saving domain settings to AskInfosec.",
          );
        }
      });
    } catch (error) {
      console.error("Error during Vercel domain operation:", error);
      let message =
        "An unexpected error occurred during Vercel domain operation.";
      if (error instanceof Error) {
        message = error.message;
      }
      toast.error(message);
    } finally {
      setIsSavingDomain(false);
      // Consider if toast.dismiss() for loading toast is needed or if success/error toasts are sufficient
    }
  };

  // Handle cancel domain changes, reset to initial settings
  const handleCancelDomainSettings = () => {
    if (initialSettings) {
      const initialIsCustom = initialSettings.is_custom_domain || false;
      const initialDomainVal = initialSettings.domain || "";
      setIsCustom(initialIsCustom);
      setUseCustomDomain(initialIsCustom);
      setDomainInput(initialDomainVal);
      setPersistedIsCustom(initialIsCustom);
      setPersistedCustomDomain(initialDomainVal);
      setIsDomainVerified(initialSettings.is_domain_verified || false);
      setIsVerified(initialSettings.is_domain_verified || false);
      setVerificationError("");
      setVerifyLoading(false);
      setIsVerifying(false);
      setDisplayedDomain(initialDomainVal);
    } else {
      setIsCustom(false);
      setUseCustomDomain(false);
      setDomainInput("");
      setPersistedIsCustom(false);
      setPersistedCustomDomain("");
      setIsDomainVerified(false);
      setIsVerified(false);
      setVerificationError("");
      setVerifyLoading(false);
      setIsVerifying(false);
      setDisplayedDomain("");
    }
  };

  // Handle company name submission
  const handleSaveCompanyName = async (e: React.FormEvent) => {
    e.preventDefault();

    startTransitionCompanyName(async () => {
      try {
        // Save only company name
        await saveTrustCenterSettings(orgId, {
          section: TrustCenterSection.COMPANY_NAME,
          data: {
            companyName: companyName,
          },
        });

        toast.success("Company name saved successfully.");
      } catch (error) {
        console.error("Error saving company name:", error);
        toast.error("Failed to save company name. Please try again.");
      }
    });
  };

  // Add a handler for advantages
  const handleAdvantagesSubmit = async () => {
    startTransitionTrustAdvantages(async () => {
      try {
        const validAdvantages = advantages.filter((adv) => adv.trim() !== "");
        if (validAdvantages.length !== advantages.length) {
          toast.info("Empty advantages were not saved.");
        }
        const result = await saveTrustCenterSettings(orgId, {
          section: TrustCenterSection.WHY_TRUST_US,
          data: {
            advantages: advantages,
          },
        });
        if (result) {
          setSettings(result);
          toast.success("Trust advantages saved successfully!");
        } else {
          toast.error("Failed to save trust advantages.");
        }
      } catch (error) {
        console.error("Error saving trust advantages:", error);
        toast.error("An error occurred while saving trust advantages.");
      }
    });
  };

  const addAdvantage = () => {
    if (!newAdvantage.trim()) {
      toast.error("Advantage text cannot be empty.");
      return;
    }

    if (newAdvantage.length > 255) {
      toast.error("Advantage text cannot exceed 255 characters.");
      return;
    }

    if (advantages.length >= MAX_ADVANTAGES) {
      toast.error(`You cannot add more than ${MAX_ADVANTAGES} advantages.`);
      return;
    }

    const newAdvantages = [...advantages];
    newAdvantages.push(newAdvantage.trim());
    setAdvantages(newAdvantages);

    setNewAdvantage("");
  };

  const removeAdvantage = (index: number) => {
    const newAdvantages = [...advantages];
    newAdvantages.splice(index, 1);
    setAdvantages(newAdvantages);
  };

  const moveAdvantageUp = (index: number) => {
    if (index <= 0) return;
    const newAdvantages = [...advantages];
    const temp = newAdvantages[index];
    newAdvantages[index] = newAdvantages[index - 1];
    newAdvantages[index - 1] = temp;
    setAdvantages(newAdvantages);
  };

  const moveAdvantageDown = (index: number) => {
    if (index >= advantages.length - 1) return;
    const newAdvantages = [...advantages];
    const temp = newAdvantages[index];
    newAdvantages[index] = newAdvantages[index + 1];
    newAdvantages[index + 1] = temp;
    setAdvantages(newAdvantages);
  };

  // Calculate if domain settings are dirty
  const isNewSetup = initialSettings === null;
  const customModeChanged = isCustom !== persistedIsCustom;
  const customDomainValueChanged = domainInput !== persistedCustomDomain;
  const domainSettingsDirty =
    isNewSetup || customModeChanged || customDomainValueChanged;

  if (isLoadingProps) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Company Name Card ... */}
      <Card>
        <CardHeader>
          <CardTitle>Company Name</CardTitle>
          <CardDescription>
            Set your company name for the Trust Center. This will be displayed
            in your Trust Center.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="companyName">Company Name</Label>
            <Input
              id="companyName"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="Your Company LLC"
            />
          </div>
          <Button
            type="button"
            onClick={handleSaveCompanyName}
            disabled={isPendingCompanyName || isLoadingProps}
          >
            {isPendingCompanyName ? <SpinnerBasic /> : null}
            Save Company Name
          </Button>
        </CardContent>
      </Card>

      {/* Domain Settings Card */}
      <Card>
        <CardHeader>
          <CardTitle>Domain Settings</CardTitle>
          <CardDescription>
            Configure the domain for your public Trust Center.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="useCustomDomain"
                checked={isCustom}
                onCheckedChange={(checked) => {
                  const newIsCustomState = Boolean(checked);
                  setIsCustom(newIsCustomState);

                  if (newIsCustomState) {
                    // Switched TO custom
                    const newDomainInputValue =
                      persistedIsCustom && persistedCustomDomain
                        ? persistedCustomDomain
                        : "";
                    setDomainInput(newDomainInputValue);
                    setDisplayedDomain(newDomainInputValue);
                    if (
                      newDomainInputValue.toLowerCase().startsWith("localhost")
                    ) {
                      setIsDomainVerified(true);
                    } else if (
                      initialSettings?.is_custom_domain &&
                      initialSettings?.domain === newDomainInputValue &&
                      initialSettings?.is_domain_verified
                    ) {
                      setIsDomainVerified(true);
                    } else {
                      setIsDomainVerified(false);
                    }
                  } else {
                    // Switched TO internal
                    const newDomainInputValue =
                      !persistedIsCustom && persistedCustomDomain
                        ? persistedCustomDomain
                        : "";
                    setDomainInput(newDomainInputValue); // Load saved slug or clear for generation
                    setDisplayedDomain(newDomainInputValue); // Or ""
                    setIsDomainVerified(true); // Internal considered verified
                  }
                }}
              />
              <Label htmlFor="useCustomDomain" className="font-medium">
                Use your own custom domain
              </Label>
            </div>
          </div>
          {isCustom ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
              <div>
                <Label htmlFor="customDomain">Custom Domain</Label>
                <Input
                  id="customDomain"
                  placeholder="e.g., trust.yourcompany.com"
                  value={domainInput}
                  onChange={(e) => {
                    const val = e.target.value;
                    setDomainInput(val);
                    setDisplayedDomain(val); // Keep displayedDomain in sync for verification UI
                    if (val.toLowerCase().startsWith("localhost")) {
                      setIsDomainVerified(true);
                    } else if (
                      initialSettings?.domain === val &&
                      initialSettings?.is_domain_verified &&
                      initialSettings?.is_custom_domain
                    ) {
                      setIsDomainVerified(true);
                    } else {
                      setIsDomainVerified(false);
                    }
                  }}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Enter the full domain. You'll need to configure DNS records
                  for verification.
                </p>
              </div>
              <div>
                <Label htmlFor="currentSavedDomain">Current Saved Domain</Label>
                <Input
                  id="currentSavedDomain"
                  value={persistedCustomDomain || "(Not set)"}
                  readOnly
                  className="bg-muted border-dashed"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  This is the domain currently active in the database.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="internalDomainSlug">
                Internal Subdomain Slug
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="internalDomainSlug"
                  value={domainInput} // Shows the generated slug e.g. orgabc12
                  readOnly
                  placeholder="(Click button to generate)"
                  className="flex-grow"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleGenerateInternalSlug}
                  title={
                    domainInput && !isCustom
                      ? "Regenerate slug"
                      : "Generate new slug"
                  }
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {domainInput && !isCustom ? "Regenerate" : "Generate"}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                {domainInput && !isCustom
                  ? `Your Trust Center will be available at: ${domainInput}`
                  : "Click 'Generate' to create your internal subdomain slug."}
              </p>
            </div>
          )}
          {/* Verification UI for custom domain ... */}
          {isCustom &&
            domainInput &&
            !domainInput.toLowerCase().startsWith("localhost") &&
            !isDomainVerified && (
              <div className="space-y-3 pt-4 border-t mt-6">
                <div className="flex items-center justify-between flex-wrap gap-2">
                  <Label className="text-orange-500 font-semibold">
                    Domain Verification Pending
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleVerifyDomain}
                    disabled={
                      verifyLoading || !isValidCustomDomain(domainInput)
                    }
                  >
                    {verifyLoading ? <SpinnerBasic /> : null} Verify Domain
                    (Simulated)
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Your custom domain <strong>{displayedDomain}</strong> requires
                  verification. Click "Verify Domain" after configuring DNS
                  records. (Full verification flow is upcoming).
                </p>
              </div>
            )}
          {isCustom && domainInput && isDomainVerified && (
            <div className="space-y-2 pt-4 border-t mt-6">
              <Label
                className={`font-semibold ${domainInput.toLowerCase().startsWith("localhost") ? "text-blue-600" : "text-green-600"}`}
              >
                {domainInput.toLowerCase().startsWith("localhost")
                  ? "Localhost Active"
                  : "Domain Verified"}
              </Label>
              <p className="text-sm text-muted-foreground">
                Your domain <strong>{displayedDomain}</strong> is active.
                {domainInput.toLowerCase().startsWith("localhost")
                  ? " (Localhost is auto-verified for development)"
                  : ""}
              </p>
            </div>
          )}
          <div className="flex items-center gap-3 mt-8">
            <Button
              type="button"
              onClick={handleSaveDomainSettings}
              disabled={
                isPendingDomain ||
                isLoadingProps ||
                !domainSettingsDirty ||
                isSavingDomain ||
                (isCustom && !isDomainVerified)
              }
            >
              {isPendingDomain || isSavingDomain ? <SpinnerBasic /> : null}
              {isSavingDomain ? "Saving..." : "Save Domain Settings"}
            </Button>
            <Button
              variant="outline"
              type="button"
              onClick={handleCancelDomainSettings}
              disabled={
                isPendingDomain || isLoadingProps || !domainSettingsDirty
              }
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Why Trust Us Card ... */}
      <Card>
        <CardHeader>
          <CardTitle>Why Trust Us</CardTitle>
          <CardDescription>
            Add reasons why organizations should trust your company. These will
            be displayed on your Trust Center home page.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {advantages.map((advantage, index) => (
            <div
              key={advantage}
              className="flex items-center justify-between gap-2 p-2 border rounded-md"
            >
              <p className="text-sm flex-grow">{advantage}</p>
              <div className="flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  disabled={index === 0}
                  onClick={() => moveAdvantageUp(index)}
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  disabled={index === advantages.length - 1}
                  onClick={() => moveAdvantageDown(index)}
                >
                  <ArrowDown className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeAdvantage(index)}
                >
                  <XCircle className="h-4 w-4 text-destructive" />
                </Button>
              </div>
            </div>
          ))}
          {advantages.length < MAX_ADVANTAGES && (
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={newAdvantage}
                onChange={(e) => setNewAdvantage(e.target.value)}
                placeholder="Add a new advantage"
                className="flex-grow"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    addAdvantage();
                  }
                }}
              />
              <Button
                variant="outline"
                onClick={addAdvantage}
                disabled={
                  isPendingTrustAdvantages ||
                  isLoadingProps ||
                  !newAdvantage.trim()
                }
              >
                <PlusCircle className="h-4 w-4 mr-2" /> Add
              </Button>
            </div>
          )}
          <p className="text-sm text-muted-foreground">
            {advantages.length}/{MAX_ADVANTAGES} advantages. Each advantage can
            be up to 255 characters.
          </p>
          <Button
            type="button"
            onClick={handleAdvantagesSubmit}
            disabled={isPendingTrustAdvantages || isLoadingProps}
          >
            {isPendingTrustAdvantages ? <SpinnerBasic /> : null}
            Save Trust Advantages
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
