"use server";

import { getServerSession } from "@/lib/get-session";
import { guardedPrisma } from "@/lib/prisma";
import {
  StaticSectionCard,
  MaturityLevel,
  PermissionLevel,
  MappedDocument,
} from "../../_components/shared/section-cards.types";
import { OverviewSettingsType } from "../_types/settings";
import { z } from "zod";
import {
  TrustCenterDocument,
  TrustCenterSectionCard,
} from "@askinfosec/database";
import { revalidatePath } from "next/cache";
import {
  documentServerSchema,
  DocumentFormData,
} from "../_lib/validation/document-schema";
import { getDocs, getSingleDocFile } from "@/services/server/docs";
import {
  MappedSectionCard,
  PREDEFINED_SECTION_CARDS,
} from "../../_components/shared/section-cards";

// Define the return type for the main server action
export interface TrustCenterManageData {
  overviewContent: OverviewSettingsType | null;
  allDocuments: MappedDocument[]; // Flattened list for the Documents card
  sectionCardsByCategory: Record<string, MappedSectionCard[]>; // Grouped for the main sections
}

// Helper to safely parse JSON fields from Prisma
function safeParseJson<T>(json: any, fallback: T | null = null): T | null {
  if (json === null) return fallback;

  try {
    // For Prisma Json fields, they already come back as parsed objects
    return json as T;
  } catch (error) {
    console.error("Error processing JSON:", error);
    return fallback;
  }
}

// Helper to map DB document to MappedDocument, applying defaults
function mapDbDocument(
  doc: TrustCenterDocument,
  sectionCard: TrustCenterSectionCard | StaticSectionCard,
): MappedDocument {
  return {
    id: doc.id,
    title: doc.title ?? "Untitled Document",
    description: doc.description ?? "No description",
    maturityLevel:
      (doc.maturity_level as MaturityLevel) ?? MaturityLevel.NOT_STARTED,
    permissionLevel:
      (doc.permission_level as PermissionLevel) ?? PermissionLevel.RESTRICTED,
    documentUrl: doc.document_url ?? "",
    isHidden: doc.is_hidden ?? false,
    sectionCardId: sectionCard.id,
    sectionCardCategory: sectionCard.category ?? "Uncategorized",
    isFeatured: doc.is_featured ?? false,
  };
}

// Helper to map static document to MappedDocument, applying defaults
function mapStaticDocument(
  doc: any, // Use any as a temporary workaround for diverse static structures
  sectionCard: StaticSectionCard,
): MappedDocument {
  return {
    id: doc.id,
    title: doc.title ?? "Untitled Document",
    description: doc.description ?? "No description",
    maturityLevel: doc.maturityLevel ?? MaturityLevel.NOT_STARTED,
    permissionLevel: doc.permissionLevel ?? PermissionLevel.RESTRICTED,
    documentUrl: doc.documentUrl ?? "",
    isHidden: doc.isHidden ?? false,
    sectionCardId: sectionCard.id,
    sectionCardCategory: sectionCard.category ?? "Uncategorized",
    isFeatured: doc.isFeatured ?? false,
  };
}

export async function getTrustCenterOverviewData(
  orgId: string,
): Promise<OverviewSettingsType | null> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    const prisma = guardedPrisma({ orgId, userId });

    const settings = await prisma.trustCenterSettings.findFirst({
      where: { organization_id: orgId },
      select: { overview: true },
    });

    // If no settings exist or overview is null, return default
    if (!settings || !settings.overview) {
      console.log("No overview settings found, returning default");
      return { description: "" };
    }

    // No need for complex parsing since Prisma returns JSON fields as parsed objects
    return settings.overview as OverviewSettingsType;
  } catch (error) {
    console.error("Error fetching Trust Center overview data:", error);
    throw new Error("Failed to load Trust Center data.");
  }
}

export async function getTrustCenterComplianceCardData(
  orgId: string,
): Promise<MappedSectionCard | null> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    const prisma = guardedPrisma({ orgId, userId });

    const complianceCard = await prisma.trustCenterSectionCard.findFirst({
      where: { organization_id: orgId, category: "Compliance" },
    });

    if (!complianceCard) {
      return null;
    }

    const documents = await prisma.trustCenterDocument.findMany({
      where: { organization_id: orgId, section_card_id: complianceCard.id },
    });

    const mappedDocuments = documents.map((doc) =>
      mapDbDocument(doc, complianceCard),
    );

    return {
      ...complianceCard,
      documents: mappedDocuments,
      displayOrder: complianceCard.display_order,
      isHidden: complianceCard.is_hidden,
      description: complianceCard.description ?? "",
      category: complianceCard.category ?? "",
      title: complianceCard.title ?? "",
    };
  } catch (error) {
    console.error("Error fetching Trust Center compliance card data:", error);
    throw new Error("Failed to load Trust Center data.");
  }
}

export async function getTrustCenterAllSectionCardsData(
  orgId: string,
): Promise<MappedSectionCard[]> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    const prisma = guardedPrisma({ orgId, userId });

    // 1. Fetch all section cards from DB
    const allSectionCards = await prisma.trustCenterSectionCard.findMany({
      where: { organization_id: orgId },
    });

    // 2. Fetch all documents from DB
    const allDbDocuments = await prisma.trustCenterDocument.findMany({
      where: { organization_id: orgId },
    });

    // 3. Create a map of documents by section card ID for quick lookup
    const documentsBySectionCardId = new Map<string, TrustCenterDocument[]>();
    allDbDocuments.forEach((doc) => {
      if (doc.section_card_id) {
        if (!documentsBySectionCardId.has(doc.section_card_id)) {
          documentsBySectionCardId.set(doc.section_card_id, []);
        }
        documentsBySectionCardId.get(doc.section_card_id)?.push(doc);
      }
    });

    const predefinedSectionCards = PREDEFINED_SECTION_CARDS;

    // 4. Find predefined cards not in DB
    const predefinedSectionCardsNotInDb = predefinedSectionCards.filter(
      // A predefined card is considered "not in DB" if no DB card has the same template_id
      (card) =>
        !allSectionCards.some((dbCard) => dbCard.template_id === card.id),
    );

    // 5. Merge cards with their documents
    const mergedCards: MappedSectionCard[] = [
      // Handle predefined cards not in DB
      ...predefinedSectionCardsNotInDb.map((card) => {
        // For predefined cards not in DB, use their static document definitions
        const staticDocuments = card.documents || [];
        const mappedDocs = staticDocuments.map((doc) =>
          mapStaticDocument(doc, card),
        );

        return {
          ...card,
          displayOrder: card.displayOrder,
          isHidden: card.isHidden ?? false,
          description: card.description ?? "",
          category: card.category ?? "",
          documents: mappedDocs,
        };
      }),

      // Handle cards from DB
      ...allSectionCards.map((card) => {
        // Get DB documents for this card
        const cardDocuments = documentsBySectionCardId.get(card.id) || [];
        // Map DB documents to MappedDocument type
        const mappedDocs = cardDocuments.map((doc) => mapDbDocument(doc, card));

        // Get predefined documents for this card (if it's a predefined card)
        const predefinedCard = predefinedSectionCards.find(
          (pc) => pc.id === card.template_id,
        );
        let predefinedDocs: MappedDocument[] = [];

        // Include predefined documents that aren't in the DB yet
        if (predefinedCard && predefinedCard.documents) {
          predefinedDocs = predefinedCard.documents
            .filter((pdoc) => {
              // Check if a database document with the same title already exists for this card
              return !cardDocuments.some((dbdoc) => dbdoc.title === pdoc.title);
            })
            .map((pdoc) => mapStaticDocument(pdoc, predefinedCard));
        }

        return {
          ...card,
          displayOrder: card.display_order,
          isHidden: card.is_hidden,
          description: card.description ?? "",
          category: card.category ?? "",
          documents: [...mappedDocs, ...predefinedDocs],
        };
      }),
    ];

    const finalMergedCards = mergedCards.sort(
      (a, b) => a.displayOrder - b.displayOrder,
    );
    return finalMergedCards;
  } catch (error) {
    console.error("Error fetching Trust Center all section cards data:", error);
    throw new Error("Failed to load Trust Center data.");
  }
}

export async function getTrustCenterAllDocumentsData(
  orgId: string,
): Promise<MappedDocument[]> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    const prisma = guardedPrisma({ orgId, userId });

    const allDocuments = await prisma.trustCenterDocument.findMany({
      where: { organization_id: orgId },
    });

    // Get all predefined documents from the static configuration
    const predefinedDocuments = PREDEFINED_SECTION_CARDS.flatMap(
      // For each predefined card, get its documents array (or an empty array if none)
      (card) => card.documents || [],
    )
      // Filter the predefined documents to exclude those that already exist in the database
      .filter((staticDoc) => {
        // Find the static card context for this document
        const staticCard = PREDEFINED_SECTION_CARDS.find((sc) =>
          sc.documents?.some((sd) => sd.id === staticDoc.id),
        );

        if (!staticCard) return true; // Keep if we can't find the card context

        // Check if a database document with the same title already exists
        const existsInDb = allDocuments.some((dbDoc) => {
          // Compare by title - if a DB document with the same title exists, exclude the static one
          return dbDoc.title === staticDoc.title;
        });

        return !existsInDb; // Keep the static document only if it doesn't exist in DB
      });

    const mergedDocuments = [...predefinedDocuments, ...allDocuments];

    // Map documents correctly based on their type
    const finalDocuments = mergedDocuments.map((doc) => {
      // Check if it's a DB document (e.g., has organization_id)
      if ("organization_id" in doc && "created_at" in doc) {
        // Find card context (simplified fallback - requires refinement if exact card needed)
        const cardContextFallback: StaticSectionCard = {
          id: doc.section_card_id ?? "unknown",
          title: "Unknown",
          description: "Unknown Description",
          category: "Uncategorized",
          displayOrder: 999,
          isHidden: false,
        };
        // Ideally, fetch the actual card if needed, here we use fallback
        return mapDbDocument(doc as TrustCenterDocument, cardContextFallback);
      } else {
        // Assume it's a static document
        // Find the static card context
        const staticCardContext = PREDEFINED_SECTION_CARDS.find((sc) =>
          sc.documents?.some((sd) => sd.id === doc.id),
        ) ?? {
          id: "unknown",
          title: "Unknown",
          description: "Unknown Description",
          category: "Uncategorized",
          displayOrder: 999,
          isHidden: false,
        }; // Fallback card
        return mapStaticDocument(doc, staticCardContext);
      }
    });
    // console.log("finalDocuments", finalDocuments);
    return finalDocuments;
  } catch (error) {
    console.error("Error fetching Trust Center all documents data:", error);
    throw new Error("Failed to load Trust Center data.");
  }
}

export async function getTrustCenterManageData2(
  orgId: string,
): Promise<TrustCenterManageData> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }

  // Fetch individual data parts
  const overviewContent = await getTrustCenterOverviewData(orgId);
  const allDocuments = await getTrustCenterAllDocumentsData(orgId);
  const allSectionCards = await getTrustCenterAllSectionCardsData(orgId);

  // Process allSectionCards into the required Record structure
  const sectionCardsByCategory: Record<string, MappedSectionCard[]> = {};
  allSectionCards.forEach((card) => {
    // Include all categories, only exclude hidden cards
    if (!sectionCardsByCategory[card.category]) {
      sectionCardsByCategory[card.category] = [];
    }
    // Keep the populated documents from getTrustCenterAllSectionCardsData
    sectionCardsByCategory[card.category].push(card);
  });

  return {
    overviewContent,
    allDocuments,
    sectionCardsByCategory,
  };
}

export async function getTrustCenterManageData(
  orgId: string,
): Promise<TrustCenterManageData> {
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    const prisma = guardedPrisma({ orgId, userId });

    // 1. Fetch organization settings (overview)
    const settings = await prisma.trustCenterSettings.findFirst({
      where: { organization_id: orgId },
    });
    const overviewContent = safeParseJson<OverviewSettingsType>(
      settings?.overview,
      {
        description: "",
      },
    );

    // 2. Fetch ALL TrustCenterDocuments for the org into a Map by ID
    const allDbDocuments = await prisma.trustCenterDocument.findMany({
      where: { organization_id: orgId },
    });
    const dbDocsMap = new Map(allDbDocuments.map((doc) => [doc.id, doc]));

    // 3. Fetch ALL TrustCenterSectionCards for the org into a Map by template_id (if exists) or id
    const allDbSectionCards = await prisma.trustCenterSectionCard.findMany({
      where: { organization_id: orgId },
      orderBy: { display_order: "asc" },
    });
    // Key predefined-linked cards by template_id, custom cards by their own id
    const dbCardsByTemplateId = new Map<string, TrustCenterSectionCard>();
    const customDbCards: TrustCenterSectionCard[] = [];
    for (const card of allDbSectionCards) {
      if (card.template_id) {
        dbCardsByTemplateId.set(card.template_id, card);
      } else {
        customDbCards.push(card);
      }
    }

    // 4. Build merged cards, prioritizing DB data
    const mergedCards: MappedSectionCard[] = [];
    const processedDbDocIds = new Set<string>(); // Keep track of DB docs added

    // Iterate through static card definitions to establish structure and defaults
    for (const staticCard of PREDEFINED_SECTION_CARDS) {
      const dbCard = dbCardsByTemplateId.get(staticCard.id); // Find corresponding DB card, if exists
      const currentCardDocuments: MappedDocument[] = [];

      // Process documents defined statically for this card
      for (const staticDoc of staticCard.documents || []) {
        const dbDoc = dbDocsMap.get(staticDoc.id); // Find corresponding DB doc, if exists

        if (dbDoc) {
          // DB Document exists - Use its data
          currentCardDocuments.push(mapDbDocument(dbDoc, dbCard ?? staticCard));
          processedDbDocIds.add(dbDoc.id); // Mark this DB doc as processed
        } else {
          // No DB Document - Use static data as fallback/default
          currentCardDocuments.push(mapStaticDocument(staticDoc, staticCard));
          // Note: We don't add staticDoc.id to processedDbDocIds because it wasn't a DB doc
        }
      }

      // Add any *additional* DB documents linked specifically to this card's ID
      // that were NOT defined in the static list (custom docs added to a predefined card)
      if (dbCard) {
        for (const dbDoc of allDbDocuments) {
          if (
            dbDoc.section_card_id === dbCard.id &&
            !processedDbDocIds.has(dbDoc.id)
          ) {
            currentCardDocuments.push(mapDbDocument(dbDoc, dbCard));
            processedDbDocIds.add(dbDoc.id); // Mark this custom DB doc as processed
          }
        }
      }

      // Create the final card object for this static definition
      mergedCards.push({
        id: dbCard?.id ?? staticCard.id,
        title: dbCard?.title ?? staticCard.title,
        description: dbCard?.description ?? staticCard.description,
        category: dbCard?.category ?? staticCard.category,
        displayOrder: dbCard?.display_order ?? staticCard.displayOrder,
        isHidden: dbCard?.is_hidden ?? staticCard.isHidden ?? false,
        documents: currentCardDocuments.sort((a, b) =>
          a.title < b.title ? -1 : 1,
        ),
      });

      dbCardsByTemplateId.delete(staticCard.id); // Remove card from map as it's handled
    }

    // 5. Add any remaining DB cards that weren't in the static list (custom cards)
    for (const dbCard of customDbCards) {
      const customCardDocuments: MappedDocument[] = [];
      // Find DB documents belonging ONLY to this custom card
      for (const dbDoc of allDbDocuments) {
        if (
          dbDoc.section_card_id === dbCard.id &&
          !processedDbDocIds.has(dbDoc.id)
        ) {
          customCardDocuments.push(mapDbDocument(dbDoc, dbCard));
          processedDbDocIds.add(dbDoc.id);
        }
      }

      // Only add the custom card if it's not hidden or has documents
      if (!dbCard.is_hidden || customCardDocuments.length > 0) {
        mergedCards.push({
          id: dbCard.id,
          title: dbCard.title,
          description: dbCard.description ?? "",
          category: dbCard.category ?? "Custom",
          displayOrder: dbCard.display_order,
          isHidden: dbCard.is_hidden,
          documents: customCardDocuments.sort((a, b) =>
            a.title < b.title ? -1 : 1,
          ),
        });
      }
    }

    // 6. Add any remaining Orphan DB documents (docs not processed yet and without a direct card link or whose card was static but removed?)
    // This step might indicate data inconsistency if orphans exist. For now, we focus on docs linked to cards.
    // Consider logging unprocessedDbDocIds if necessary.

    // 7. Sort all merged cards by displayOrder
    mergedCards.sort((a, b) => a.displayOrder - b.displayOrder);

    // 8. Prepare final data structure (similar to before, but based on the new mergedCards)

    // Flatten all documents from the final merged cards
    const allDocumentsFromMerged = mergedCards.flatMap(
      (card) => card.documents,
    );

    // Create a unique list of documents based on ID, prioritizing non-hidden ones
    const uniqueDocumentsMap = new Map<string, MappedDocument>();
    allDocumentsFromMerged.forEach((doc) => {
      const existing = uniqueDocumentsMap.get(doc.id);
      if (!existing || (!doc.isHidden && existing.isHidden)) {
        uniqueDocumentsMap.set(doc.id, doc);
      } else if (!existing) {
        uniqueDocumentsMap.set(doc.id, doc); // Add if not existing at all
      }
    });
    // Filter final list for non-hidden documents only for the "All Documents" view
    const allDocumentsView = Array.from(uniqueDocumentsMap.values()).filter(
      (doc) => !doc.isHidden,
    );

    // Group cards by category for the main section display
    const sectionCardsByCategory: Record<string, MappedSectionCard[]> = {};
    mergedCards.forEach((card) => {
      // Exclude only fully hidden cards from the main sections
      if (!card.isHidden) {
        // Filter documents within the card to show only non-hidden ones in the section view
        const visibleDocuments = card.documents.filter((doc) => !doc.isHidden);

        // Include the card if it has visible documents or belongs to always-shown categories
        if (
          visibleDocuments.length > 0 ||
          card.category === "Security Policies" || // Example: always show these categories
          card.category === "Reports & Certificates"
        ) {
          const filteredCard = {
            ...card,
            documents: visibleDocuments, // Use only visible documents for this view
          };
          if (!sectionCardsByCategory[card.category]) {
            sectionCardsByCategory[card.category] = [];
          }
          sectionCardsByCategory[card.category].push(filteredCard);
        }
      }
    });

    return {
      overviewContent,
      allDocuments: allDocumentsView, // Flattened, unique, non-hidden docs
      sectionCardsByCategory, // Grouped, non-compliance, non-hidden cards with non-hidden docs
    };
  } catch (error) {
    console.error("Error fetching Trust Center manage data:", error);
    // Return a default/empty state or rethrow depending on desired page behavior
    throw new Error("Failed to load Trust Center data.");
  }
}

// --- Add New Server Action ---
export async function updateTrustCenterDocument({
  orgId,
  data,
}: {
  orgId: string;
  data: DocumentFormData;
}) {
  console.log(`Attempting to update document ${data.id} for org ${orgId}`);
  const session = await getServerSession();
  if (!session?.user?.id) {
    throw new Error("Unauthorized: User session not found.");
  }
  const userId = session.user.id;

  try {
    // Re-validate data on the server
    const validatedData = documentServerSchema.parse(data);
    console.log("Validated data:", validatedData);

    const prisma = guardedPrisma({ orgId, userId });

    // 1. First check if the section card exists for this organization
    const sectionCardId = validatedData.sectionCardId;
    let sectionCard = await prisma.trustCenterSectionCard.findFirst({
      where: {
        id: sectionCardId,
        organization_id: orgId,
      },
    });

    // 2. If the section card doesn't exist in the database, return error
    // Since all section cards now use CUIDs, if we can't find it by ID, it doesn't exist
    if (!sectionCard) {
      console.error(`Section card ${sectionCardId} not found for org ${orgId}`);
      return {
        success: false,
        error:
          "Invalid section card ID. The specified section card does not exist.",
      };
    }

    // Check if user actually has permission to update this org's settings/docs
    // (guardedPrisma helps, but an explicit check might be needed depending on rules)
    // Example: Fetch the document first to ensure it belongs to the orgId
    const existingDoc = await prisma.trustCenterDocument.findUnique({
      where: { id: validatedData.id, organization_id: orgId },
      select: { id: true },
    });

    if (!existingDoc) {
      console.error(
        `Document ${validatedData.id} not found or does not belong to org ${orgId}`,
      );
      // This is actually not an error, but we need to create a new record in the trustCenterDocument table
      // Lets implement this and return succeful once done.
      const newDoc = await prisma.trustCenterDocument.create({
        data: {
          organization_id: orgId,
          title: validatedData.title,
          description: validatedData.description,
          is_hidden: validatedData.isHidden,
          is_featured: validatedData.isFeatured,
          maturity_level: validatedData.maturityLevel,
          permission_level: validatedData.permissionLevel,
          document_url: validatedData.documentUrl,
          section_card_id: sectionCard.id,
          created_by_id: userId,
          updated_by_id: userId,
          subject_id: validatedData.subjectId ?? "",
          subject_entity: validatedData.subjectEntity ?? "",
        },
      });
      console.log(`Document ${newDoc.id} created successfully.`);
      revalidatePath(`/[lang]/(private)/[org]/trust-center/manage`);
      return { success: true, document: newDoc };
    }

    console.log(`Updating document ${existingDoc.id} in DB...`);
    const updatedDocument = await prisma.trustCenterDocument.update({
      where: {
        id: existingDoc.id,
        organization_id: orgId, // Ensure we only update if org matches
      },
      data: {
        title: validatedData.title,
        description: validatedData.description,
        is_hidden: validatedData.isHidden,
        is_featured: validatedData.isFeatured,
        maturity_level: validatedData.maturityLevel,
        permission_level: validatedData.permissionLevel,
        document_url: validatedData.documentUrl,
        section_card_id: sectionCard.id,
        updated_by_id: userId,
      },
    });
    console.log(`Document ${updatedDocument.id} updated successfully.`);

    // Revalidate the manage page path
    revalidatePath(`/[lang]/(private)/[org]/trust-center/manage`);
    console.log(`Revalidated path for org ${orgId}`);

    return { success: true, document: updatedDocument };
  } catch (error) {
    console.error(
      `Failed to update document ${data.id} for org ${orgId}:`,
      error,
    );
    // Provide a more generic error message to the client
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid data provided.",
        details: error.errors,
      };
    }
    return { success: false, error: "Failed to update document." };
  }
}

// Add new function to get document options for dropdown
export async function getTrustCenterDocumentOptions(orgId: string) {
  try {
    // Get all documents from the Document Management system
    const documents = await getDocs(orgId);

    // Transform documents to the format expected by SingleSelect
    const documentOptions = documents.map((doc) => ({
      id: doc.id,
      label: doc.name,
      access_level: doc.access_level,
    }));

    return documentOptions;
  } catch (error) {
    console.error("Error fetching document options:", error);
    return [];
  }
}

// Add a new function to get document details
export async function getTrustCenterDocumentDetails(
  orgId: string,
  documentId: string,
) {
  try {
    if (!documentId) return null;

    // Get document details
    const document = await getSingleDocFile(documentId, orgId);
    if (!document) return null;

    return {
      id: document.id,
      name: document.name,
      path: document.path,
      documentType: document.document_type,
      createdBy: document.created_by,
      content: document.content,
    };
  } catch (error) {
    console.error(`Error fetching document details for ${documentId}:`, error);
    return null;
  }
}
