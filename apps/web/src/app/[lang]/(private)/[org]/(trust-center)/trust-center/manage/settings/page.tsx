"use client";

import React, { useEffect, useState, useTransition } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { AppearanceSettings } from "./_components/appearance-settings";
import { FunctionalitySettings } from "./_components/functionality-settings";
import { IntegrationSettings } from "./_components/integration-settings";
import { GeneralSettings } from "./_components/general-settings";
import { Button } from "@/components/ui/button";
import { RefreshCw, ArrowLeft } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import ContentBodyPageHeader from "~/src/components/header/content-body-page-header";
import { TrustCenterSettings } from "@askinfosec/database";
import { getTrustCenterSettingsByOrgId } from "./_server-actions/settings";

export default function TrustCenterSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const orgId = params.org as string;

  const [initialSettings, setInitialSettings] =
    useState<TrustCenterSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPending, startTransition] = useTransition();

  useEffect(() => {
    if (orgId) {
      setIsLoading(true);
      startTransition(async () => {
        try {
          const settings = await getTrustCenterSettingsByOrgId(orgId);
          setInitialSettings(settings);
        } catch (error) {
          console.error("Failed to load trust center settings:", error);
        } finally {
          setIsLoading(false);
        }
      });
    }
  }, [orgId]);

  return (
    <>
      <ContentBodyPageHeader>
        <div className="container flex h-20 items-center justify-between">
          <h1 className="text-2xl font-semibold text-slate-800">
            Trust Center Settings
          </h1>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={() =>
                router.push(`/${params.lang}/${params.org}/trust-center/manage`)
              }
            >
              <ArrowLeft className="h-4 w-4" />
              Back To Manage Trust
            </Button>
          </div>
        </div>
      </ContentBodyPageHeader>

      <Tabs defaultValue="general" className="w-full space-y-8">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="functionality">Functionality</TabsTrigger>
            <TabsTrigger value="integration" disabled>
              Integration
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="general">
          <GeneralSettings
            initialSettings={initialSettings}
            orgId={orgId}
            isLoading={isLoading || isPending}
          />
        </TabsContent>

        <TabsContent value="appearance">
          <AppearanceSettings />
        </TabsContent>

        <TabsContent value="functionality">
          <FunctionalitySettings />
        </TabsContent>

        <TabsContent value="integration">
          <IntegrationSettings />
        </TabsContent>
      </Tabs>
    </>
  );
}
