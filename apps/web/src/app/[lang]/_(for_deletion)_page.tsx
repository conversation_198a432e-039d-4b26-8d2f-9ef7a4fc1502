import { redirect } from "next/navigation";
import { auth } from "@/auth";
import { bypassedPrisma } from "@/lib/prisma";
import { PrismaClient } from "@askinfosec/database";

const prisma = bypassedPrisma as unknown as PrismaClient;

export default async function LangIndexPage() {
  // Get the current session
  const session = await auth();

  // Redirect based on authentication status
  if (session) {
    // User is authenticated, redirect to private route
    const userProfileSettings = await prisma.userProfile.findFirst({
      where: {
        user_id: session.user.id,
      },
      select: {
        settings: true,
      },
    });
    const { defaultOrganizationId } = userProfileSettings?.settings as {
      defaultOrganizationId: string;
    };

    if (!defaultOrganizationId) {
      const org = await prisma.member.findFirst({
        where: {
          user_id: session.user.id,
        },
      });
      if (!org) {
        throw new Error("No default organization found");
      }
      redirect(`/${"en"}/${org.id}`);
    }

    redirect(`/${"en"}/${defaultOrganizationId}`);
  } else {
    // User is not authenticated, redirect to sign in
    redirect(`/en/signin`);
  }
}
