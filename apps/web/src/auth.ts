import NextAuth from "next-auth";
import authConfig from "./auth.config";
import { authAdapter } from "./lib/auth-helpers";

// Check for Edge Runtime
const isEdgeRuntime =
  typeof process !== "undefined" && process.env.NEXT_RUNTIME === "edge";

// Enable debug mode based on environment variable
const debug = process.env.NEXTAUTH_DEBUG === "true";

// Log configuration details if in debug mode
if (debug) {
  console.log("NextAuth Configuration:");
  console.log("- Edge Runtime:", isEdgeRuntime);
  console.log("- Session Strategy:", isEdgeRuntime ? "jwt" : "database");
  console.log(
    "- Session Max Age:",
    Number(process.env.SESSION_DURATION || 3600),
  );
  console.log("- Using Adapter:", !isEdgeRuntime);
  console.log("- Trust Host:", true);
}

// Configure NextAuth differently based on runtime
const nextAuth = NextAuth({
  ...authConfig,
  // Only use adapter in Node.js runtime
  // ...(isEdgeRuntime ? {} : { adapter: authAdapter }),
  adapter: authAdapter,
  session: {
    // Use JWT strategy in Edge Runtime, database strategy in Node.js
    strategy: "database",
    maxAge: Number(process.env.SESSION_DURATION || 3600),
    updateAge: Number(process.env.SESSION_UPDATE_AGE || 0),
  },
  // Add trust host configuration for CSRF protection
  trustHost: true,
  // Override debug setting if needed
  debug: debug || authConfig.debug,
  // Suppress noisy experimental WebAuthn warning while keeping other logs
  logger: {
    warn: (code: any, ...message: any[]) => {
      if (code === "experimental-webauthn") return;
      // Fallback to console warn for other warnings
      // eslint-disable-next-line no-console
      console.warn("[auth][warn][" + String(code) + "]", ...message);
    },
    error: (code: any, ...message: any[]) => {
      // eslint-disable-next-line no-console
      console.error("[auth][error][" + String(code) + "]", ...message);
    },
  },
  // experimental: { enableWebAuthn: true },
});

export const { auth, handlers, signIn, signOut } = nextAuth as any;

// Force Node.js runtime for auth-related routes
export const runtime = "nodejs";
