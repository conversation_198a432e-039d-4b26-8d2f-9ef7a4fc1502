import {
  Controller,
  Get,
  Param,
  UseGuards,
  Version,
  Req,
} from '@nestjs/common';
import { ApiSurface } from '../../../common/decorators/api-surface.decorator';
import { CustomAuthGuard } from '../../auth/guards/custom-auth.guard';
import { RlsContextGuard } from '../../db-drizzle/guards/rls-context.guard';
import { OrganizationModulesService } from '../services/organization-modules.service';
import { SessionContext } from '@askinfosec/database-drizzle';
import { ExtendedRequest } from 'src/common/types/extended-request';

@Controller('organizations/:organizationId/modules')
@UseGuards(CustomAuthGuard, RlsContextGuard)
export class OrganizationModulesController {
  constructor(
    private readonly organizationModulesService: OrganizationModulesService,
  ) {}

  /**
   * GET /v1/organizations/:organizationId/modules (external surface)
   * -------------------------------------------------------------
   * Returns the complete module access for a specific organization.
   * This endpoint calculates module access based on:
   * 1. Active subscriptions and their associated products
   * 2. Organization module overrides (enabled/disabled modules)
   * 3. Automatic dependency resolution
   *
   * Response includes:
   * - List of modules the organization has access to
   * - Source of each module (subscription, override, or dependency)
   * - Module details (id, name, displayName, isEnabled)
   * - Override reason (if applicable)
   *
   * Note: This endpoint is the primary way for the frontend to determine
   * what modules and features should be available to users in the organization.
   * The final module access is stored in organization.modules field but this
   * endpoint provides the calculated view including dependencies.
   */
  @ApiSurface('internal')
  @Version('1')
  @Get()
  async getOrganizationModules(
    @Param('organizationId') organizationId: string,
    @Req() request: ExtendedRequest,
  ) {
    const modules =
      await this.organizationModulesService.getOrganizationModuleAccess(
        organizationId,
        {
          organizationId,
          userId: request.userId,
          bypassRls: request.bypassRls,
        } as SessionContext,
      );
    return { modules };
  }

  /**
   * FUTURE SUPER-ADMIN ENDPOINTS (NOT IMPLEMENTED)
   * -------------------------------------------------------------
   *
   * These endpoints are planned for future implementation when the separate
   * admin platform is developed. They will be restricted to super-admin users
   * and will use a different authentication mechanism that bypasses RLS.
   *
   * Current Limitation:
   * - JWT-based authentication requires organization membership
   * - AskInfosec super-admins cannot be added as users/members of customer organizations
   * - Need separate admin platform with different PostgreSQL user (RLS bypass)
   *
   * Planned Endpoints:
   *
   * POST /v1/organizations/:organizationId/module-overrides (super-admin only)
   * - Add module overrides for specific organization
   * - Enable/disable modules independent of subscriptions
   * - Update organization.modules field with final module list
   * - Handle dependency resolution automatically
   *
   * PUT /v1/organizations/:organizationId/module-overrides/:moduleId (super-admin only)
   * - Update existing module override
   * - Change enable/disable status
   * - Update override reason
   * - Update organization.modules field
   *
   * DELETE /v1/organizations/:organizationId/module-overrides/:moduleId (super-admin only)
   * - Remove module override for specific organization
   * - Revert to subscription-based access only
   * - Update organization.modules field
   *
   * GET /v1/admin/organizations/:organizationId/module-access (super-admin only)
   * - View complete module access breakdown for organization
   * - Show subscription-based modules
   * - Show override-based modules
   * - Show dependency resolution
   * - Show organization.modules field content
   *
   * POST /v1/admin/organizations/:organizationId/sync-modules (super-admin only)
   * - Manually sync organization.modules field
   * - Recalculate from subscriptions + overrides
   * - Force dependency resolution
   * - Useful for fixing inconsistencies
   *
   * GET /v1/admin/organizations/:organizationId/subscriptions (super-admin only)
   * - View all subscriptions for organization
   * - Show subscription status and details
   * - Show associated products and modules
   *
   * Implementation Requirements:
   * 1. Separate admin platform with different auth mechanism
   * 2. PostgreSQL user with RLS bypass capabilities
   * 3. Super-admin role/authorization system
   * 4. Integration with Stripe for subscription management
   * 5. Automatic dependency resolution
   * 6. Audit logging for all super-admin actions
   * 7. Validation to prevent breaking existing functionality
   *
   * Business Logic:
   * - Module overrides take precedence over subscription-based access
   * - Dependencies are automatically resolved and added
   * - Core modules cannot be disabled via overrides
   * - Changes to organization.modules field trigger frontend updates
   * - All changes are logged for audit purposes
   */
}
