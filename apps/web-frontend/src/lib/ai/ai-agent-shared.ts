import {
  type AgentInvokeRequest,
  type RequestContext,
} from "@/lib/ai/ai-agent-service";

function generateId(): string {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

export function buildAgentInvokeRequest(
  message: string,
  sessionId: string,
  organizationId: string,
  userId: string,
  projectId?: string,
): AgentInvokeRequest {
  const timestamp = new Date().toISOString();
  return {
    agent_name: "ask_ai",
    input: message,
    context: {
      sessionId,
      organizationId,
      userId,
      metadata: {
        source: "web-frontend",
        timestamp,
        query_type: "general",
        projectId,
      },
      requestId: generateId(),
      timestamp,
      platform: "web",
    },
  };
}

export function buildCorrelationContext(sessionId: string): RequestContext {
  return {
    requestId: generateId(),
    traceId: generateId(),
    spanId: generateId(),
    timestamp: Date.now(),
    sessionId,
  };
}

export function getPublicAgentBaseUrl(fallback: string): string {
  const env =
    (typeof process !== "undefined" &&
      (process as unknown as { env?: Record<string, string | undefined> })
        .env) ||
    {};
  return env.ANTER_API_URL || fallback;
}
