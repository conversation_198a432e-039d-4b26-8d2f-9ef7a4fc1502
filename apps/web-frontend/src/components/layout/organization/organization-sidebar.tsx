"use client";

import React from "react";
import {
  Settings,
  CreditCard,
  Users,
  Package2,
  WandSparkles,
} from "lucide-react";
import { OrganizationSidebarProps } from "@/types/organization";
import { usePathname, useRouter } from "next/navigation";
import { useOrganization } from "@/providers/organization-provider";
import { buildOrgRoute } from "@/lib/routes";
import { SidebarLayout, SidebarNav } from "@/components/layout/sidebar";

const ORGANIZATION_NAV_ITEMS = [
  {
    id: "general",
    name: "General",
    description: "General organization settings",
    path: "/settings/general",
    icon: Settings,
  },
  {
    id: "members",
    name: "Members",
    description: "Team member management",
    path: "/settings/members",
    icon: Users,
  },
  {
    id: "billing",
    name: "Billing & Subscription",
    description: "Manage billing and subscription",
    path: "/settings/billing",
    icon: CreditCard,
  },
  {
    id: "products",
    name: "Products",
    description: "Manage products",
    path: "/settings/products",
    icon: Package2,
  },
  {
    id: "ai",
    name: "AI",
    description: "AI",
    path: "/settings/ai",
    icon: WandSparkles,
  },
];

export const OrganizationSidebar: React.FC<OrganizationSidebarProps> = ({
  isCollapsed,
  onClose,
  isMobile = false,
  currentPath: _currentPath = "",
  onBackToDashboard,
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const { currentOrganizationId } = useOrganization();

  const orgNavItems = ORGANIZATION_NAV_ITEMS.map((item) => ({
    ...item,
    path: currentOrganizationId
      ? buildOrgRoute(currentOrganizationId, item.path)
      : item.path,
  }));

  const isRouteActive = (routePath: string): boolean => {
    return pathname.includes(routePath);
  };

  const handleBackToDashboard = () => {
    onBackToDashboard();
    if (currentOrganizationId) {
      router.push(buildOrgRoute(currentOrganizationId, "/dashboard"));
    }
  };

  return (
    <SidebarLayout
      layoutType="organization"
      isCollapsed={isCollapsed}
      isMobile={isMobile}
      onClose={onClose}
      showBack
      onBack={handleBackToDashboard}
    >
      <SidebarNav
        items={orgNavItems as any}
        isCollapsed={isCollapsed}
        currentPath={pathname}
        isRouteActive={(p?: string) => (p ? isRouteActive(p) : false)}
      />
    </SidebarLayout>
  );
};
