"use client";

import React from "react";
import { OrganizationLayoutProps } from "@/types/organization";
import { RBACSecurityTest } from "@/components/rbac-security-test";
import { SidebarPageLayout } from "@/components/layout/sidebar-main-layout";

export const OrganizationLayout: React.FC<OrganizationLayoutProps> = ({
  children,
  sidebar,
  mobileMenu,
  _headerActions,
  onMobileMenuToggle,
  onSidebarToggle,
  headerTitle,
  headerDescription,
  mobileMenuOpen = false,
}) => {
  return (
    <>
      <SidebarPageLayout
        sidebar={sidebar}
        mobileMenu={mobileMenu}
        _headerActions={_headerActions}
        onMobileMenuToggle={onMobileMenuToggle}
        onSidebarToggle={onSidebarToggle}
        headerTitle={headerTitle}
        headerDescription={headerDescription}
        mobileMenuOpen={mobileMenuOpen}
      >
        {children}
      </SidebarPageLayout>
      <RBACSecurityTest />
    </>
  );
};
