"use client";

import React from "react";
import { Header } from "@/components/header/header";
import { PageHeader } from "@/components/header/page-header";

interface SidebarPageLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  mobileMenu?: React.ReactNode;
  headerTitle?: string;
  headerDescription?: string;
  _headerActions?: React.ReactNode;
  onMobileMenuToggle?: () => void;
  onSidebarToggle?: () => void;
  mobileMenuOpen?: boolean;
  pageFooter?: React.ReactNode; // optional global page footer slot
}

export const SidebarPageLayout: React.FC<SidebarPageLayoutProps> = ({
  children,
  sidebar,
  mobileMenu,
  _headerActions,
  onMobileMenuToggle,
  onSidebarToggle,
  headerTitle,
  headerDescription,
  mobileMenuOpen = false,
  pageFooter,
}) => {
  const showHamburgerMenu = !!sidebar;

  return (
    <div
      className="flex h-screen transition-colors duration-200"
      suppressHydrationWarning
    >
      {/* Desktop Sidebar */}
      {sidebar && <div className="hidden lg:block">{sidebar}</div>}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 bg-background">
        {/* Main Header */}
        <Header
          showHamburgerMenu={showHamburgerMenu}
          onMobileMenuToggle={onMobileMenuToggle}
          onSidebarToggle={onSidebarToggle}
          mobileMenuOpen={mobileMenuOpen}
          headerActions={_headerActions}
        />

        {/* Page Header */}
        <PageHeader
          headerTitle={headerTitle}
          headerDescription={headerDescription}
        />

        {/* Scrollable Content */}
        <div className="flex-1 overflow-auto">
          <main className="p-4 lg:p-6 bg-background transition-colors duration-200">
            {children}
          </main>
        </div>

        {/* Optional global page footer */}
        {pageFooter ?? null}
      </div>

      {/* Mobile Menu */}
      {mobileMenu}
    </div>
  );
};
