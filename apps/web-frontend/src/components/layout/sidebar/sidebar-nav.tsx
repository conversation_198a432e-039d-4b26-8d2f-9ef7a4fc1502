"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { SidebarNavItem } from "@/types/sidebar";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface SidebarNavProps {
  items: SidebarNavItem[];
  isCollapsed?: boolean;
  currentPath?: string;
  isRouteActive?: (path?: string) => boolean;
  isChildRouteActive?: (path?: string) => boolean;
}

export function SidebarNav({
  items,
  isCollapsed = false,
  currentPath = "",
  isRouteActive = (p?: string) => !!p && currentPath.includes(p),
  isChildRouteActive = (p?: string) => !!p && currentPath.includes(p),
}: SidebarNavProps) {
  const [collapsed, setCollapsed] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    items.forEach((i) => {
      if (i.children) initial[i.id] = true;
    });
    return initial;
  });

  const [selectedParent, setSelectedParent] = useState<string | null>(null);

  function toggle(id: string) {
    setCollapsed((prev) => ({ ...prev, [id]: !prev[id] }));
    setSelectedParent(id);
  }

  function renderItem(item: SidebarNavItem) {
    const itemCollapsed = collapsed[item.id];
    const isActive = (() => {
      if (item.children && item.children.length > 0) {
        const hasActiveChild = item.children.some((c) =>
          c.path
            ? isChildRouteActive(c.path) || currentPath.includes(c.path)
            : false,
        );
        return hasActiveChild || selectedParent === item.id;
      }
      return (
        isRouteActive(item.path) ||
        (!!item.path && currentPath.includes(item.path))
      );
    })();

    if (item.children && item.children.length > 0) {
      return (
        <div key={item.id} className="space-y-1">
          <div className="relative">
            {item.path ? (
              <Link
                href={item.path}
                className={cn(
                  "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group h-10",
                  isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
                  isActive
                    ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
                )}
              >
                <item.icon
                  className={cn(
                    "transition-colors duration-200",
                    isCollapsed ? "h-7 w-7" : "h-6 w-6",
                    isActive
                      ? "text-primary"
                      : "text-muted-foreground group-hover:text-accent-foreground",
                  )}
                />
                {!isCollapsed && (
                  <>
                    <div className="ml-3 min-w-0 flex-1">
                      <div className="font-medium truncate">{item.name}</div>
                    </div>
                    <button
                      data-chevron
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggle(item.id);
                      }}
                      className="ml-2 p-1.5 rounded-md hover:bg-accent/50 transition-colors duration-200"
                    >
                      {itemCollapsed ? (
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </button>
                  </>
                )}
              </Link>
            ) : (
              <div
                className={cn(
                  "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-default group h-10",
                  isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
                  isActive
                    ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
                )}
              >
                <item.icon
                  className={cn(
                    "transition-colors duration-200",
                    isCollapsed ? "h-7 w-7" : "h-6 w-6",
                    isActive
                      ? "text-primary"
                      : "text-muted-foreground group-hover:text-accent-foreground",
                  )}
                />
                {!isCollapsed && (
                  <>
                    <div className="ml-3 min-w-0 flex-1">
                      <div className="font-medium truncate">{item.name}</div>
                    </div>
                    <button
                      data-chevron
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggle(item.id);
                      }}
                      className="ml-2 p-1.5 rounded-md hover:bg-accent/50 transition-colors duration-200"
                    >
                      {itemCollapsed ? (
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </button>
                  </>
                )}
              </div>
            )}
          </div>

          {!isCollapsed && !itemCollapsed && (
            <div className="ml-4 space-y-1">
              {item.children.map((child) =>
                child.path ? (
                  <Link
                    key={child.id}
                    href={child.path}
                    className={cn(
                      "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group px-3 py-2 h-9",
                      isChildRouteActive(child.path)
                        ? "bg-accent/60 text-accent-foreground border border-accent/50 shadow-sm"
                        : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground border border-transparent",
                    )}
                  >
                    <child.icon
                      className={cn(
                        "h-4 w-4 transition-colors duration-200",
                        isChildRouteActive(child.path)
                          ? "text-primary"
                          : "text-muted-foreground group-hover:text-accent-foreground",
                      )}
                    />
                    <div className="ml-3 min-w-0 flex-1">
                      <div className="font-medium truncate">{child.name}</div>
                    </div>
                  </Link>
                ) : null,
              )}
            </div>
          )}
        </div>
      );
    }

    if (!item.path) return null;

    return (
      <Link
        key={item.id}
        href={item.path}
        className={cn(
          "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group h-10",
          isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
          isActive
            ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
        )}
      >
        <item.icon
          className={cn(
            "transition-colors duration-200",
            isCollapsed ? "h-7 w-7" : "h-6 w-6",
            isActive
              ? "text-primary"
              : "text-muted-foreground group-hover:text-accent-foreground",
          )}
        />
        {!isCollapsed && (
          <div className="ml-3 min-w-0 flex-1">
            <div className="font-medium truncate">{item.name}</div>
          </div>
        )}
      </Link>
    );
  }

  return (
    <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto">
      {items.map(renderItem)}
    </nav>
  );
}
