"use client";

import React from "react";
import { SidebarLayoutProps } from "@/types/sidebar";
import { SidebarHeader } from "./sidebar-header";
import { SidebarFooter } from "./sidebar-footer";
import { useOrganization } from "@/providers/organization-provider";
import { buildOrgRoute } from "@/lib/routes";
import { useSidebar } from "@/providers/sidebar-context";
import { cn } from "@/lib/utils";

export function SidebarLayout({
  layoutType,
  isCollapsed = false,
  isMobile = false,
  onClose,
  showBack,
  onBack,
  children,
  footer,
}: SidebarLayoutProps) {
  const { currentOrganizationId } = useOrganization();
  const { setActiveSidebar } = useSidebar();

  const shouldShowBack = showBack ?? layoutType !== "dashboard";

  const handleBack = () => {
    setActiveSidebar("dashboard");
    if (onBack) return onBack();
    if (currentOrganizationId) {
      window.location.href = buildOrgRoute(currentOrganizationId, "/dashboard");
    }
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-card no-border shadow-sm transition-all duration-300",
        isCollapsed ? "w-14" : "w-64",
      )}
    >
      <SidebarHeader
        isCollapsed={isCollapsed}
        isMobile={isMobile}
        onClose={onClose}
        showBack={shouldShowBack}
        onBack={handleBack}
      />

      {/* Main nav/body */}
      {children}

      {/* Optional footer */}
      <SidebarFooter>{footer}</SidebarFooter>
    </div>
  );
}
