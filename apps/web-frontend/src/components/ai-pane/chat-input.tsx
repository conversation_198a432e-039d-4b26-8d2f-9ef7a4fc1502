"use client";

import React, { useState, useRef, useEffect } from "react";
import { Send } from "lucide-react";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import { cn } from "@/lib/utils";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Ask a question",
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Focus textarea when component mounts
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || disabled) return;

    onSendMessage(message.trim());
    setMessage("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  return (
    <div className="border-t border-border bg-card/50 p-4 safe-area-inset-bottom">
      {/* Loading indicator when disabled */}
      {disabled && (
        <div className="mb-3 flex items-center justify-center text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
            <span>AI is thinking...</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="relative">
        {/* Message input */}
        <div className="flex flex-row items-center relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            placeholder={disabled ? "Please wait..." : placeholder}
            disabled={disabled}
            rows={1}
            className={cn(
              "w-full resize-none rounded-lg border border-input bg-input px-4 py-3 pr-12",
              "text-sm text-foreground placeholder-muted-foreground",
              "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              "min-h-[48px] max-h-[120px] touch-manipulation",
              // Better mobile text input handling
              "text-base sm:text-sm", // Prevent zoom on iOS
            )}
            style={{
              scrollbarWidth: "thin",
            }}
          />

          {/* Send button */}
          <Button
            type="submit"
            size="icon"
            disabled={!message.trim() || disabled}
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2",
              "h-8 w-8 leading-none",
              "disabled:opacity-50 disabled:cursor-not-allowed",
            )}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        {/* Keyboard shortcuts hint - hidden on mobile */}
        <div className="hidden sm:flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>
            <kbd className="px-1.5 py-0.5 text-xs bg-muted border border-border rounded">
              Enter
            </kbd>{" "}
            to send
          </span>
          <span>
            <kbd className="px-1.5 py-0.5 text-xs bg-muted border border-border rounded">
              Shift
            </kbd>{" "}
            +{" "}
            <kbd className="px-1.5 py-0.5 text-xs bg-muted border border-border rounded">
              Enter
            </kbd>{" "}
            for new line
          </span>
        </div>
      </form>
    </div>
  );
}
