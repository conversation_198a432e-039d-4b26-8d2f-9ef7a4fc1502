"use client";

import React from "react";
import { ChatInput } from "./chat-input";

interface AIPaneChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
}

export function AIPaneChatInput({
  onSendMessage,
  disabled,
}: AIPaneChatInputProps) {
  return (
    <div className="flex-shrink-0">
      <ChatInput onSendMessage={onSendMessage} disabled={disabled} />
    </div>
  );
}
