"use client";

import React, { useEffect, useRef } from "react";
import { ChatMessageComponent } from "./chat-message";
import type { ChatMessage } from "@/hooks/use-ai-assistant";

interface ChatMessagesProps {
  messages: ChatMessage[];
  isStreaming?: boolean;
  onRetry?: () => void;
}

export function ChatMessages({
  messages,
  isStreaming,
  onRetry,
}: ChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive or when streaming
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  }, [messages.length, isStreaming]);

  // Auto-scroll during streaming for better UX
  useEffect(() => {
    if (isStreaming && messagesEndRef.current) {
      const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "end",
        });
      };

      // Scroll immediately and then periodically during streaming
      scrollToBottom();
      const interval = setInterval(scrollToBottom, 500);

      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <div className="text-lg font-medium mb-2">No messages yet</div>
          <div className="text-sm">
            Start a conversation by typing a message below
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto"
      style={{
        scrollbarWidth: "thin",
        scrollbarColor: "hsl(var(--muted-foreground)) transparent",
      }}
    >
      <div className="space-y-1">
        {messages.map((message) => (
          <ChatMessageComponent
            key={message.id}
            message={message}
            onRetry={message.error ? onRetry : undefined}
          />
        ))}

        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} className="h-1" />
      </div>
    </div>
  );
}
