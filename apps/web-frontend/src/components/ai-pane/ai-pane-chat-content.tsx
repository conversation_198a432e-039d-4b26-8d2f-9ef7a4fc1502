"use client";

import React from "react";
import { ChatWelcome } from "./chat-welcome";
import { ChatMessages } from "./chat-messages";
import type { ChatMessage } from "@/hooks/use-ai-assistant";

interface AIPaneChatContentProps {
  messages: ChatMessage[];
  isStreaming?: boolean;
  onSuggestedQuestion: (question: string) => void;
  onRetry?: () => void;
}

export function AIPaneChatContent({
  messages,
  isStreaming,
  onSuggestedQuestion,
  onRetry,
}: AIPaneChatContentProps) {
  const hasMessages = messages.length > 0;

  return (
    <div
      className="flex flex-col min-h-0"
      style={{ height: "calc(100vh - 160px)" }}
    >
      {!hasMessages ? (
        <div className="flex-1 overflow-y-auto">
          <ChatWelcome onSuggestedQuestion={onSuggestedQuestion} />
        </div>
      ) : (
        <ChatMessages
          messages={messages}
          isStreaming={isStreaming}
          onRetry={onRetry}
        />
      )}
    </div>
  );
}
