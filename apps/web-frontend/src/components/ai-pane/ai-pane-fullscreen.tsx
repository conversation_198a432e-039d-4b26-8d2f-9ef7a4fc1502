"use client";

import React, { useEffect, useState } from "react";
import { PanelLeft, PanelRight, AlertCircle } from "lucide-react";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@askinfosec/shadcn-ui/components/ui/dialog";
import { cn } from "@/lib/utils";
import { useAIPane } from "@/providers/ai-pane-provider";
import { useAIAssistantContext } from "@/providers/ai-assistant-provider";
import { AIPaneHeader } from "./ai-pane-header";
import { AIPaneChatContent } from "./ai-pane-chat-content";
import { AIPaneChatInput } from "./ai-pane-chat-input";
import { ChatHistorySidebar } from "./chat-history-sidebar";

interface AIPaneFullscreenProps {
  className?: string;
}

export function AIPaneFullscreen({ className }: AIPaneFullscreenProps) {
  const { mode, close, openSidebar } = useAIPane();
  const isOpen = mode === "fullscreen";
  const [showHistory, setShowHistory] = useState(true);

  // Use the AI assistant hook
  const {
    messages,
    isStreaming,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    retryLastMessage,
  } = useAIAssistantContext();

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        close();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, close]);

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error("Error sending message:", error);
      // Error handling is managed by the hook
    }
  };

  const handleSuggestedQuestion = (question: string) => {
    handleSendMessage(question);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && close()}>
      <DialogContent
        className={cn(
          "max-w-none w-screen h-screen p-0 gap-0 rounded-none border-0 flex flex-col",
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          className,
        )}
        showCloseButton={false}
      >
        <DialogTitle className="sr-only">AI Assistant</DialogTitle>

        {/* Main Layout */}
        <div className="relative flex-1 bg-background text-foreground">
          {/* Chat History Sidebar - Positioned absolutely */}
          {showHistory && (
            <div className="absolute left-0 top-0 bottom-0 z-10 h-full">
              <ChatHistorySidebar
                isVisible={true}
                onToggle={() => {
                  console.log(
                    "Chat history toggle clicked, current state:",
                    showHistory,
                  );
                  setShowHistory(!showHistory);
                }}
                showCloseButton={true}
              />
            </div>
          )}

          {/* Main Chat Area - Takes full width, adjusts margin when sidebar is visible */}
          <div
            className={cn(
              "flex-1 flex flex-col bg-background transition-all duration-300 min-h-0",
              showHistory ? "ml-64 sm:ml-64 md:ml-72 lg:ml-80" : "ml-0",
            )}
          >
            {/* Header */}
            <AIPaneHeader
              onClose={close}
              onToggle={openSidebar}
              onClear={() => clearMessages()}
              isFullscreen={true}
              className="px-4 py-2 border-b border-border bg-card/50"
              leftElement={
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowHistory(!showHistory)}
                  className="h-8 w-8 text-muted-foreground hover:text-foreground hover:bg-accent"
                >
                  {showHistory ? (
                    <PanelRight className="h-4 w-4" />
                  ) : (
                    <PanelLeft className="h-4 w-4" />
                  )}
                </Button>
              }
            />

            {/* Global Error Display */}
            {error && (
              <div className="mx-4 mb-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-destructive font-medium">
                      Connection Error
                    </p>
                    <p className="text-xs text-destructive/80 mt-1">{error}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={retryLastMessage}
                    className="h-6 px-2 text-xs text-destructive hover:text-destructive"
                  >
                    Retry
                  </Button>
                </div>
              </div>
            )}

            {/* Chat Content */}
            <AIPaneChatContent
              messages={messages}
              isStreaming={isStreaming}
              onSuggestedQuestion={handleSuggestedQuestion}
              onRetry={retryLastMessage}
            />

            {/* Chat Input */}
            <AIPaneChatInput
              onSendMessage={handleSendMessage}
              disabled={isLoading || isStreaming}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
