"use server";

import {
  AgentInvokeConfig,
  AgentStreamingService,
  AgentSource,
  AgentMetadata,
} from "@/lib/ai/ai-agent-service";
import {
  buildAgentInvokeRequest,
  buildCorrelationContext,
} from "@/lib/ai/ai-agent-shared";

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface StreamResult {
  content: string;
  sources?: AgentSource[];
  metadata?: AgentMetadata;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function generateId(): string {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

function getConfig(): AgentInvokeConfig {
  const baseUrl = process.env.ANTER_API_URL;
  const internalSecret = process.env.INTERNAL_SECRET;
  const timeout = parseInt(process.env.ANTER_API_TIMEOUT || "30000", 10);
  const retries = parseInt(process.env.ANTER_API_RETRIES || "3", 10);
  const sslVerify = process.env.ANTER_SSL_VERIFY !== "false";

  if (!baseUrl) {
    throw new Error("ANTER_API_URL environment variable is required");
  }

  if (!internalSecret) {
    throw new Error("INTERNAL_SECRET environment variable is required");
  }

  return {
    baseUrl,
    internalSecret,
    timeout,
    retries,
    sslVerify,
  };
}

// Build request and context using shared helpers to keep parity with client streaming

// ============================================================================
// MAIN SERVER ACTION
// ============================================================================

export async function sendMessageUsingStream(
  message: string,
): Promise<StreamResult> {
  if (!message.trim()) {
    throw new Error("Message cannot be empty");
  }

  // Generate session for this request (replace with real session id later)
  const sessionId = generateId();

  try {
    // Get configuration from environment variables
    const config = getConfig();

    // Create service instance
    const service = new AgentStreamingService(config);

    // Create request and correlation context using shared helpers
    const request = buildAgentInvokeRequest(
      message.trim(),
      sessionId,
      "default-org",
      "default-user",
    );
    const correlationContext = buildCorrelationContext(sessionId);

    let fullContent = "";
    let lastSources: AgentSource[] | undefined;
    let lastMetadata: AgentMetadata | undefined;

    // Start streaming and aggregate content on the server
    await service.invokeAgentStream(request, correlationContext, (chunk) => {
      if (chunk.content) fullContent += chunk.content;
      if (chunk.sources) lastSources = chunk.sources;
      if (chunk.metadata) lastMetadata = chunk.metadata;
    });

    return {
      content: fullContent,
      sources: lastSources,
      metadata: lastMetadata,
    };
  } catch (error) {
    console.error("Error in sendMessageUsingStream:", error);

    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`AI Assistant Error: ${error.message}`);
    } else {
      throw new Error(
        "An unexpected error occurred while communicating with the AI assistant",
      );
    }
  }
}
