export interface OrganizationLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  mobileMenu?: React.ReactNode;
  headerTitle?: string;
  headerDescription?: string;
  _headerActions?: React.ReactNode;
  onMobileMenuToggle?: () => void;
  onSidebarToggle?: () => void;
  mobileMenuOpen?: boolean;
}

export interface OrganizationSidebarProps {
  isCollapsed: boolean;
  onClose?: () => void;
  isMobile?: boolean;
  currentPath?: string;
  onBackToDashboard: () => void;
}

export type Organization = {
  id: string;
  companyName: string;
  stripeCustomerId: string | null;
};
