import type { LucideIcon } from "lucide-react";

export interface SidebarNavItem {
  id: string;
  name: string;
  path?: string;
  icon: LucideIcon;
  children?: SidebarNavItem[];
}

export interface SidebarLayoutProps {
  layoutType: "dashboard" | "organization" | string;
  isCollapsed?: boolean;
  isMobile?: boolean;
  onClose?: () => void;

  // Header
  showBack?: boolean;
  onBack?: () => void;

  // Slots
  children: React.ReactNode; // main nav content
  footer?: React.ReactNode; // optional bottom area
}
