"use client";

import { useState, useCallback, useRef } from "react";
import type { AgentSource, AgentMetadata } from "@/lib/ai/ai-agent-service";

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
  sources?: AgentSource[];
  metadata?: AgentMetadata;
  isStreaming?: boolean;
  error?: string;
}

export interface StreamingState {
  isStreaming: boolean;
  currentMessageId?: string;
  error?: string;
}

export interface AIAssistantState {
  messages: ChatMessage[];
  streamingState: StreamingState;
  isLoading: boolean;
}

export interface UseAIAssistantReturn {
  // State
  messages: ChatMessage[];
  isStreaming: boolean;
  isLoading: boolean;
  error?: string;

  // Actions
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  retryLastMessage: () => Promise<void>;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

function createUserMessage(content: string): ChatMessage {
  return {
    id: generateMessageId(),
    content,
    role: "user",
    timestamp: new Date(),
  };
}

function createAssistantMessage(id?: string): ChatMessage {
  return {
    id: id || generateMessageId(),
    content: "",
    role: "assistant",
    timestamp: new Date(),
    isStreaming: true,
  };
}

// ============================================================================
// MAIN HOOK
// ============================================================================

export function useAIAssistant(
  organizationIdArg?: string,
): UseAIAssistantReturn {
  const [state, setState] = useState<AIAssistantState>({
    messages: [],
    streamingState: {
      isStreaming: false,
    },
    isLoading: false,
  });

  const abortControllerRef = useRef<globalThis.AbortController | null>(null);
  const lastUserMessageRef = useRef<string>("");
  const sessionIdRef = useRef<string | null>(null);
  // Stream through Next.js API proxy that adds INTERNAL_SECRET server-side

  if (sessionIdRef.current === null && typeof window !== "undefined") {
    try {
      const key = "ai_assistant_session_id";
      const existing = window.localStorage.getItem(key);
      if (existing) {
        sessionIdRef.current = existing;
      } else {
        const newId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        window.localStorage.setItem(key, newId);
        sessionIdRef.current = newId;
      }
    } catch {
      // localStorage may be unavailable; generate ephemeral session id
      sessionIdRef.current = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
  }

  const clearMessages = useCallback(() => {
    // Cancel any ongoing streaming
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setState({
      messages: [],
      streamingState: {
        isStreaming: false,
      },
      isLoading: false,
    });
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    // Cancel any ongoing streaming
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new globalThis.AbortController();
    lastUserMessageRef.current = message.trim();

    // Add user message
    const userMessage = createUserMessage(message.trim());
    const assistantMessage = createAssistantMessage();

    setState((prev) => ({
      ...prev,
      messages: [...prev.messages, userMessage, assistantMessage],
      streamingState: {
        isStreaming: true,
        currentMessageId: assistantMessage.id,
        error: undefined,
      },
      isLoading: true,
    }));

    try {
      const organizationId = organizationIdArg || "default-org";
      const controller = abortControllerRef.current;
      const response = await fetch("/api/ai/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
        },
        body: JSON.stringify({
          message: message.trim(),
          sessionId: sessionIdRef.current || undefined,
          organizationId,
        }),
        signal: controller?.signal,
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => "");
        throw new Error(errorText || response.statusText);
      }

      const reader = response.body?.getReader();
      const decoder = new globalThis.TextDecoder();
      if (!reader) throw new Error("No response body reader available");

      let buffer = "";
      try {
        // Read SSE stream
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (!line.startsWith("data:")) continue;
            const raw = line.slice(5).trimStart();
            if (!raw) continue;
            let parsed: {
              content?: string;
              isComplete?: boolean;
              error?: string;
              sources?: AgentSource[];
              metadata?: AgentMetadata;
            };
            try {
              parsed = JSON.parse(raw);
            } catch {
              continue;
            }

            if (parsed.error) {
              throw new Error(parsed.error);
            }

            const isComplete = Boolean(parsed.isComplete);
            const contentChunk = parsed.content || "";

            setState((prev) => {
              const updatedMessages = prev.messages.map((msg) => {
                if (msg.id === assistantMessage.id) {
                  const nextContent = contentChunk
                    ? (msg.content || "") + contentChunk
                    : msg.content;
                  return {
                    ...msg,
                    content: nextContent || "",
                    sources: msg.sources,
                    isStreaming: !isComplete,
                  };
                }
                return msg;
              });

              return {
                ...prev,
                messages: updatedMessages,
                streamingState: {
                  isStreaming: isComplete ? false : true,
                  currentMessageId: isComplete
                    ? undefined
                    : assistantMessage.id,
                  error: undefined,
                },
                isLoading: isComplete ? false : true,
              };
            });
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Error sending message:", error);

      // Handle abort error (user cancelled)
      if (error instanceof Error && error.name === "AbortError") {
        setState((prev) => ({
          ...prev,
          streamingState: {
            isStreaming: false,
            currentMessageId: undefined,
            error: undefined,
          },
          isLoading: false,
        }));
        return;
      }

      // Handle other errors
      const errorMessage =
        error instanceof Error ? error.message : "An unexpected error occurred";

      setState((prev) => {
        const updatedMessages = prev.messages.map((msg) => {
          if (msg.id === assistantMessage.id) {
            return {
              ...msg,
              content:
                msg.content ||
                "Sorry, I encountered an error while processing your request.",
              error: errorMessage,
              isStreaming: false,
            };
          }
          return msg;
        });

        return {
          ...prev,
          messages: updatedMessages,
          streamingState: {
            isStreaming: false,
            currentMessageId: undefined,
            error: errorMessage,
          },
          isLoading: false,
        };
      });
    } finally {
      // Cleanup and final state guard
      setState((prev) => ({
        ...prev,
        streamingState: {
          isStreaming: false,
          currentMessageId: undefined,
          error: prev.streamingState.error,
        },
        isLoading: false,
      }));
      abortControllerRef.current = null;
    }
  }, []);

  const retryLastMessage = useCallback(async () => {
    if (!lastUserMessageRef.current) return;

    // Remove the last assistant message if it has an error
    setState((prev) => {
      const lastMessage = prev.messages[prev.messages.length - 1];
      if (lastMessage?.role === "assistant" && lastMessage.error) {
        return {
          ...prev,
          messages: prev.messages.slice(0, -1),
        };
      }
      return prev;
    });

    // Retry with the last user message
    await sendMessage(lastUserMessageRef.current);
  }, [sendMessage]);

  return {
    messages: state.messages,
    isStreaming: state.streamingState.isStreaming,
    isLoading: state.isLoading,
    error: state.streamingState.error,
    sendMessage,
    clearMessages,
    retryLastMessage,
  };
}
