"use client";

import React, { createContext, useContext } from "react";
import { useAIAssistant } from "@/hooks/use-ai-assistant";
import { useOrganization } from "@/providers/organization-provider";

interface AIAssistantContextValue {
  messages: ReturnType<typeof useAIAssistant>["messages"];
  isStreaming: boolean;
  isLoading: boolean;
  error?: string;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  retryLastMessage: () => Promise<void>;
}

const AIAssistantContext = createContext<AIAssistantContextValue | undefined>(
  undefined,
);

export function AIAssistantProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { currentOrganizationId } = (() => {
    try {
      return useOrganization();
    } catch {
      return { currentOrganizationId: undefined } as {
        currentOrganizationId?: string;
      };
    }
  })();
  const assistant = useAIAssistant(currentOrganizationId ?? undefined);

  return (
    <AIAssistantContext.Provider value={assistant}>
      {children}
    </AIAssistantContext.Provider>
  );
}

export function useAIAssistantContext(): AIAssistantContextValue {
  const ctx = useContext(AIAssistantContext);
  if (!ctx) {
    throw new Error(
      "useAIAssistantContext must be used within an AIAssistantProvider",
    );
  }
  return ctx;
}
