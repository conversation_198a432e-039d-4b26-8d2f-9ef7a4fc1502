import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET() {
  const baseUrl = process.env.ANTER_API_URL;
  if (!baseUrl) {
    return NextResponse.json(
      { error: "ANTER_API_URL is not configured on the server" },
      { status: 500 },
    );
  }

  return NextResponse.json(
    { baseUrl },
    {
      status: 200,
      headers: {
        "Cache-Control":
          "no-store, no-cache, must-revalidate, proxy-revalidate",
      },
    },
  );
}
