import { NextResponse, NextRequest } from "next/server";
import { ReadableStream } from "node:stream/web";
import { TextEncoder } from "node:util";
import {
  AgentStreamingService,
  type AgentInvokeConfig,
} from "@/lib/ai/ai-agent-service";
import {
  buildAgentInvokeRequest,
  buildCorrelationContext,
} from "@/lib/ai/ai-agent-shared";

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const { message, sessionId, organizationId, projectId } =
      (await request.json()) as {
        message: string;
        sessionId?: string;
        organizationId: string;
        projectId?: string;
      };

    if (!message || !organizationId) {
      return NextResponse.json(
        { error: "Missing required fields: message, organizationId" },
        { status: 400 },
      );
    }

    const baseUrl = process.env.ANTER_API_URL;
    const internalSecret = process.env.INTERNAL_SECRET;
    const timeout = parseInt(process.env.ANTER_API_TIMEOUT || "30000", 10);
    const retries = parseInt(process.env.ANTER_API_RETRIES || "3", 10);
    const sslVerify = process.env.ANTER_SSL_VERIFY !== "false";

    if (!baseUrl || !internalSecret) {
      return NextResponse.json(
        {
          error:
            "Server configuration missing ANTER_API_URL or INTERNAL_SECRET",
        },
        { status: 500 },
      );
    }

    const config: AgentInvokeConfig = {
      baseUrl,
      internalSecret,
      timeout,
      retries,
      sslVerify,
    };

    const svc = new AgentStreamingService(config);

    const sid =
      sessionId ||
      `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const agentReq = buildAgentInvokeRequest(
      message.trim(),
      sid,
      organizationId,
      "web-user",
      projectId,
    );
    const correlation = buildCorrelationContext(sid);

    const encoder = new TextEncoder();

    const stream: ReadableStream<Uint8Array> = new ReadableStream({
      start(controller) {
        (async () => {
          try {
            await svc.invokeAgentStream(agentReq, correlation, (chunk) => {
              const payload = JSON.stringify({
                content: chunk.content || "",
                isComplete: Boolean(chunk.isComplete),
                sources: chunk.sources,
                metadata: chunk.metadata,
              });
              controller.enqueue(encoder.encode(`data: ${payload}\n\n`));
            });

            // Final event (in case the upstream didn't send an explicit completion flag)
            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ isComplete: true })}\n\n`,
              ),
            );
          } catch (e) {
            const msg = e instanceof Error ? e.message : String(e);
            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({ error: msg, isComplete: true })}\n\n`,
              ),
            );
          } finally {
            controller.close();
          }
        })();
      },
      cancel() {
        // Nothing to cancel on server side for now
      },
    });

    return new Response(stream as unknown as any, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-store",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    const msg = error instanceof Error ? error.message : String(error);
    return NextResponse.json({ error: msg }, { status: 500 });
  }
}
