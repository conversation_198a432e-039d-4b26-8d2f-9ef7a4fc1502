import React from "react";
import { Metada<PERSON> } from "next";
import { PageHeader } from "@/components/layout/page-header";

export const metadata: Metadata = {
  title: "AI",
  description: "Manage your organization's AI",
};

interface AISettingsPageProps {
  params: Promise<{ organizationId: string }>;
}
export default async function AISettingsPage({ params }: AISettingsPageProps) {
  const { organizationId } = await params;
  return (
    <>
      <PageHeader
        title="AI"
        description="Manage your organization's AI settings"
      />
      <React.Suspense fallback={"Loading..." + organizationId}></React.Suspense>
    </>
  );
}
