"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import {
  DashboardLayout,
  DashboardSidebar,
  SidebarMobileMenu,
  UserProfileDropdown,
} from "@/components/layout";
import {
  OrganizationLayout,
  OrganizationSidebar,
} from "@/components/layout/organization";
import { useSession } from "@/providers/session-provider";
import { useOrganization } from "@/providers/organization-provider";
import { useSidebar } from "@/providers/sidebar-context";
import { logout } from "@/lib/auth-client";
import { useRouter, useParams, usePathname } from "next/navigation";
import { usePageMetadata } from "@/providers/page-metadata-context";
import { AIPane } from "@/components/ai-pane";
import { useOrgRoutes } from "@/hooks/use-org-routes";
import { useRBAC } from "@/providers/rbac-provider";
import { cn } from "@/lib/utils";

export function ProtectedLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { session, isLoading: sessionLoading } = useSession();
  const {
    currentOrganizationId,
    organizations,
    isLoading: organizationLoading,
    hasOrganization,
  } = useOrganization();
  const { metadata } = usePageMetadata();
  const {
    activeSidebar,
    sidebarCollapsed,
    setSidebarCollapsed,
    setActiveSidebar,
  } = useSidebar();
  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();

  // Hooks that must be called unconditionally (used by dashboard branch)
  const { orgRoutes, orgSettingsRoute, isRouteActive } = useOrgRoutes();
  const { userRole } = useRBAC();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Memoize the URL organization ID to prevent unnecessary re-renders
  const urlOrgId = useMemo(
    () => params?.organizationId as string,
    [params?.organizationId],
  );

  // Memoize the user object to prevent unnecessary re-renders
  const user = useMemo(() => {
    if (!session?.user) return null;
    return {
      id: session.user.id,
      name: session.user.name || session.user.email || "User",
      email: session.user.email || "",
    };
  }, [session?.user]);

  // Ensure user object is stable and never null when session exists
  const stableUser = useMemo(() => {
    if (sessionLoading || !mounted) return null;
    if (!session?.user) return null;
    return user;
  }, [sessionLoading, mounted, session?.user, user]);

  // Memoize the redirect logic to prevent unnecessary re-renders
  const shouldRedirectToNoOrg = useMemo(() => {
    return (
      !organizationLoading &&
      organizations.length === 0 &&
      !hasOrganization &&
      session && // User is authenticated
      mounted // Component is mounted
    );
  }, [
    organizationLoading,
    organizations.length,
    hasOrganization,
    session,
    mounted,
  ]);

  const shouldRedirectToCorrectOrg = useMemo(() => {
    return (
      !organizationLoading &&
      hasOrganization &&
      currentOrganizationId &&
      session && // User is authenticated
      mounted && // Component is mounted
      urlOrgId !== currentOrganizationId // URL doesn't match current organization ID
    );
  }, [
    organizationLoading,
    hasOrganization,
    currentOrganizationId,
    session,
    mounted,
    urlOrgId,
  ]);

  // Handle redirect to no-organization page when user has no organizations
  useEffect(() => {
    if (shouldRedirectToNoOrg) {
      console.debug(
        "[ProtectedLayout] No organizations found, redirecting to no-organization page",
      );
      router.push("/no-organization");
    }
  }, [shouldRedirectToNoOrg, router]);

  // Handle redirect to correct organization route when user has organizations but is on wrong URL
  useEffect(() => {
    if (shouldRedirectToCorrectOrg) {
      console.debug(
        `[ProtectedLayout] URL organization ID (${urlOrgId}) doesn't match current organization ID (${currentOrganizationId}), redirecting to correct route`,
      );

      // Extract the current path after the organization ID
      const pathSegments = pathname.split("/");
      const currentPath = pathSegments.slice(2).join("/"); // Remove empty string and organizationId

      // Redirect to the correct organization route
      const correctPath = `/${currentOrganizationId}/${currentPath}`;
      router.push(correctPath);
    }
  }, [
    shouldRedirectToCorrectOrg,
    urlOrgId,
    currentOrganizationId,
    pathname,
    router,
  ]);

  const handleLogout = useCallback(async () => {
    try {
      await logout();
      router.push("/sign-in");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  }, [router]);

  const handleMobileMenuToggle = useCallback(() => {
    setMobileMenuOpen(true);
  }, []);

  const handleMobileMenuClose = useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  const handleSidebarToggle = useCallback(() => {
    setSidebarCollapsed(!sidebarCollapsed);
  }, [sidebarCollapsed, setSidebarCollapsed]);

  const handleBackToDashboard = useCallback(() => {
    setActiveSidebar("dashboard");
  }, [setActiveSidebar]);

  // Show loading only if session is still loading or component not mounted
  // Don't show loading for organization loading as we want to render the layout
  if (sessionLoading || !mounted) {
    return (
      <div className="flex h-screen bg-muted">
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <div className="text-muted-foreground">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  // Render dashboard layout
  if (activeSidebar === "dashboard") {
    const mobileMenuFooter =
      (userRole === "owner" || userRole === "admin") && orgSettingsRoute ? (
        <button
          onClick={() => {
            setActiveSidebar("organization");
            if (currentOrganizationId) {
              router.push(`/${currentOrganizationId}/settings/general`);
            }
          }}
          className={cn(
            "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer h-10 px-3 py-2",
            isRouteActive(orgSettingsRoute.path)
              ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
              : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
          )}
        >
          <orgSettingsRoute.icon className="h-6 w-6" />
          <span className="ml-3">{orgSettingsRoute.name}</span>
        </button>
      ) : null;
    return (
      <>
        <DashboardLayout
          sidebar={
            <DashboardSidebar
              isCollapsed={sidebarCollapsed}
              currentPath={currentOrganizationId || ""}
            />
          }
          mobileMenu={
            <SidebarMobileMenu
              isOpen={mobileMenuOpen}
              onClose={handleMobileMenuClose}
              layoutType="dashboard"
              items={orgRoutes as any}
              footer={mobileMenuFooter}
              currentPath={currentOrganizationId || ""}
            />
          }
          _headerActions={
            <UserProfileDropdown user={stableUser} onSignOut={handleLogout} />
          }
          onMobileMenuToggle={handleMobileMenuToggle}
          onSidebarToggle={handleSidebarToggle}
          mobileMenuOpen={mobileMenuOpen}
          headerTitle={metadata.title}
          headerDescription={metadata.description}
        >
          {children}
        </DashboardLayout>
        <AIPane />
      </>
    );
  }

  // Render organization layout
  return (
    <>
      <OrganizationLayout
        sidebar={
          <OrganizationSidebar
            isCollapsed={sidebarCollapsed}
            currentPath={currentOrganizationId || ""}
            onBackToDashboard={handleBackToDashboard}
          />
        }
        mobileMenu={null}
        _headerActions={
          <UserProfileDropdown user={stableUser} onSignOut={handleLogout} />
        }
        onMobileMenuToggle={handleMobileMenuToggle}
        onSidebarToggle={handleSidebarToggle}
        mobileMenuOpen={mobileMenuOpen}
        headerTitle={metadata.title}
        headerDescription={metadata.description}
      >
        {children}
      </OrganizationLayout>
      <AIPane />
    </>
  );
}
