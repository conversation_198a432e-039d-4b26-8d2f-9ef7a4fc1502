"use client";

import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@askinfosec/shadcn-ui/components/ui/card";
import { But<PERSON> } from "@askinfosec/shadcn-ui/components/ui/button";
import { AIPaneProvider, useAIPane } from "@/providers/ai-pane-provider";
import { AIPane } from "@/components/ai-pane";

function AIAssistantDemoContent() {
  const { openSidebar, openFullscreen, close, mode } = useAIPane();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>AI Assistant Integration Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Test the AI Assistant chat functionality with streaming responses
            from the external AI Agent API.
          </div>

          <div className="flex gap-2">
            <Button onClick={openSidebar} variant="default">
              Open AI Assistant (Sidebar)
            </Button>
            <Button onClick={openFullscreen} variant="outline">
              Open AI Assistant (Fullscreen)
            </Button>
            <Button
              onClick={close}
              variant="ghost"
              disabled={mode === "closed"}
            >
              Close AI Assistant
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            Current mode: <span className="font-mono">{mode}</span>
          </div>

          <div className="p-4 bg-muted rounded-lg text-sm">
            <div className="font-medium mb-2">Test Instructions:</div>
            <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
              <li>
                Click "Open AI Assistant (Sidebar)" to open the chat interface
              </li>
              <li>Try typing a message and pressing Enter</li>
              <li>Verify that the message appears in the chat</li>
              <li>
                Check that the AI response streams back from the external API
              </li>
              <li>Test error handling by disconnecting from the API</li>
              <li>Test the retry functionality when errors occur</li>
            </ol>
          </div>

          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
            <div className="font-medium text-yellow-800 mb-2">
              Prerequisites:
            </div>
            <ul className="list-disc list-inside space-y-1 text-yellow-700">
              <li>
                External AI Agent API should be running at{" "}
                <code>https://localhost:8000</code>
              </li>
              <li>Environment variables should be properly configured</li>
              <li>SSL verification is disabled for local development</li>
            </ul>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm">
            <div className="font-medium text-blue-800 mb-2">
              Expected Behavior:
            </div>
            <ul className="list-disc list-inside space-y-1 text-blue-700">
              <li>Messages should appear immediately when sent</li>
              <li>AI responses should stream in real-time</li>
              <li>Loading states should be visible during processing</li>
              <li>Error messages should appear if the API is unavailable</li>
              <li>Retry functionality should work for failed requests</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* AI Pane Component */}
      <AIPane />
    </div>
  );
}

export function AIAssistantDemo() {
  return (
    <AIPaneProvider>
      <AIAssistantDemoContent />
    </AIPaneProvider>
  );
}
