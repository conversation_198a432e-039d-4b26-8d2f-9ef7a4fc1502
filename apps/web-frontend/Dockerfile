FROM node:22-alpine AS builder

# Install pnpm globally
RUN npm install -g pnpm

WORKDIR /app

# Copy package files and source code
# Ignore files using .dockerignore
COPY . .

# Install dependencies
RUN pnpm install --frozen-lockfile

# Build the application
RUN pnpm build:packages \
    && pnpm --filter @askinfosec/web-frontend build




FROM node:22-alpine AS production

# Install pnpm globally
RUN npm install -g pnpm

WORKDIR /app

# Copy package files
COPY --from=builder /app/package.json .
COPY --from=builder /app/pnpm-lock.yaml .
COPY --from=builder /app/pnpm-workspace.yaml .
COPY --from=builder /app/apps/web-frontend/package.json ./apps/web-frontend/
COPY --from=builder /app/apps/web-frontend/next.config.ts ./apps/web-frontend/
COPY --from=builder /app/packages/types/package.json ./packages/types/
COPY --from=builder /app/packages/shared/package.json ./packages/shared/
COPY --from=builder /app/packages/shadcn-ui/package.json ./packages/shadcn-ui/

# Copy built files
COPY --from=builder /app/apps/web-frontend/.next ./apps/web-frontend/.next
COPY --from=builder /app/packages/types/dist ./packages/types/dist
COPY --from=builder /app/packages/shared/dist ./packages/shared/dist
COPY --from=builder /app/packages/shadcn-ui/dist ./packages/shadcn-ui/dist

# Install dependencies
RUN pnpm install --frozen-lockfile --production

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nonroot -u 1001

# Change ownership of the app directory
RUN chown -R nonroot:nodejs /app
USER nonroot

# Expose port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Start the application
WORKDIR /app/apps/web-frontend
CMD ["pnpm", "start"]
# CMD ["/bin/sh"]
